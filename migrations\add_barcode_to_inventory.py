#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة حقل الباركود إلى جدول المخزون
=====================================

هذا الملف يحتوي على migration لإضافة حقل الباركود إلى جدول المخزون الموجود
مع إنشاء فهرس فريد للباركود وفهرس مركب للباركود والاسم

المؤلف: نظام إدارة الأعمال
التاريخ: 2025-07-30
"""

import sqlite3
import os
import sys
from datetime import datetime

def add_barcode_column():
    """إضافة حقل الباركود إلى جدول المخزون"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'accounting.db')
    
    if not os.path.exists(db_path):
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 بدء إضافة حقل الباركود إلى جدول المخزون...")
        
        # التحقق من وجود الحقل مسبقاً
        cursor.execute("PRAGMA table_info(inventory)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'barcode' in columns:
            print("✅ حقل الباركود موجود مسبقاً في جدول المخزون")
            conn.close()
            return True
        
        # إضافة حقل الباركود
        cursor.execute("""
            ALTER TABLE inventory 
            ADD COLUMN barcode TEXT
        """)
        print("✅ تم إضافة حقل الباركود بنجاح")
        
        # إنشاء فهرس فريد للباركود
        try:
            cursor.execute("""
                CREATE UNIQUE INDEX idx_inventory_barcode_unique 
                ON inventory(barcode) 
                WHERE barcode IS NOT NULL
            """)
            print("✅ تم إنشاء فهرس فريد للباركود")
        except sqlite3.Error as e:
            if "already exists" in str(e):
                print("ℹ️ فهرس الباركود الفريد موجود مسبقاً")
            else:
                print(f"⚠️ تحذير: لم يتم إنشاء فهرس الباركود الفريد: {e}")
        
        # إنشاء فهرس مركب للباركود والاسم
        try:
            cursor.execute("""
                CREATE INDEX idx_inventory_barcode_name 
                ON inventory(barcode, name)
            """)
            print("✅ تم إنشاء فهرس مركب للباركود والاسم")
        except sqlite3.Error as e:
            if "already exists" in str(e):
                print("ℹ️ فهرس الباركود والاسم المركب موجود مسبقاً")
            else:
                print(f"⚠️ تحذير: لم يتم إنشاء فهرس الباركود والاسم المركب: {e}")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من نجاح العملية
        cursor.execute("PRAGMA table_info(inventory)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'barcode' in columns:
            print("✅ تم التحقق من إضافة حقل الباركود بنجاح")
            
            # عرض إحصائيات الجدول
            cursor.execute("SELECT COUNT(*) FROM inventory")
            total_items = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM inventory WHERE barcode IS NOT NULL")
            items_with_barcode = cursor.fetchone()[0]
            
            print(f"📊 إحصائيات المخزون:")
            print(f"   • إجمالي العناصر: {total_items}")
            print(f"   • العناصر التي تحتوي على باركود: {items_with_barcode}")
            print(f"   • العناصر بدون باركود: {total_items - items_with_barcode}")
            
            conn.close()
            return True
        else:
            print("❌ فشل في إضافة حقل الباركود")
            conn.close()
            return False
            
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def generate_sample_barcodes():
    """توليد باركود تجريبي للعناصر الموجودة"""
    
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'accounting.db')
    
    if not os.path.exists(db_path):
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 بدء توليد باركود تجريبي للعناصر...")
        
        # الحصول على العناصر بدون باركود
        cursor.execute("""
            SELECT id, name FROM inventory 
            WHERE barcode IS NULL OR barcode = ''
            LIMIT 10
        """)
        items = cursor.fetchall()
        
        if not items:
            print("ℹ️ جميع العناصر تحتوي على باركود أو لا توجد عناصر")
            conn.close()
            return True
        
        # توليد باركود لكل عنصر
        for item_id, item_name in items:
            # توليد باركود بسيط بناءً على ID
            barcode = f"ITM{item_id:06d}"
            
            try:
                cursor.execute("""
                    UPDATE inventory 
                    SET barcode = ? 
                    WHERE id = ?
                """, (barcode, item_id))
                print(f"✅ تم توليد باركود {barcode} للعنصر: {item_name}")
            except sqlite3.IntegrityError:
                # في حالة تكرار الباركود، جرب باركود آخر
                barcode = f"ITM{item_id:06d}_{datetime.now().strftime('%H%M%S')}"
                cursor.execute("""
                    UPDATE inventory 
                    SET barcode = ? 
                    WHERE id = ?
                """, (barcode, item_id))
                print(f"✅ تم توليد باركود بديل {barcode} للعنصر: {item_name}")
        
        conn.commit()
        
        # عرض الإحصائيات المحدثة
        cursor.execute("SELECT COUNT(*) FROM inventory WHERE barcode IS NOT NULL")
        items_with_barcode = cursor.fetchone()[0]
        
        print(f"📊 تم توليد باركود لـ {len(items)} عنصر")
        print(f"📊 إجمالي العناصر التي تحتوي على باركود: {items_with_barcode}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في توليد الباركود التجريبي: {e}")
        return False

def main():
    """الدالة الرئيسية لتشغيل Migration"""
    print("🚀 بدء migration إضافة الباركود إلى المخزون...")
    print("=" * 50)
    
    # إضافة حقل الباركود
    if add_barcode_column():
        print("✅ تم إضافة حقل الباركود بنجاح")
        
        # توليد باركود تجريبي
        if generate_sample_barcodes():
            print("✅ تم توليد باركود تجريبي بنجاح")
        else:
            print("⚠️ فشل في توليد باركود تجريبي")
    else:
        print("❌ فشل في إضافة حقل الباركود")
        return False
    
    print("=" * 50)
    print("🎉 تم إكمال migration بنجاح!")
    print("💡 يمكنك الآن استخدام نظام الباركود في المخزون")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
