# -*- coding: utf-8 -*-
"""
إعدادات قاعدة البيانات المتطورة
Advanced Database Configuration
"""

import os
from pathlib import Path
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import QueuePool
import logging

class AdvancedDatabaseConfig:
    """إعدادات قاعدة البيانات المتطورة"""
    
    def __init__(self):
        self.current_db_type = "sqlite"
        self.target_db_type = "postgresql"  # الهدف المتطور
        self.config = self.load_config()
        
    def load_config(self):
        """تحميل إعدادات قواعد البيانات المختلفة"""
        return {
            'sqlite_advanced': {
                'url': 'sqlite:///accounting.db',
                'connect_args': {
                    'check_same_thread': False,
                    'timeout': 60,
                    'cached_statements': 2000,  # زيادة التخزين المؤقت
                },
                'pool_size': 100,  # زيادة حجم التجمع
                'max_overflow': 200,  # زيادة الفائض
                'pool_timeout': 60,
                'pool_recycle': 3600,  # إعادة تدوير كل ساعة
                'pool_pre_ping': True,
                'echo': False,
                'isolation_level': 'AUTOCOMMIT'
            },
            
            'postgresql_basic': {
                'url': 'postgresql://postgres:password@localhost:5432/accounting',
                'connect_args': {
                    'connect_timeout': 60,
                    'application_name': 'SmartFinish_Accounting',
                    'options': '-c timezone=UTC'
                },
                'pool_size': 20,
                'max_overflow': 50,
                'pool_timeout': 30,
                'pool_recycle': 7200,
                'pool_pre_ping': True,
                'echo': False
            },
            
            'postgresql_advanced': {
                'url': 'postgresql://postgres:password@localhost:5432/accounting',
                'connect_args': {
                    'connect_timeout': 60,
                    'application_name': 'SmartFinish_Advanced',
                    'options': '-c timezone=UTC -c shared_preload_libraries=pg_stat_statements'
                },
                'pool_size': 50,  # تجمع أكبر
                'max_overflow': 100,
                'pool_timeout': 60,
                'pool_recycle': 3600,
                'pool_pre_ping': True,
                'echo': False,
                'poolclass': QueuePool,
                'pool_reset_on_return': 'commit'
            },
            
            'redis_cache': {
                'host': 'localhost',
                'port': 6379,
                'db': 0,
                'decode_responses': True,
                'socket_timeout': 30,
                'socket_connect_timeout': 30,
                'retry_on_timeout': True,
                'health_check_interval': 30
            }
        }
    
    def create_sqlite_advanced_engine(self):
        """إنشاء محرك SQLite متطور"""
        config = self.config['sqlite_advanced']
        
        engine = create_engine(
            config['url'],
            connect_args=config['connect_args'],
            pool_size=config['pool_size'],
            max_overflow=config['max_overflow'],
            pool_timeout=config['pool_timeout'],
            pool_recycle=config['pool_recycle'],
            pool_pre_ping=config['pool_pre_ping'],
            echo=config['echo'],
            isolation_level=config['isolation_level']
        )
        
        # إعدادات SQLite المتطورة
        @event.listens_for(engine, "connect")
        def set_advanced_sqlite_pragma(dbapi_connection, connection_record):
            """إعدادات SQLite فائقة التطور"""
            cursor = dbapi_connection.cursor()
            
            # إعدادات الأداء الفائق
            cursor.execute("PRAGMA journal_mode=WAL")
            cursor.execute("PRAGMA synchronous=NORMAL")
            cursor.execute("PRAGMA cache_size=-128000")  # 128MB
            cursor.execute("PRAGMA temp_store=MEMORY")
            cursor.execute("PRAGMA mmap_size=536870912")  # 512MB
            cursor.execute("PRAGMA busy_timeout=60000")
            cursor.execute("PRAGMA page_size=4096")
            cursor.execute("PRAGMA wal_autocheckpoint=2000")
            cursor.execute("PRAGMA optimize")
            cursor.execute("PRAGMA threads=8")  # زيادة الخيوط
            
            # إعدادات الذاكرة المتطورة
            cursor.execute("PRAGMA cache_spill=FALSE")
            cursor.execute("PRAGMA query_only=FALSE")
            cursor.execute("PRAGMA read_uncommitted=FALSE")
            cursor.execute("PRAGMA recursive_triggers=TRUE")
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.execute("PRAGMA case_sensitive_like=TRUE")
            cursor.execute("PRAGMA secure_delete=FALSE")
            cursor.execute("PRAGMA auto_vacuum=INCREMENTAL")
            
            # إعدادات جديدة متطورة
            cursor.execute("PRAGMA locking_mode=NORMAL")
            cursor.execute("PRAGMA wal_checkpoint=TRUNCATE")
            cursor.execute("PRAGMA journal_size_limit=67108864")  # 64MB
            cursor.execute("PRAGMA max_page_count=1073741823")  # حد أقصى للصفحات
            
            cursor.close()
        
        return engine
    
    def create_postgresql_engine(self, advanced=True):
        """إنشاء محرك PostgreSQL"""
        config_key = 'postgresql_advanced' if advanced else 'postgresql_basic'
        config = self.config[config_key]
        
        engine = create_engine(
            config['url'],
            connect_args=config['connect_args'],
            pool_size=config['pool_size'],
            max_overflow=config['max_overflow'],
            pool_timeout=config['pool_timeout'],
            pool_recycle=config['pool_recycle'],
            pool_pre_ping=config['pool_pre_ping'],
            echo=config['echo']
        )
        
        if advanced:
            # إعدادات PostgreSQL المتطورة
            @event.listens_for(engine, "connect")
            def set_postgresql_advanced_settings(dbapi_connection, connection_record):
                """إعدادات PostgreSQL متطورة"""
                with dbapi_connection.cursor() as cursor:
                    # إعدادات الأداء
                    cursor.execute("SET work_mem = '256MB'")
                    cursor.execute("SET maintenance_work_mem = '512MB'")
                    cursor.execute("SET shared_buffers = '1GB'")
                    cursor.execute("SET effective_cache_size = '4GB'")
                    cursor.execute("SET random_page_cost = 1.1")
                    cursor.execute("SET seq_page_cost = 1.0")
                    
                    # إعدادات الكتابة
                    cursor.execute("SET wal_buffers = '16MB'")
                    cursor.execute("SET checkpoint_completion_target = 0.9")
                    cursor.execute("SET max_wal_size = '2GB'")
                    cursor.execute("SET min_wal_size = '1GB'")
                    
                    # إعدادات الاستعلامات
                    cursor.execute("SET enable_hashjoin = on")
                    cursor.execute("SET enable_mergejoin = on")
                    cursor.execute("SET enable_nestloop = on")
                    cursor.execute("SET enable_seqscan = on")
                    cursor.execute("SET enable_indexscan = on")
                    cursor.execute("SET enable_bitmapscan = on")
                    
                    # إعدادات الإحصائيات
                    cursor.execute("SET default_statistics_target = 1000")
                    cursor.execute("SET track_activities = on")
                    cursor.execute("SET track_counts = on")
                    cursor.execute("SET track_io_timing = on")
                    
                dbapi_connection.commit()
        
        return engine
    
    def setup_redis_cache(self):
        """إعداد Redis للتخزين المؤقت"""
        try:
            import redis
            config = self.config['redis_cache']
            
            redis_client = redis.Redis(
                host=config['host'],
                port=config['port'],
                db=config['db'],
                decode_responses=config['decode_responses'],
                socket_timeout=config['socket_timeout'],
                socket_connect_timeout=config['socket_connect_timeout'],
                retry_on_timeout=config['retry_on_timeout'],
                health_check_interval=config['health_check_interval']
            )
            
            # اختبار الاتصال
            redis_client.ping()
            return redis_client
            
        except ImportError:
            print("⚠️ Redis غير مثبت. استخدم: pip install redis")
            return None
        except Exception as e:
            print(f"⚠️ فشل في الاتصال بـ Redis: {e}")
            return None
    
    def get_migration_strategy(self):
        """استراتيجية الترقية التدريجية"""
        return {
            'phase_1': {
                'name': 'تحسين SQLite الحالي',
                'description': 'تطبيق تحسينات متطورة على SQLite',
                'actions': [
                    'تطبيق إعدادات SQLite المتطورة',
                    'إضافة فهارس محسنة',
                    'تحسين استعلامات قاعدة البيانات',
                    'إضافة مراقبة الأداء'
                ],
                'expected_improvement': '40-60%',
                'risk_level': 'منخفض',
                'downtime': 'لا يوجد'
            },
            
            'phase_2': {
                'name': 'إضافة Redis للتخزين المؤقت',
                'description': 'تطبيق نظام تخزين مؤقت متطور',
                'actions': [
                    'تثبيت وإعداد Redis',
                    'تطبيق تخزين مؤقت للاستعلامات',
                    'تخزين مؤقت للجلسات',
                    'تحسين أداء التقارير'
                ],
                'expected_improvement': '60-80%',
                'risk_level': 'منخفض',
                'downtime': 'أقل من 5 دقائق'
            },
            
            'phase_3': {
                'name': 'الترقية إلى PostgreSQL',
                'description': 'ترقية كاملة إلى PostgreSQL',
                'actions': [
                    'تثبيت PostgreSQL',
                    'ترحيل البيانات',
                    'تحسين الاستعلامات',
                    'اختبار شامل'
                ],
                'expected_improvement': '100-200%',
                'risk_level': 'متوسط',
                'downtime': '30-60 دقيقة'
            },
            
            'phase_4': {
                'name': 'تطبيق ميزات متطورة',
                'description': 'استخدام ميزات PostgreSQL المتطورة',
                'actions': [
                    'تطبيق Partitioning',
                    'إعداد Read Replicas',
                    'تحسين الفهرسة المتطورة',
                    'مراقبة أداء متقدمة'
                ],
                'expected_improvement': '200-300%',
                'risk_level': 'متوسط',
                'downtime': 'أقل من 15 دقيقة'
            }
        }
    
    def create_hybrid_system(self):
        """إنشاء نظام هجين (SQLite + Redis)"""
        try:
            # محرك SQLite المحسن
            sqlite_engine = self.create_sqlite_advanced_engine()
            
            # Redis للتخزين المؤقت
            redis_client = self.setup_redis_cache()
            
            return {
                'primary_db': sqlite_engine,
                'cache': redis_client,
                'status': 'hybrid_ready' if redis_client else 'sqlite_only'
            }
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النظام الهجين: {e}")
            return None

# إنشاء مثيل الإعدادات المتطورة
advanced_config = AdvancedDatabaseConfig()

def get_advanced_engine(db_type="sqlite_advanced"):
    """الحصول على محرك قاعدة بيانات متطور"""
    if db_type == "sqlite_advanced":
        return advanced_config.create_sqlite_advanced_engine()
    elif db_type == "postgresql":
        return advanced_config.create_postgresql_engine(advanced=True)
    elif db_type == "postgresql_basic":
        return advanced_config.create_postgresql_engine(advanced=False)
    else:
        raise ValueError(f"نوع قاعدة البيانات غير مدعوم: {db_type}")

def setup_hybrid_system():
    """إعداد النظام الهجين"""
    return advanced_config.create_hybrid_system()

def get_migration_plan():
    """الحصول على خطة الترقية"""
    return advanced_config.get_migration_strategy()

if __name__ == "__main__":
    print("🚀 اختبار الإعدادات المتطورة...")
    
    # اختبار SQLite المحسن
    try:
        engine = get_advanced_engine("sqlite_advanced")
        print("✅ تم إنشاء محرك SQLite المتطور")
    except Exception as e:
        print(f"❌ خطأ في SQLite المتطور: {e}")
    
    # اختبار النظام الهجين
    try:
        hybrid = setup_hybrid_system()
        if hybrid:
            print(f"✅ تم إعداد النظام الهجين: {hybrid['status']}")
        else:
            print("❌ فشل في إعداد النظام الهجين")
    except Exception as e:
        print(f"❌ خطأ في النظام الهجين: {e}")
    
    # عرض خطة الترقية
    plan = get_migration_plan()
    print("\n📋 خطة الترقية:")
    for phase, details in plan.items():
        print(f"   {phase}: {details['name']} - تحسين متوقع: {details['expected_improvement']}")
