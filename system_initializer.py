# -*- coding: utf-8 -*-
"""
System Initializer Module
مهيئ النظام - ملف محسن لتجنب أخطاء الاستيراد والكود المكرر
"""

import os
import sys
from pathlib import Path

class SystemInitializer:
    """فئة مهيئ النظام الموحدة"""

    def __init__(self):
        self.initialized = False
        self.advanced_available = False
        self.systems_status = {}

    def check_system_requirements(self):
        """فحص متطلبات النظام"""
        try:
            # فحص Python version
            if sys.version_info < (3, 6):
                print("❌ يتطلب Python 3.6 أو أحدث")
                return False

            # فحص الملفات الأساسية
            required_files = ['database.py', 'main.py']
            for file in required_files:
                if not os.path.exists(file):
                    print(f"❌ ملف مطلوب غير موجود: {file}")
                    return False

            print("✅ تم فحص متطلبات النظام بنجاح")
            return True
        except Exception as e:
            print(f"❌ خطأ في فحص متطلبات النظام: {e}")
            return False

    def setup_logging(self):
        """إعداد نظام السجلات"""
        try:
            # إنشاء مجلد السجلات إذا لم يكن موجوداً
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)
            print("✅ تم إعداد نظام السجلات")
            return True
        except Exception as e:
            print(f"⚠️ خطأ في إعداد السجلات: {e}")
            return False

    def setup_security(self):
        """إعداد الأمان"""
        try:
            # إنشاء مجلد الأمان إذا لم يكن موجوداً
            security_dir = Path("data/security")
            security_dir.mkdir(parents=True, exist_ok=True)
            print("✅ تم إعداد نظام الأمان")
            return True
        except Exception as e:
            print(f"⚠️ خطأ في إعداد الأمان: {e}")
            return False

    def initialize_advanced_systems(self):
        """تهيئة الأنظمة المتقدمة"""
        try:
            if not self.check_system_requirements():
                return False

            # تهيئة الأنظمة الأساسية
            logging_ok = self.setup_logging()
            security_ok = self.setup_security()

            self.systems_status = {
                "basic_systems": True,
                "logging": logging_ok,
                "security": security_ok,
                "advanced_systems": logging_ok and security_ok
            }

            if self.systems_status["advanced_systems"]:
                print("✅ تم تهيئة الأنظمة المتقدمة بنجاح")
                self.advanced_available = True
                self.initialized = True
                return True
            else:
                print("⚠️ تم تهيئة الأنظمة الأساسية فقط")
                self.initialized = True
                return False

        except Exception as e:
            print(f"❌ خطأ في تهيئة الأنظمة المتقدمة: {e}")
            return False

    def shutdown_advanced_systems(self):
        """إغلاق الأنظمة المتقدمة"""
        try:
            print("🔄 إغلاق الأنظمة المتقدمة...")
            self.initialized = False
            self.advanced_available = False
            self.systems_status = {}
            print("✅ تم إغلاق الأنظمة المتقدمة بنجاح")
            return True
        except Exception as e:
            print(f"⚠️ خطأ في إغلاق الأنظمة المتقدمة: {e}")
            return False

    def get_systems_status(self):
        """الحصول على حالة الأنظمة"""
        return self.systems_status.copy() if self.systems_status else {
            "basic_systems": False,
            "advanced_systems": False,
            "logging": False,
            "security": False
        }

# إنشاء مثيل عام
_system_initializer = SystemInitializer()

# دوال الواجهة العامة (للتوافق مع الكود القديم)
def initialize_advanced_systems():
    """تهيئة الأنظمة المتقدمة"""
    return _system_initializer.initialize_advanced_systems()

def shutdown_advanced_systems():
    """إغلاق الأنظمة المتقدمة"""
    return _system_initializer.shutdown_advanced_systems()

def get_systems_status():
    """الحصول على حالة الأنظمة"""
    return _system_initializer.get_systems_status()

def check_system_requirements():
    """فحص متطلبات النظام"""
    return _system_initializer.check_system_requirements()