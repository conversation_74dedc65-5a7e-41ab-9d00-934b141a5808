#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
طابعة الباركود المتقدمة
====================

وحدة شاملة لطباعة ملصقات الباركود مع دعم:
- طباعة ملصقات فردية ومتعددة
- تخصيص حجم وتصميم الملصقات
- معاينة قبل الطباعة
- حفظ كملفات PDF أو صور

المؤلف: نظام إدارة الأعمال
التاريخ: 2025-07-30
"""

import os
import io
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import tempfile

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.units import mm, inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    print("⚠️ مكتبة reportlab غير مثبتة. لن تكون طباعة PDF متاحة")

from utils.barcode_generator import barcode_generator, create_product_barcode_image


class BarcodePrinter:
    """طابعة الباركود المتقدمة"""
    
    def __init__(self):
        self.label_sizes = {
            'small': {'width': 40, 'height': 20},      # ملصق صغير
            'medium': {'width': 60, 'height': 30},     # ملصق متوسط
            'large': {'width': 80, 'height': 40},      # ملصق كبير
            'custom': {'width': 50, 'height': 25}      # ملصق مخصص
        }
        
        self.fonts = {
            'small': 8,
            'medium': 10,
            'large': 12,
            'title': 14
        }
    
    def create_single_label(self, product_data: Dict[str, Any], 
                           label_size: str = 'medium',
                           include_price: bool = True,
                           include_name: bool = True) -> Tuple[bool, str, Optional[Image.Image]]:
        """
        إنشاء ملصق باركود واحد
        
        Args:
            product_data: بيانات المنتج (name, barcode, price, etc.)
            label_size: حجم الملصق (small, medium, large, custom)
            include_price: تضمين السعر
            include_name: تضمين اسم المنتج
        
        Returns:
            tuple: (نجح, رسالة, صورة الملصق)
        """
        try:
            # الحصول على أبعاد الملصق
            size_config = self.label_sizes.get(label_size, self.label_sizes['medium'])
            width_mm = size_config['width']
            height_mm = size_config['height']
            
            # تحويل إلى بكسل (300 DPI)
            width_px = int(width_mm * 300 / 25.4)
            height_px = int(height_mm * 300 / 25.4)
            
            # إنشاء صورة الملصق
            label_img = Image.new('RGB', (width_px, height_px), 'white')
            draw = ImageDraw.Draw(label_img)
            
            # إنشاء الباركود
            barcode_data = product_data.get('barcode', '')
            if not barcode_data:
                return False, "لا يوجد باركود للمنتج", None
            
            success, message, barcode_bytes = create_product_barcode_image(
                barcode_data, 
                format_type='code128'
            )
            
            if not success or not barcode_bytes:
                return False, f"فشل في إنشاء الباركود: {message}", None
            
            # تحميل صورة الباركود
            barcode_img = Image.open(io.BytesIO(barcode_bytes))
            
            # حساب أبعاد الباركود على الملصق
            barcode_height = int(height_px * 0.4)  # 40% من ارتفاع الملصق
            barcode_width = int(width_px * 0.8)    # 80% من عرض الملصق
            
            # تغيير حجم الباركود
            barcode_img = barcode_img.resize((barcode_width, barcode_height), Image.Resampling.LANCZOS)
            
            # وضع الباركود في المنتصف
            barcode_x = (width_px - barcode_width) // 2
            barcode_y = int(height_px * 0.3)  # 30% من الأعلى
            
            label_img.paste(barcode_img, (barcode_x, barcode_y))
            
            # إضافة النصوص
            try:
                # محاولة استخدام خط مناسب
                font_size = self.fonts.get(label_size, 10)
                font = ImageFont.load_default()
            except:
                font = None
            
            # إضافة اسم المنتج
            if include_name and product_data.get('name'):
                product_name = product_data['name']
                if len(product_name) > 20:  # اختصار الاسم إذا كان طويلاً
                    product_name = product_name[:17] + "..."
                
                # حساب موقع النص
                if font:
                    text_width = draw.textlength(product_name, font=font)
                else:
                    text_width = len(product_name) * 6  # تقدير تقريبي
                
                text_x = (width_px - text_width) // 2
                text_y = int(height_px * 0.05)  # 5% من الأعلى
                
                draw.text((text_x, text_y), product_name, fill='black', font=font)
            
            # إضافة السعر
            if include_price and product_data.get('selling_price'):
                price_text = f"{product_data['selling_price']:.2f} جنيه"
                
                if font:
                    text_width = draw.textlength(price_text, font=font)
                else:
                    text_width = len(price_text) * 6
                
                text_x = (width_px - text_width) // 2
                text_y = int(height_px * 0.8)  # 80% من الأعلى
                
                draw.text((text_x, text_y), price_text, fill='black', font=font)
            
            # إضافة رقم الباركود أسفل الصورة
            if font:
                text_width = draw.textlength(barcode_data, font=font)
            else:
                text_width = len(barcode_data) * 6
            
            text_x = (width_px - text_width) // 2
            text_y = int(height_px * 0.9)  # 90% من الأعلى
            
            draw.text((text_x, text_y), barcode_data, fill='black', font=font)
            
            return True, "تم إنشاء الملصق بنجاح", label_img
            
        except Exception as e:
            return False, f"خطأ في إنشاء الملصق: {str(e)}", None
    
    def create_multiple_labels(self, products_data: List[Dict[str, Any]], 
                              labels_per_product: int = 1,
                              label_size: str = 'medium',
                              include_price: bool = True,
                              include_name: bool = True) -> Tuple[bool, str, List[Image.Image]]:
        """
        إنشاء ملصقات متعددة
        
        Args:
            products_data: قائمة بيانات المنتجات
            labels_per_product: عدد الملصقات لكل منتج
            label_size: حجم الملصق
            include_price: تضمين السعر
            include_name: تضمين اسم المنتج
        
        Returns:
            tuple: (نجح, رسالة, قائمة صور الملصقات)
        """
        try:
            labels = []
            
            for product_data in products_data:
                for _ in range(labels_per_product):
                    success, message, label_img = self.create_single_label(
                        product_data, label_size, include_price, include_name
                    )
                    
                    if success and label_img:
                        labels.append(label_img)
                    else:
                        print(f"⚠️ فشل في إنشاء ملصق للمنتج {product_data.get('name', 'غير محدد')}: {message}")
            
            if labels:
                return True, f"تم إنشاء {len(labels)} ملصق بنجاح", labels
            else:
                return False, "فشل في إنشاء أي ملصق", []
                
        except Exception as e:
            return False, f"خطأ في إنشاء الملصقات المتعددة: {str(e)}", []
    
    def create_label_sheet(self, labels: List[Image.Image], 
                          labels_per_row: int = 3,
                          labels_per_column: int = 8,
                          margin: int = 10) -> Tuple[bool, str, Optional[Image.Image]]:
        """
        إنشاء ورقة ملصقات (عدة ملصقات في ورقة واحدة)
        
        Args:
            labels: قائمة صور الملصقات
            labels_per_row: عدد الملصقات في الصف
            labels_per_column: عدد الملصقات في العمود
            margin: الهامش بين الملصقات
        
        Returns:
            tuple: (نجح, رسالة, صورة الورقة)
        """
        try:
            if not labels:
                return False, "لا توجد ملصقات لإنشاء الورقة", None
            
            # أبعاد الملصق الواحد
            label_width, label_height = labels[0].size
            
            # أبعاد الورقة
            sheet_width = (label_width * labels_per_row) + (margin * (labels_per_row + 1))
            sheet_height = (label_height * labels_per_column) + (margin * (labels_per_column + 1))
            
            # إنشاء ورقة فارغة
            sheet = Image.new('RGB', (sheet_width, sheet_height), 'white')
            
            # وضع الملصقات على الورقة
            label_index = 0
            for row in range(labels_per_column):
                for col in range(labels_per_row):
                    if label_index >= len(labels):
                        break
                    
                    # حساب موقع الملصق
                    x = margin + (col * (label_width + margin))
                    y = margin + (row * (label_height + margin))
                    
                    # وضع الملصق
                    sheet.paste(labels[label_index], (x, y))
                    label_index += 1
                
                if label_index >= len(labels):
                    break
            
            return True, f"تم إنشاء ورقة تحتوي على {min(len(labels), labels_per_row * labels_per_column)} ملصق", sheet
            
        except Exception as e:
            return False, f"خطأ في إنشاء ورقة الملصقات: {str(e)}", None
    
    def save_labels_as_pdf(self, labels: List[Image.Image], 
                          output_path: str,
                          labels_per_page: int = 24) -> Tuple[bool, str]:
        """
        حفظ الملصقات كملف PDF
        
        Args:
            labels: قائمة صور الملصقات
            output_path: مسار حفظ ملف PDF
            labels_per_page: عدد الملصقات في الصفحة
        
        Returns:
            tuple: (نجح, رسالة)
        """
        try:
            if not REPORTLAB_AVAILABLE:
                return False, "مكتبة ReportLab غير متاحة لإنشاء PDF"
            
            if not labels:
                return False, "لا توجد ملصقات للحفظ"
            
            # إنشاء ملف PDF
            c = canvas.Canvas(output_path, pagesize=A4)
            page_width, page_height = A4
            
            # حساب أبعاد الملصق على الصفحة
            labels_per_row = 3
            labels_per_column = 8
            
            label_width = (page_width - 40) / labels_per_row
            label_height = (page_height - 40) / labels_per_column
            
            label_index = 0
            
            for page in range((len(labels) + labels_per_page - 1) // labels_per_page):
                if page > 0:
                    c.showPage()  # صفحة جديدة
                
                # رسم الملصقات في الصفحة الحالية
                for row in range(labels_per_column):
                    for col in range(labels_per_row):
                        if label_index >= len(labels):
                            break
                        
                        # حساب موقع الملصق
                        x = 20 + (col * label_width)
                        y = page_height - 20 - ((row + 1) * label_height)
                        
                        # حفظ الملصق كملف مؤقت
                        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                            labels[label_index].save(temp_file.name, 'PNG')
                            
                            # إضافة الصورة إلى PDF
                            c.drawImage(temp_file.name, x, y, 
                                      width=label_width-5, height=label_height-5)
                            
                            # حذف الملف المؤقت
                            os.unlink(temp_file.name)
                        
                        label_index += 1
                    
                    if label_index >= len(labels):
                        break
            
            c.save()
            return True, f"تم حفظ {len(labels)} ملصق في ملف PDF: {output_path}"
            
        except Exception as e:
            return False, f"خطأ في حفظ PDF: {str(e)}"
    
    def save_labels_as_images(self, labels: List[Image.Image], 
                             output_dir: str,
                             prefix: str = "barcode_label") -> Tuple[bool, str, List[str]]:
        """
        حفظ الملصقات كصور منفصلة
        
        Args:
            labels: قائمة صور الملصقات
            output_dir: مجلد الحفظ
            prefix: بادئة اسم الملف
        
        Returns:
            tuple: (نجح, رسالة, قائمة مسارات الملفات)
        """
        try:
            if not labels:
                return False, "لا توجد ملصقات للحفظ", []
            
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(output_dir, exist_ok=True)
            
            saved_files = []
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            for i, label in enumerate(labels):
                filename = f"{prefix}_{timestamp}_{i+1:03d}.png"
                file_path = os.path.join(output_dir, filename)
                
                label.save(file_path, 'PNG')
                saved_files.append(file_path)
            
            return True, f"تم حفظ {len(labels)} ملصق في المجلد: {output_dir}", saved_files
            
        except Exception as e:
            return False, f"خطأ في حفظ الصور: {str(e)}", []
    
    def print_labels(self, labels: List[Image.Image], 
                    printer_name: Optional[str] = None) -> Tuple[bool, str]:
        """
        طباعة الملصقات مباشرة
        
        Args:
            labels: قائمة صور الملصقات
            printer_name: اسم الطابعة (اختياري)
        
        Returns:
            tuple: (نجح, رسالة)
        """
        try:
            # هذه الميزة تحتاج إلى تطوير إضافي حسب نوع الطابعة
            # يمكن استخدام مكتبات مثل win32print على Windows
            return False, "ميزة الطباعة المباشرة قيد التطوير"
            
        except Exception as e:
            return False, f"خطأ في الطباعة: {str(e)}"


# مثيل عام للاستخدام
barcode_printer = BarcodePrinter()


def create_product_labels(products_data: List[Dict[str, Any]], 
                         labels_per_product: int = 1,
                         label_size: str = 'medium',
                         output_format: str = 'pdf',
                         output_path: str = None) -> Tuple[bool, str, Optional[str]]:
    """
    إنشاء وحفظ ملصقات المنتجات
    
    Args:
        products_data: قائمة بيانات المنتجات
        labels_per_product: عدد الملصقات لكل منتج
        label_size: حجم الملصق
        output_format: صيغة الحفظ (pdf, images, sheet)
        output_path: مسار الحفظ
    
    Returns:
        tuple: (نجح, رسالة, مسار الملف المحفوظ)
    """
    try:
        # إنشاء الملصقات
        success, message, labels = barcode_printer.create_multiple_labels(
            products_data, labels_per_product, label_size
        )
        
        if not success or not labels:
            return False, f"فشل في إنشاء الملصقات: {message}", None
        
        # تحديد مسار الحفظ إذا لم يتم تحديده
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if output_format == 'pdf':
                output_path = f"barcode_labels_{timestamp}.pdf"
            else:
                output_path = f"barcode_labels_{timestamp}"
        
        # حفظ حسب الصيغة المطلوبة
        if output_format == 'pdf':
            success, save_message = barcode_printer.save_labels_as_pdf(labels, output_path)
            return success, save_message, output_path if success else None
            
        elif output_format == 'images':
            success, save_message, file_paths = barcode_printer.save_labels_as_images(labels, output_path)
            return success, save_message, output_path if success else None
            
        elif output_format == 'sheet':
            success, sheet_message, sheet_img = barcode_printer.create_label_sheet(labels)
            if success and sheet_img:
                sheet_path = output_path + "_sheet.png"
                sheet_img.save(sheet_path, 'PNG')
                return True, f"تم حفظ ورقة الملصقات: {sheet_path}", sheet_path
            else:
                return False, f"فشل في إنشاء ورقة الملصقات: {sheet_message}", None
        
        else:
            return False, f"صيغة حفظ غير مدعومة: {output_format}", None
            
    except Exception as e:
        return False, f"خطأ في إنشاء ملصقات المنتجات: {str(e)}", None
