#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات الأداء لنظام إدارة الأعمال
=====================================

ملف إعدادات منفصل لإدارة خيارات الأداء والتحسينات
يمكن للمستخدم تخصيص هذه الإعدادات حسب قوة جهازه ومتطلباته

المؤلف: نظام إدارة الأعمال
الإصدار: 2.0
التاريخ: 2025
"""

class PerformanceSettings:
    """إعدادات الأداء والتحسينات"""
    
    def __init__(self):
        # إعدادات التحميل المسبق
        self.PRELOAD_ENABLED = True  # تفعيل التحميل المسبق
        self.PRELOAD_DELAY = 2000  # التأخير قبل بدء التحميل المسبق (بالميلي ثانية) - مُحسن
        self.PRELOAD_INTERVAL = 800  # الفترة بين تحميل كل قسم (بالميلي ثانية) - مُحسن
        self.IMMEDIATE_PRELOAD_DELAY = 300  # التأخير للتحميل الفوري (بالميلي ثانية)

        # الأقسام ذات الأولوية للتحميل المسبق
        self.PRIORITY_TABS = [0, 1, 2, 6, 8]  # لوحة المعلومات، العملاء، الموردين، المصروفات، الفواتير
        self.ESSENTIAL_TABS = [1, 2, 6]  # الأقسام الأساسية للتحميل السريع
        self.IMMEDIATE_TABS = [1, 2, 6, 8]  # الأقسام للتحميل الفوري (العملاء، الموردين، المصروفات، الفواتير)

        # إعدادات التحميل الفوري للجميع
        self.INSTANT_LOAD_ALL = True  # تفعيل التحميل الفوري لجميع الأقسام عند النقر
        self.PRELOAD_MODULES = True  # تحميل مسبق للوحدات لتسريع الاستيراد
        
        # إعدادات التحديث المؤجل
        self.DELAYED_REFRESH_ENABLED = True  # تفعيل التحديث المؤجل
        self.DELAYED_REFRESH_DELAY = 100  # تأخير التحديث (بالميلي ثانية)
        
        # إعدادات الذاكرة والأداء
        self.CACHE_ENABLED = True  # تفعيل التخزين المؤقت
        self.AUTO_CLEANUP_ENABLED = True  # تفعيل التنظيف التلقائي للذاكرة
        self.CLEANUP_INTERVAL = 300000  # فترة التنظيف التلقائي (5 دقائق)
        
        # إعدادات واجهة المستخدم
        self.SMOOTH_ANIMATIONS = True  # تفعيل الحركات السلسة
        self.REDUCE_VISUAL_EFFECTS = False  # تقليل التأثيرات البصرية لتحسين الأداء
        self.FAST_SWITCHING = True  # تفعيل التبديل السريع بين الأقسام
        
        # إعدادات قاعدة البيانات
        self.DB_POOL_SIZE = 10  # حجم مجموعة اتصالات قاعدة البيانات
        self.DB_LAZY_LOADING = True  # تفعيل التحميل الكسول لقاعدة البيانات
        self.DB_CACHE_SIZE = 1000  # حجم ذاكرة التخزين المؤقت لقاعدة البيانات
        
        # إعدادات الشبكة والمزامنة
        self.NETWORK_TIMEOUT = 5000  # مهلة الشبكة (بالميلي ثانية)
        self.SYNC_ENABLED = True  # تفعيل المزامنة
        self.SYNC_INTERVAL = 30000  # فترة المزامنة (30 ثانية)
        
        # إعدادات التشخيص والمراقبة
        self.PERFORMANCE_MONITORING = True  # تفعيل مراقبة الأداء
        self.DEBUG_MODE = False  # وضع التشخيص
        self.LOG_PERFORMANCE = False  # تسجيل بيانات الأداء

        # إعدادات بدء التشغيل السريع الجديدة
        self.FAST_STARTUP_ENABLED = True  # تفعيل بدء التشغيل السريع
        self.PARALLEL_MODULE_LOADING = True  # التحميل المتوازي للوحدات
        self.DEFER_HEAVY_MODULES = True  # تأجيل الوحدات الثقيلة
        self.CACHE_STARTUP_DATA = True  # تخزين بيانات البدء مؤقتاً
        self.SKIP_NON_ESSENTIAL_CHECKS = True  # تخطي الفحوصات غير الأساسية
        self.FAST_DATABASE_INIT = True  # تهيئة سريعة لقاعدة البيانات
        self.MINIMAL_UI_LOAD = True  # تحميل واجهة مستخدم مبسطة
        self.BACKGROUND_OPTIMIZATION = True  # التحسين في الخلفية
        self.STARTUP_TIMEOUT = 10  # مهلة بدء التشغيل بالثواني

        # الوحدات المراد تحميلها مسبقاً
        self.PRELOAD_MODULES_LIST = [
            'PyQt5.QtCore',
            'PyQt5.QtWidgets',
            'PyQt5.QtGui',
            'sqlalchemy.orm',
            'sqlite3'
        ]

        # الوحدات المراد تأجيلها
        self.DEFER_MODULES_LIST = [
            'matplotlib',
            'reportlab',
            'openpyxl',
            'PIL',
            'numpy',
            'pandas'
        ]
        
    def get_preload_settings(self):
        """الحصول على إعدادات التحميل المسبق"""
        return {
            'enabled': self.PRELOAD_ENABLED,
            'delay': self.PRELOAD_DELAY,
            'interval': self.PRELOAD_INTERVAL,
            'priority_tabs': self.PRIORITY_TABS,
            'essential_tabs': self.ESSENTIAL_TABS
        }
    
    def get_ui_settings(self):
        """الحصول على إعدادات واجهة المستخدم"""
        return {
            'smooth_animations': self.SMOOTH_ANIMATIONS,
            'reduce_visual_effects': self.REDUCE_VISUAL_EFFECTS,
            'fast_switching': self.FAST_SWITCHING,
            'delayed_refresh': self.DELAYED_REFRESH_ENABLED,
            'refresh_delay': self.DELAYED_REFRESH_DELAY
        }

    def get_fast_startup_settings(self):
        """الحصول على إعدادات بدء التشغيل السريع"""
        return {
            'enabled': self.FAST_STARTUP_ENABLED,
            'parallel_loading': self.PARALLEL_MODULE_LOADING,
            'defer_heavy_modules': self.DEFER_HEAVY_MODULES,
            'cache_startup_data': self.CACHE_STARTUP_DATA,
            'skip_non_essential_checks': self.SKIP_NON_ESSENTIAL_CHECKS,
            'fast_database_init': self.FAST_DATABASE_INIT,
            'minimal_ui_load': self.MINIMAL_UI_LOAD,
            'background_optimization': self.BACKGROUND_OPTIMIZATION,
            'startup_timeout': self.STARTUP_TIMEOUT,
            'preload_modules': self.PRELOAD_MODULES_LIST,
            'defer_modules': self.DEFER_MODULES_LIST
        }
    
    def get_database_settings(self):
        """الحصول على إعدادات قاعدة البيانات"""
        return {
            'pool_size': self.DB_POOL_SIZE,
            'lazy_loading': self.DB_LAZY_LOADING,
            'cache_size': self.DB_CACHE_SIZE
        }
    
    def get_memory_settings(self):
        """الحصول على إعدادات الذاكرة"""
        return {
            'cache_enabled': self.CACHE_ENABLED,
            'auto_cleanup': self.AUTO_CLEANUP_ENABLED,
            'cleanup_interval': self.CLEANUP_INTERVAL
        }
    
    def apply_performance_profile(self, profile_name):
        """تطبيق ملف أداء محدد"""
        if profile_name == "high_performance":
            self.apply_high_performance_profile()
        elif profile_name == "balanced":
            self.apply_balanced_profile()
        elif profile_name == "low_resource":
            self.apply_low_resource_profile()
        else:
            print(f"⚠️ ملف الأداء غير معروف: {profile_name}")
    
    def apply_high_performance_profile(self):
        """تطبيق ملف الأداء العالي (للأجهزة القوية)"""
        print("🚀 تطبيق ملف الأداء العالي...")
        self.PRELOAD_ENABLED = True
        self.PRELOAD_DELAY = 1000  # تحميل أسرع جداً
        self.PRELOAD_INTERVAL = 300  # فترات أقصر جداً
        self.IMMEDIATE_PRELOAD_DELAY = 100  # تحميل فوري أسرع
        self.PRIORITY_TABS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # تحميل جميع الأقسام
        self.IMMEDIATE_TABS = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]  # جميع الأقسام للتحميل الفوري
        self.INSTANT_LOAD_ALL = True  # تفعيل التحميل الفوري لجميع الأقسام
        self.PRELOAD_MODULES = True  # تحميل مسبق للوحدات
        self.DELAYED_REFRESH_DELAY = 10  # تحديث فائق السرعة
        self.SMOOTH_ANIMATIONS = True
        self.REDUCE_VISUAL_EFFECTS = False
        self.DB_CACHE_SIZE = 5000  # ذاكرة تخزين كبيرة جداً
        
    def apply_balanced_profile(self):
        """تطبيق ملف الأداء المتوازن (الافتراضي)"""
        print("⚖️ تطبيق ملف الأداء المتوازن...")
        self.PRELOAD_ENABLED = True
        self.PRELOAD_DELAY = 3000
        self.PRELOAD_INTERVAL = 1000
        self.PRIORITY_TABS = [0, 1, 2, 6, 8]
        self.DELAYED_REFRESH_DELAY = 100
        self.SMOOTH_ANIMATIONS = True
        self.REDUCE_VISUAL_EFFECTS = False
        self.DB_CACHE_SIZE = 1000
        
    def apply_low_resource_profile(self):
        """تطبيق ملف الأداء المنخفض (للأجهزة الضعيفة)"""
        print("🔋 تطبيق ملف الأداء المنخفض...")
        self.PRELOAD_ENABLED = False  # تعطيل التحميل المسبق
        self.DELAYED_REFRESH_DELAY = 200  # تحديث أبطأ
        self.SMOOTH_ANIMATIONS = False  # تعطيل الحركات
        self.REDUCE_VISUAL_EFFECTS = True  # تقليل التأثيرات
        self.DB_CACHE_SIZE = 500  # ذاكرة تخزين أقل
        self.AUTO_CLEANUP_ENABLED = True
        self.CLEANUP_INTERVAL = 120000  # تنظيف أكثر تكراراً (دقيقتان)
    
    def save_settings(self, filename="performance_config.json"):
        """حفظ الإعدادات في ملف"""
        try:
            import json
            settings = {
                'preload_enabled': self.PRELOAD_ENABLED,
                'preload_delay': self.PRELOAD_DELAY,
                'preload_interval': self.PRELOAD_INTERVAL,
                'priority_tabs': self.PRIORITY_TABS,
                'essential_tabs': self.ESSENTIAL_TABS,
                'delayed_refresh_enabled': self.DELAYED_REFRESH_ENABLED,
                'delayed_refresh_delay': self.DELAYED_REFRESH_DELAY,
                'cache_enabled': self.CACHE_ENABLED,
                'auto_cleanup_enabled': self.AUTO_CLEANUP_ENABLED,
                'cleanup_interval': self.CLEANUP_INTERVAL,
                'smooth_animations': self.SMOOTH_ANIMATIONS,
                'reduce_visual_effects': self.REDUCE_VISUAL_EFFECTS,
                'fast_switching': self.FAST_SWITCHING,
                'db_pool_size': self.DB_POOL_SIZE,
                'db_lazy_loading': self.DB_LAZY_LOADING,
                'db_cache_size': self.DB_CACHE_SIZE
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4, ensure_ascii=False)
            print(f"✅ تم حفظ إعدادات الأداء في: {filename}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ إعدادات الأداء: {e}")
    
    def load_settings(self, filename="performance_config.json", silent=True):
        """تحميل الإعدادات من ملف"""
        try:
            import json
            import os

            if not os.path.exists(filename):
                if not silent:
                    print(f"⚠️ ملف الإعدادات غير موجود: {filename}")
                return False
                
            with open(filename, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            # تطبيق الإعدادات المحملة
            self.PRELOAD_ENABLED = settings.get('preload_enabled', self.PRELOAD_ENABLED)
            self.PRELOAD_DELAY = settings.get('preload_delay', self.PRELOAD_DELAY)
            self.PRELOAD_INTERVAL = settings.get('preload_interval', self.PRELOAD_INTERVAL)
            self.PRIORITY_TABS = settings.get('priority_tabs', self.PRIORITY_TABS)
            self.ESSENTIAL_TABS = settings.get('essential_tabs', self.ESSENTIAL_TABS)
            self.DELAYED_REFRESH_ENABLED = settings.get('delayed_refresh_enabled', self.DELAYED_REFRESH_ENABLED)
            self.DELAYED_REFRESH_DELAY = settings.get('delayed_refresh_delay', self.DELAYED_REFRESH_DELAY)
            self.CACHE_ENABLED = settings.get('cache_enabled', self.CACHE_ENABLED)
            self.AUTO_CLEANUP_ENABLED = settings.get('auto_cleanup_enabled', self.AUTO_CLEANUP_ENABLED)
            self.CLEANUP_INTERVAL = settings.get('cleanup_interval', self.CLEANUP_INTERVAL)
            self.SMOOTH_ANIMATIONS = settings.get('smooth_animations', self.SMOOTH_ANIMATIONS)
            self.REDUCE_VISUAL_EFFECTS = settings.get('reduce_visual_effects', self.REDUCE_VISUAL_EFFECTS)
            self.FAST_SWITCHING = settings.get('fast_switching', self.FAST_SWITCHING)
            self.DB_POOL_SIZE = settings.get('db_pool_size', self.DB_POOL_SIZE)
            self.DB_LAZY_LOADING = settings.get('db_lazy_loading', self.DB_LAZY_LOADING)
            self.DB_CACHE_SIZE = settings.get('db_cache_size', self.DB_CACHE_SIZE)
            
            if not silent:
                print(f"✅ تم تحميل إعدادات الأداء من: {filename}")
            return True

        except Exception as e:
            if not silent:
                print(f"❌ خطأ في تحميل إعدادات الأداء: {e}")
            return False

# إنشاء مثيل عام للإعدادات
performance_settings = PerformanceSettings()

# تحميل الإعدادات المحفوظة إن وجدت (بدون رسائل خطأ)
try:
    performance_settings.load_settings()
except:
    # تجاهل الأخطاء - سيتم استخدام الإعدادات الافتراضية
    pass
