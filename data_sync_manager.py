# -*- coding: utf-8 -*-
"""
Data Sync Manager Module
مدير مزامنة البيانات - ملف محسن لتجنب أخطاء الاستيراد والكود المكرر
"""

import json
import os
import threading
from datetime import datetime
from pathlib import Path

class DataSyncManager:
    """
    مدير مزامنة البيانات المحسن
    """

    def __init__(self):
        self.sync_file = Path("data") / "data_sync.json"
        self.is_enabled = False
        self.last_sync = None
        self.sync_lock = threading.Lock()

        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        self.sync_file.parent.mkdir(exist_ok=True)

    def initialize(self):
        """تهيئة مدير المزامنة"""
        try:
            with self.sync_lock:
                print("🔄 تهيئة نظام المزامنة...")
                self.is_enabled = True
                self.last_sync = datetime.now()
                print("✅ تم تهيئة نظام المزامنة بنجاح")
                return True
        except Exception as e:
            print(f"⚠️ فشل في تهيئة نظام المزامنة: {e}")
            return False

    def sync_data(self):
        """مزامنة البيانات"""
        if not self.is_enabled:
            return False

        try:
            with self.sync_lock:
                sync_data = {
                    "last_sync": datetime.now().isoformat(),
                    "status": "success",
                    "sync_count": getattr(self, 'sync_count', 0) + 1
                }

                with open(self.sync_file, 'w', encoding='utf-8') as f:
                    json.dump(sync_data, f, ensure_ascii=False, indent=2)

                self.last_sync = datetime.now()
                self.sync_count = sync_data["sync_count"]
                return True
        except Exception as e:
            print(f"⚠️ خطأ في مزامنة البيانات: {e}")
            return False

    def get_sync_status(self):
        """الحصول على حالة المزامنة"""
        try:
            if not self.sync_file.exists():
                return {"status": "never_synced", "enabled": self.is_enabled}

            with open(self.sync_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                data["enabled"] = self.is_enabled
                return data
        except Exception:
            return {"status": "error", "enabled": self.is_enabled}

    def get_sync_stats(self):
        """الحصول على إحصائيات المزامنة المفصلة"""
        try:
            status = self.get_sync_status()
            return {
                "web_app_status": False,  # يمكن تحديثه لاحقاً
                "sync_status": {
                    "sync_needed": False,
                    "last_sync": status.get("last_sync"),
                    "enabled": self.is_enabled
                },
                "total_records": getattr(self, 'total_records', 0),
                "synced_records": getattr(self, 'synced_records', 0),
                "sync_count": status.get("sync_count", 0)
            }
        except Exception:
            return None

    def stop(self):
        """إيقاف نظام المزامنة"""
        try:
            with self.sync_lock:
                print("🔄 إيقاف نظام مزامنة البيانات...")
                self.is_enabled = False
                print("✅ تم إيقاف نظام المزامنة بنجاح")
                return True
        except Exception as e:
            print(f"⚠️ خطأ في إيقاف نظام المزامنة: {e}")
            return False

    def cleanup(self):
        """تنظيف ملفات المزامنة"""
        try:
            if self.sync_file.exists():
                self.sync_file.unlink()
            return True
        except Exception:
            return False

# إنشاء مثيل عام
_sync_manager = DataSyncManager()

# دوال الواجهة العامة (للتوافق مع الكود القديم)
def start_data_sync():
    """بدء نظام المزامنة"""
    return _sync_manager.initialize()

def stop_data_sync():
    """إيقاف نظام المزامنة"""
    return _sync_manager.stop()

def sync_data():
    """مزامنة البيانات"""
    return _sync_manager.sync_data()

def get_sync_status():
    """الحصول على حالة المزامنة"""
    return _sync_manager.get_sync_status()

def get_sync_stats():
    """الحصول على إحصائيات المزامنة"""
    return _sync_manager.get_sync_stats()

def cleanup_sync():
    """تنظيف ملفات المزامنة"""
    return _sync_manager.cleanup()

def get_sync_manager():
    """الحصول على مدير المزامنة"""
    return _sync_manager
