#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الأعمال المتكامل
==========================

تطبيق شامل لإدارة الحسابات والمشاريع والعملاء باستخدام PyQt5 و SQLAlchemy

الميزات الرئيسية:
- إدارة العملاء والموردين والموظفين
- نظام الفواتير والمبيعات والمشتريات
- إدارة المشاريع والعقارات
- نظام التقارير المالية المتقدم
- أنظمة الأمان والنسخ الاحتياطي
- مراقبة الأداء والصلاحيات

المؤلف: نظام إدارة الأعمال
الإصدار: 2.0
التاريخ: 2025
"""

import sys
import os
import warnings
import traceback
import time
from concurrent.futures import ThreadPoolExecutor
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, qInstallMessageHandler, QtMsgType
from PyQt5.QtGui import QFont

# استيراد محسن بدء التشغيل السريع
try:
    from fast_startup_optimizer import fast_optimizer, optimize_startup, get_startup_metrics
    FAST_OPTIMIZER_AVAILABLE = True
    print("✅ تم تحميل محسن بدء التشغيل السريع")
except ImportError:
    FAST_OPTIMIZER_AVAILABLE = False
    print("⚠️ محسن بدء التشغيل السريع غير متاح")

    # نسخة مبسطة من المحسن
    class SimpleOptimizer:
        def preload_critical_modules(self):
            pass
        def get_metrics(self):
            return {}

    fast_optimizer = SimpleOptimizer()

# استيراد الأنظمة المتقدمة مع التحسين
ADVANCED_SYSTEMS_AVAILABLE = False
advanced_systems_modules = {}

def load_advanced_systems_async():
    """تحميل الأنظمة المتقدمة بشكل غير متزامن"""
    global ADVANCED_SYSTEMS_AVAILABLE, advanced_systems_modules
    try:
        if os.path.exists('system_initializer.py'):
            from system_initializer import initialize_advanced_systems, shutdown_advanced_systems
            advanced_systems_modules['initialize'] = initialize_advanced_systems
            advanced_systems_modules['shutdown'] = shutdown_advanced_systems
            ADVANCED_SYSTEMS_AVAILABLE = True
            return True, "تم تحميل الأنظمة المتقدمة بنجاح"
        else:
            return False, "ملف الأنظمة المتقدمة غير موجود"
    except ImportError as e:
        return False, f"الأنظمة المتقدمة غير متاحة: {e}"
    except Exception as e:
        return False, f"خطأ في تحميل الأنظمة المتقدمة: {e}"

# بدء تحميل الأنظمة المتقدمة في الخلفية
advanced_systems_future = None
try:
    with ThreadPoolExecutor(max_workers=1) as executor:
        advanced_systems_future = executor.submit(load_advanced_systems_async)
        # انتظار قصير للتحميل السريع
        try:
            success, message = advanced_systems_future.result(timeout=0.5)
            if success:
                print(f"✅ {message}")
            else:
                print(f"⚠️ {message}")
        except:
            # إذا لم يكتمل التحميل في 0.5 ثانية، سيكمل في الخلفية
            print("🔄 تحميل الأنظمة المتقدمة في الخلفية...")
except Exception as e:
    print(f"⚠️ خطأ في بدء تحميل الأنظمة المتقدمة: {e}")

# إعداد إخفاء التحذيرات غير المهمة فقط
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
warnings.filterwarnings("ignore", category=FutureWarning)
# الاحتفاظ بالتحذيرات المهمة مثل UserWarning و RuntimeWarning
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false'

# إخفاء رسائل CSS غير المدعومة نهائياً
def qt_message_handler(mode, _, message):
    """مرشح رسائل Qt لإخفاء رسائل CSS غير المدعومة"""
    # قائمة الرسائل المراد إخفاؤها
    blocked_messages = [
        'Unknown property',
        'text-shadow',
        'box-shadow',
        'transform',
        'transition',
        'filter',
        'backdrop-filter',
        'overflow',
        'text-overflow',
        'cursor',
        'letter-spacing',
        'word-spacing',
        'text-decoration',
        'outline',
        'resize',
        'user-select',
        'pointer-events',
        'clip-path',
        'mask',
        'opacity',
        'visibility',
        'z-index',
        'position',
        'top',
        'left',
        'right',
        'bottom',
        'float',
        'clear',
        'display',
        'flex',
        'grid',
        'animation',
        'keyframes'
    ]

    # إخفاء الرسائل المحددة
    if any(blocked in message for blocked in blocked_messages):
        return

    # السماح بالرسائل الحرجة فقط
    if mode in [QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg]:
        print(f"Qt Critical: {message}")

# تطبيق مرشح الرسائل
qInstallMessageHandler(qt_message_handler)

# تأجيل الاستيراد للتحميل السريع
def get_main_window():
    """استيراد النافذة الرئيسية عند الحاجة"""
    from ui.main_window import MainWindow
    return MainWindow

def get_database_functions():
    """استيراد دوال قاعدة البيانات عند الحاجة"""
    from database import init_db, get_session, User, hash_password
    return init_db, get_session, User, hash_password

def setup_application():
    """
    إعداد تطبيق PyQt مع التحسينات والإعدادات المتقدمة للبدء السريع

    يتضمن:
    - إعداد الخط الافتراضي
    - تحسين الأداء
    - إعداد معالج الرسائل
    - تحسين العرض على الشاشات عالية الدقة
    - تحسينات البدء السريع

    Returns:
        QApplication: مثيل التطبيق المُعد
    """
    # تعيين خيارات الأداء العالي قبل إنشاء التطبيق
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    QApplication.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)

    # تحسينات إضافية للأداء
    QApplication.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts, True)

    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # تعيين اسم التطبيق
    app.setApplicationName("Advanced Management System")

    # تعيين نمط التطبيق (Fusion أسرع من الأنماط الأخرى)
    app.setStyle("Fusion")

    # تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط الافتراضي بشكل محسن
    try:
        font = QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        font.setHintingPreference(QFont.PreferDefaultHinting)  # تحسين الأداء
        app.setFont(font)
    except Exception as e:
        print(f"تحذير: فشل في تعيين الخط الافتراضي: {e}")
        # المتابعة بالخط الافتراضي للنظام

    # تحسين استهلاك الذاكرة
    app.setAttribute(Qt.AA_Use96Dpi, False)

    return app

def main():
    """
    الدالة الرئيسية للتطبيق

    تتولى:
    - تهيئة التطبيق والأنظمة المتقدمة
    - إعداد قاعدة البيانات والمستخدم الإداري
    - إنشاء وعرض النافذة الرئيسية
    - معالجة الأخطاء والإغلاق الآمن

    يحل مشكلة الشاشة السوداء من خلال:
    - التهيئة المتدرجة للأنظمة
    - معالجة شاملة للأخطاء
    - إعداد صحيح لواجهة المستخدم
    """
    global ADVANCED_SYSTEMS_AVAILABLE

    try:
        print("🚀 بدء تشغيل البرنامج...")

        # بدء قياس وقت التشغيل
        startup_start_time = time.time()

        # تشغيل تحسين بدء التشغيل السريع
        if FAST_OPTIMIZER_AVAILABLE:
            try:
                print("🚀 تشغيل محسن بدء التشغيل السريع...")
                optimization_results = optimize_startup()
                print("✅ اكتمل تحسين بدء التشغيل")
            except Exception as e:
                print(f"⚠️ خطأ في محسن بدء التشغيل: {e}")
        else:
            # تحميل الوحدات الحرجة مسبقاً (النسخة المبسطة)
            fast_optimizer.preload_critical_modules()

        # تهيئة الأنظمة المتقدمة
        if ADVANCED_SYSTEMS_AVAILABLE and 'initialize' in advanced_systems_modules:
            try:
                print("🔧 تهيئة الأنظمة المتقدمة...")
                advanced_success = advanced_systems_modules['initialize']()
                if advanced_success:
                    print("✅ تم تهيئة الأنظمة المتقدمة بنجاح")
                else:
                    print("⚠️ تم تهيئة الأنظمة المتقدمة مع بعض القيود")
            except Exception as e:
                print(f"❌ خطأ في تهيئة الأنظمة المتقدمة: {e}")
                ADVANCED_SYSTEMS_AVAILABLE = False

        # إعداد تطبيق PyQt
        app = setup_application()
        print("✅ تم إعداد التطبيق بنجاح")

        # إعداد قاعدة البيانات مع التحسينات المتقدمة
        print("🔧 إعداد قاعدة البيانات مع التحسينات المتقدمة...")
        init_db, get_session, User, hash_password = get_database_functions()
        init_db()
        print("✅ تم إعداد قاعدة البيانات مع التحسينات بنجاح")

        # إنشاء جلسة قاعدة البيانات
        session = get_session()
        print("✅ تم إنشاء جلسة قاعدة البيانات بنجاح")

        try:
            # الحصول على المستخدم الإداري (تم إنشاؤه في init_db)
            user = session.query(User).filter_by(role="admin").first()
            if not user:
                print("⚠️ لم يتم العثور على مستخدم إداري، سيتم إنشاؤه تلقائياً في المرة القادمة")
                # إنشاء مستخدم مؤقت للجلسة الحالية
                user = User(
                    username="admin",
                    password=hash_password("admin"),
                    role="admin",
                    full_name="المدير العام"
                )
                session.add(user)
                session.commit()
                print("✅ تم إنشاء المستخدم الإداري بنجاح")

            # إنشاء النافذة الرئيسية
            print("🚀 إنشاء النافذة الرئيسية...")
            try:
                MainWindow = get_main_window()
                window = MainWindow(session=session, current_user=user)
                print("✅ تم إنشاء النافذة الرئيسية بنجاح")
            except Exception as e:
                print(f"❌ خطأ في إنشاء النافذة الرئيسية: {str(e)}")
                traceback.print_exc()
                session.close()  # إغلاق الجلسة في حالة الخطأ
                raise
        except Exception as e:
            print(f"❌ خطأ في إعداد المستخدم: {str(e)}")
            session.close()  # إغلاق الجلسة في حالة الخطأ
            raise

        # إظهار النافذة الرئيسية بحجم الشاشة كاملة
        print("📺 إظهار النافذة بحجم الشاشة كاملة...")

        # تعيين النافذة لتفتح بحجم الشاشة كاملة مباشرة
        window.showMaximized()

        # التأكد من أن النافذة في المقدمة
        window.raise_()
        window.activateWindow()

        # حساب وعرض وقت بدء التشغيل مع مقاييس التحسين
        startup_time = time.time() - startup_start_time
        print(f"🎉 تم تشغيل البرنامج بنجاح في {startup_time:.2f} ثانية!")

        # عرض مقاييس التحسين إذا كانت متاحة
        if FAST_OPTIMIZER_AVAILABLE:
            try:
                metrics = get_startup_metrics()
                if metrics:
                    print("📊 مقاييس الأداء:")
                    if 'total_optimization_time' in metrics:
                        print(f"   ⏱️ وقت التحسين: {metrics['total_optimization_time']:.3f}s")
                    if 'memory_usage' in metrics:
                        print(f"   🧠 استهلاك الذاكرة: {metrics['memory_usage']:.1f}%")
                    if 'cpu_usage' in metrics:
                        print(f"   ⚡ استهلاك المعالج: {metrics['cpu_usage']:.1f}%")
            except Exception as e:
                print(f"⚠️ خطأ في عرض مقاييس الأداء: {e}")

        # إضافة زر تطبيق الويب في شريط المهام (مبسط)
        try:
            # التحقق من وجود ملف تطبيق الويب قبل إضافة الزر
            web_app_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'final_web_app.py')
            if os.path.exists(web_app_file):
                print("✅ تم العثور على ملف تطبيق الويب")
            else:
                print("⚠️ ملف تطبيق الويب غير موجود")

        except Exception as e:
            print(f"⚠️ تحذير في فحص تطبيق الويب: {e}")

        # تشغيل حلقة الأحداث
        exit_code = app.exec_()

        # إغلاق الأنظمة المتقدمة عند الخروج
        if ADVANCED_SYSTEMS_AVAILABLE and 'shutdown' in advanced_systems_modules:
            try:
                print("🔄 إغلاق الأنظمة المتقدمة...")
                advanced_systems_modules['shutdown']()
                print("✅ تم إغلاق الأنظمة المتقدمة بنجاح")
            except Exception as e:
                print(f"❌ خطأ في إغلاق الأنظمة المتقدمة: {e}")
        else:
            print("ℹ️ لا توجد أنظمة متقدمة لإغلاقها")

        # إغلاق جلسة قاعدة البيانات
        try:
            if 'session' in locals() and session:
                session.close()
                print("✅ تم إغلاق جلسة قاعدة البيانات")
        except Exception as e:
            print(f"⚠️ خطأ في إغلاق جلسة قاعدة البيانات: {e}")

        # تنظيف موارد محسن بدء التشغيل
        if FAST_OPTIMIZER_AVAILABLE:
            try:
                fast_optimizer.cleanup()
                print("✅ تم تنظيف موارد محسن بدء التشغيل")
            except Exception as e:
                print(f"⚠️ خطأ في تنظيف موارد المحسن: {e}")

        sys.exit(exit_code)

    except Exception as e:
        # معالجة الأخطاء غير المتوقعة
        error_message = f"❌ حدث خطأ غير متوقع: {str(e)}"
        print(error_message)

        # طباعة تفاصيل الخطأ للمطور
        traceback.print_exc()

        # محاولة عرض رسالة الخطأ في نافذة منبثقة
        try:
            QMessageBox.critical(None, "خطأ في البرنامج", error_message)
        except:
            # إذا فشل عرض النافذة المنبثقة، اطبع الخطأ في وحدة التحكم
            print("❌ فشل في عرض نافذة الخطأ")

        sys.exit(1)

if __name__ == "__main__":
    main()
    