# -*- coding: utf-8 -*-
"""
نظام الأمان المتقدم
Advanced Security System
"""

import os
import hashlib
import secrets
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import threading

class AdvancedSecurityManager:
    """مدير الأمان المتقدم"""
    
    def __init__(self):
        self.security_dir = Path("data/security")
        self.security_dir.mkdir(parents=True, exist_ok=True)
        
        # ملفات الأمان
        self.master_key_file = self.security_dir / "master.key"
        self.sessions_file = self.security_dir / "sessions.json"
        self.audit_log_file = self.security_dir / "audit.log"
        self.permissions_file = self.security_dir / "permissions.json"
        
        # إعدادات الأمان
        self.session_timeout = 3600  # ساعة واحدة
        self.max_login_attempts = 5
        self.lockout_duration = 300  # 5 دقائق
        
        # بيانات الجلسة والأمان
        self.active_sessions = {}
        self.failed_attempts = {}
        self.locked_accounts = {}
        self.permissions_cache = {}
        
        # قفل للأمان في البيئة متعددة الخيوط
        self.lock = threading.Lock()
        
        # تهيئة النظام
        self.initialize_security()
    
    def initialize_security(self):
        """تهيئة نظام الأمان"""
        try:
            # تحميل أو إنشاء المفتاح الرئيسي
            self.load_or_create_master_key()
            
            # تحميل الجلسات النشطة
            self.load_active_sessions()
            
            # تحميل الصلاحيات
            self.load_permissions()
            
            # تنظيف الجلسات المنتهية الصلاحية
            self.cleanup_expired_sessions()
            
            print("✅ تم تهيئة نظام الأمان المتقدم")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام الأمان: {e}")
    
    def load_or_create_master_key(self):
        """تحميل أو إنشاء المفتاح الرئيسي"""
        try:
            if self.master_key_file.exists():
                with open(self.master_key_file, 'rb') as f:
                    self.master_key = f.read()
            else:
                # إنشاء مفتاح جديد
                self.master_key = Fernet.generate_key()
                with open(self.master_key_file, 'wb') as f:
                    f.write(self.master_key)
                
                # تأمين الملف (Windows)
                try:
                    os.chmod(self.master_key_file, 0o600)
                except:
                    pass
            
            self.cipher = Fernet(self.master_key)
            
        except Exception as e:
            print(f"خطأ في تحميل المفتاح الرئيسي: {e}")
            self.cipher = None
    
    def load_active_sessions(self):
        """تحميل الجلسات النشطة"""
        try:
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    encrypted_data = f.read()
                    if encrypted_data and self.cipher:
                        decrypted_data = self.cipher.decrypt(encrypted_data.encode())
                        self.active_sessions = json.loads(decrypted_data.decode())
        except Exception as e:
            print(f"خطأ في تحميل الجلسات: {e}")
            self.active_sessions = {}
    
    def save_active_sessions(self):
        """حفظ الجلسات النشطة"""
        try:
            if self.cipher:
                data = json.dumps(self.active_sessions, ensure_ascii=False)
                encrypted_data = self.cipher.encrypt(data.encode())
                with open(self.sessions_file, 'w', encoding='utf-8') as f:
                    f.write(encrypted_data.decode())
        except Exception as e:
            print(f"خطأ في حفظ الجلسات: {e}")
    
    def load_permissions(self):
        """تحميل الصلاحيات"""
        try:
            if self.permissions_file.exists():
                with open(self.permissions_file, 'r', encoding='utf-8') as f:
                    self.permissions_cache = json.load(f)
            else:
                # إنشاء صلاحيات افتراضية
                self.permissions_cache = {
                    'admin': {
                        'can_view_all': True,
                        'can_edit_all': True,
                        'can_delete_all': True,
                        'can_manage_users': True,
                        'can_view_reports': True,
                        'can_backup': True,
                        'can_restore': True
                    },
                    'manager': {
                        'can_view_all': True,
                        'can_edit_all': True,
                        'can_delete_all': False,
                        'can_manage_users': False,
                        'can_view_reports': True,
                        'can_backup': False,
                        'can_restore': False
                    },
                    'user': {
                        'can_view_all': False,
                        'can_edit_all': False,
                        'can_delete_all': False,
                        'can_manage_users': False,
                        'can_view_reports': False,
                        'can_backup': False,
                        'can_restore': False
                    }
                }
                self.save_permissions()
        except Exception as e:
            print(f"خطأ في تحميل الصلاحيات: {e}")
            self.permissions_cache = {}
    
    def save_permissions(self):
        """حفظ الصلاحيات"""
        try:
            with open(self.permissions_file, 'w', encoding='utf-8') as f:
                json.dump(self.permissions_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الصلاحيات: {e}")
    
    def hash_password_advanced(self, password: str, salt: bytes = None) -> Tuple[str, bytes]:
        """تشفير كلمة المرور المتقدم"""
        if salt is None:
            salt = secrets.token_bytes(32)
        
        # استخدام PBKDF2 مع SHA-256
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,  # 100,000 تكرار للأمان
        )
        
        key = kdf.derive(password.encode('utf-8'))
        
        # دمج Salt مع Hash
        combined = salt + key
        encoded = base64.b64encode(combined).decode('utf-8')
        
        return encoded, salt
    
    def verify_password_advanced(self, password: str, stored_hash: str) -> bool:
        """التحقق من كلمة المرور المتقدم"""
        try:
            # فك تشفير البيانات المخزنة
            combined = base64.b64decode(stored_hash.encode('utf-8'))
            salt = combined[:32]
            stored_key = combined[32:]
            
            # إعادة تشفير كلمة المرور المدخلة
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            key = kdf.derive(password.encode('utf-8'))
            
            # مقارنة آمنة
            return secrets.compare_digest(stored_key, key)
            
        except Exception as e:
            print(f"خطأ في التحقق من كلمة المرور: {e}")
            return False
    
    def create_secure_session(self, user_id: int, username: str, role: str) -> str:
        """إنشاء جلسة آمنة"""
        with self.lock:
            # إنشاء معرف جلسة آمن
            session_id = secrets.token_urlsafe(32)
            
            # بيانات الجلسة
            session_data = {
                'user_id': user_id,
                'username': username,
                'role': role,
                'created_at': datetime.now().isoformat(),
                'last_activity': datetime.now().isoformat(),
                'ip_address': '127.0.0.1',  # يمكن تحسينه لاحقاً
                'user_agent': 'Desktop App',
                'permissions': self.permissions_cache.get(role, {})
            }
            
            self.active_sessions[session_id] = session_data
            self.save_active_sessions()
            
            # تسجيل في سجل التدقيق
            self.log_security_event('SESSION_CREATED', {
                'user_id': user_id,
                'username': username,
                'session_id': session_id[:8] + '...'  # جزء من معرف الجلسة للأمان
            })
            
            return session_id
    
    def validate_session(self, session_id: str) -> Optional[Dict]:
        """التحقق من صحة الجلسة"""
        with self.lock:
            if session_id not in self.active_sessions:
                return None
            
            session_data = self.active_sessions[session_id]
            
            # فحص انتهاء الصلاحية
            last_activity = datetime.fromisoformat(session_data['last_activity'])
            if datetime.now() - last_activity > timedelta(seconds=self.session_timeout):
                # انتهت صلاحية الجلسة
                del self.active_sessions[session_id]
                self.save_active_sessions()
                return None
            
            # تحديث آخر نشاط
            session_data['last_activity'] = datetime.now().isoformat()
            self.save_active_sessions()
            
            return session_data
    
    def logout_session(self, session_id: str) -> bool:
        """تسجيل خروج الجلسة"""
        with self.lock:
            if session_id in self.active_sessions:
                session_data = self.active_sessions[session_id]
                
                # تسجيل في سجل التدقيق
                self.log_security_event('SESSION_LOGOUT', {
                    'user_id': session_data.get('user_id'),
                    'username': session_data.get('username'),
                    'session_id': session_id[:8] + '...'
                })
                
                del self.active_sessions[session_id]
                self.save_active_sessions()
                return True
            
            return False
    
    def check_permission(self, session_id: str, permission: str) -> bool:
        """فحص الصلاحية"""
        session_data = self.validate_session(session_id)
        if not session_data:
            return False
        
        permissions = session_data.get('permissions', {})
        return permissions.get(permission, False)
    
    def log_security_event(self, event_type: str, details: Dict):
        """تسجيل حدث أمني"""
        try:
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'details': details
            }
            
            with open(self.audit_log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
                
        except Exception as e:
            print(f"خطأ في تسجيل الحدث الأمني: {e}")
    
    def check_login_attempts(self, username: str) -> bool:
        """فحص محاولات تسجيل الدخول"""
        with self.lock:
            current_time = datetime.now()
            
            # فحص إذا كان الحساب مقفل
            if username in self.locked_accounts:
                lock_time = datetime.fromisoformat(self.locked_accounts[username])
                if current_time - lock_time < timedelta(seconds=self.lockout_duration):
                    return False  # الحساب مازال مقفل
                else:
                    # انتهت فترة القفل
                    del self.locked_accounts[username]
                    if username in self.failed_attempts:
                        del self.failed_attempts[username]
            
            return True
    
    def record_failed_login(self, username: str):
        """تسجيل محاولة دخول فاشلة"""
        with self.lock:
            if username not in self.failed_attempts:
                self.failed_attempts[username] = []
            
            self.failed_attempts[username].append(datetime.now().isoformat())
            
            # إزالة المحاولات القديمة (أكثر من ساعة)
            cutoff_time = datetime.now() - timedelta(hours=1)
            self.failed_attempts[username] = [
                attempt for attempt in self.failed_attempts[username]
                if datetime.fromisoformat(attempt) > cutoff_time
            ]
            
            # فحص إذا تجاوز الحد الأقصى
            if len(self.failed_attempts[username]) >= self.max_login_attempts:
                self.locked_accounts[username] = datetime.now().isoformat()
                
                # تسجيل في سجل التدقيق
                self.log_security_event('ACCOUNT_LOCKED', {
                    'username': username,
                    'failed_attempts': len(self.failed_attempts[username])
                })
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        with self.lock:
            current_time = datetime.now()
            expired_sessions = []
            
            for session_id, session_data in self.active_sessions.items():
                last_activity = datetime.fromisoformat(session_data['last_activity'])
                if current_time - last_activity > timedelta(seconds=self.session_timeout):
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                del self.active_sessions[session_id]
            
            if expired_sessions:
                self.save_active_sessions()
                print(f"✅ تم تنظيف {len(expired_sessions)} جلسة منتهية الصلاحية")
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """تشفير البيانات الحساسة"""
        if not self.cipher:
            return data
        
        try:
            encrypted_data = self.cipher.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            print(f"خطأ في تشفير البيانات: {e}")
            return data
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات الحساسة"""
        if not self.cipher:
            return encrypted_data
        
        try:
            decoded_data = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.cipher.decrypt(decoded_data)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            print(f"خطأ في فك تشفير البيانات: {e}")
            return encrypted_data
    
    def get_security_status(self) -> Dict:
        """الحصول على حالة الأمان"""
        with self.lock:
            return {
                'active_sessions': len(self.active_sessions),
                'locked_accounts': len(self.locked_accounts),
                'failed_attempts_accounts': len(self.failed_attempts),
                'encryption_enabled': self.cipher is not None,
                'session_timeout_minutes': self.session_timeout // 60,
                'max_login_attempts': self.max_login_attempts,
                'lockout_duration_minutes': self.lockout_duration // 60
            }

# إنشاء مثيل مدير الأمان المتقدم
security_manager = AdvancedSecurityManager()

def authenticate_user(username: str, password: str) -> Optional[str]:
    """مصادقة المستخدم"""
    # فحص محاولات تسجيل الدخول
    if not security_manager.check_login_attempts(username):
        return None
    
    # هنا يجب إضافة التحقق من قاعدة البيانات
    # هذا مثال مبسط
    if username == "admin" and password == "admin":
        session_id = security_manager.create_secure_session(1, username, "admin")
        return session_id
    else:
        security_manager.record_failed_login(username)
        return None

def check_session(session_id: str) -> Optional[Dict]:
    """فحص الجلسة"""
    return security_manager.validate_session(session_id)

def check_permission(session_id: str, permission: str) -> bool:
    """فحص الصلاحية"""
    return security_manager.check_permission(session_id, permission)

def logout_user(session_id: str) -> bool:
    """تسجيل خروج المستخدم"""
    return security_manager.logout_session(session_id)

def get_security_status() -> Dict:
    """الحصول على حالة الأمان"""
    return security_manager.get_security_status()

# تم إزالة دوال الاختبار
