import sys
import platform
import ctypes
from ctypes import wintypes
import csv
import json
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QComboBox, QSizePolicy, QMenu, QAction,
                            QDialog, QFormLayout, QTextEdit, QDoubleSpinBox, QMessageBox,
                            QDateEdit, QTabWidget, QSplitter, QListWidget, QListWidgetItem,
                            QTextBrowser, QFileDialog, QAbstractItemView, QScrollArea,
                            QCheckBox)
from PyQt5.QtCore import Qt, QDate, QTimer, QRect
from PyQt5.QtGui import (QIcon, QFont, QColor, QPainter, QPixmap, QBrush, QPen,
                        QLinearGradient, QRadialGradient, QTextDocument)
from PyQt5.QtPrintSupport import QPrinter

from database import Sale, Client, get_session
from utils import (show_error_message, show_info_message, format_currency, safe_edit_item, show_confirmation_message,
                   format_datetime_for_export, format_datetime_for_filename)
from ui.unified_styles import UnifiedStyles
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin
from sqlalchemy import func


class SaleWarningDialog(QDialog):
    """نافذة تحذير متطورة للمبيعات مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تحذير", message="", icon="⚠️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(251, 191, 36, 0.2),
                    stop:0.5 rgba(245, 158, 11, 0.3),
                    stop:1 rgba(217, 119, 6, 0.2));
                border: 2px solid rgba(251, 191, 36, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التحذير
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ تم فهم التحذير؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم الأزرار: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")
            # المتابعة بدون تخصيص شريط العنوان


class SaleConfirmationDialog(QDialog):
    """نافذة تأكيد متطورة للمبيعات مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تأكيد", message="", icon="❓"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التأكيد
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # سؤال التأكيد
        question_label = QLabel("❓ هل تريد المتابعة؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("✅ تأكيد")
        self.style_advanced_button(confirm_button, 'success')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار التأكيد: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان للتأكيد: {e}")
            # المتابعة بدون تخصيص شريط العنوان


class SaleSuccessDialog(QDialog):
    """نافذة نجاح متطورة للمبيعات مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="نجح", message="", icon="✅"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.2),
                    stop:0.5 rgba(5, 150, 105, 0.3),
                    stop:1 rgba(4, 120, 87, 0.2));
                border: 2px solid rgba(16, 185, 129, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة النجاح
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # رسالة التأكيد
        question_label = QLabel("✅ تمت العملية بنجاح!")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #10b981;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار النجاح: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان للنجاح: {e}")
            # المتابعة بدون تخصيص شريط العنوان


class SaleErrorDialog(QDialog):
    """نافذة خطأ متطورة للمبيعات مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="خطأ", message="", icon="❌"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة الخطأ
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # رسالة التأكيد
        question_label = QLabel("❌ حدث خطأ!")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #ef4444;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("❌ إغلاق")
        self.style_advanced_button(ok_button, 'danger')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الخطأ: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان للخطأ: {e}")
            # المتابعة بدون تخصيص شريط العنوان


class DeleteSaleDialog(QDialog):
    """نافذة حذف المبيعة مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, sale_data=None):
        super().__init__(parent)
        self.sale_data = sale_data
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("🛍️ حذف - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("💰 حذف المبيعة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات المبيعة مضغوطة
        if self.sale_data:
            info_text = f"💰 {self.sale_data.get('item', 'عنصر')[:15]}{'...' if len(self.sale_data.get('item', '')) > 15 else ''}"
            if self.sale_data.get('quantity'):
                info_text += f" | 📊 {self.sale_data.get('quantity')}"
            if self.sale_data.get('price'):
                info_text += f" | 💰 {self.sale_data.get('price')}"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("💰 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الحذف: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "💰")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان للحذف: {e}")
            # المتابعة بدون تخصيص شريط العنوان

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)


class SalesWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة المبيعات مع التحديد المتعدد"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        # متغير لتتبع ما إذا كان المستخدم قد تفاعل مع الجدول
        self.user_interacted_with_table = False
        self.init_ui()

        # تحميل البيانات تلقائياً عند بدء التشغيل
        QTimer.singleShot(100, self.refresh_data)

    def _setup_custom_wheel_event(self):
        """إعداد معالج التمرير المخصص"""
        def sales_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.sales_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.sales_table, event)

        self.sales_table.wheelEvent = sales_wheelEvent

    def _create_table_item(self, text, default="No Data"):
        """إنشاء عنصر جدول منسق"""
        display_text = text if text and str(text).strip() else default
        item = QTableWidgetItem(str(display_text))
        item.setTextAlignment(Qt.AlignCenter)
        if display_text == default:
            item.setForeground(QColor("#ef4444"))
        return item

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تقليل المساحات الفارغة لاستغلال المساحة للجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(3)  # تقليل المسافات من 8 إلى 3

        # العنوان الرئيسي مطابق للفواتير
        title_label = QLabel("🛍️ إدارة المبيعات المتطورة - نظام شامل ومتقدم لإدارة المبيعات مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إطار البحث
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # البحث مطابق للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعميل، رقم الفاتورة أو المنتج...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 7px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 36px;
                min-height: 32px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        # فلتر الحالة مطابق للفواتير
        status_label = QLabel("📊 حالة:")
        status_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        status_label.setAlignment(Qt.AlignCenter)

        # زر البحث - مطابق للعملاء
        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                font-size: 18px;
                font-weight: 900;
                padding: 8px 12px;
                min-width: 45px;
                max-width: 45px;
                min-height: 34px;
                max-height: 38px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(139, 92, 246, 0.9),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 3px solid rgba(59, 130, 246, 0.8);
                transform: translateY(0px);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            }
        """)
        search_button.clicked.connect(self.filter_sales)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير
        self.create_custom_status_filter()


        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit, 1)
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter_frame)
        search_layout.addStretch()

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        search_frame.setLayout(top_container)
        main_layout.addWidget(search_frame)

        # جدول المبيعات
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(14)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للحقول الجديدة
        # الترتيب الجديد: التاريخ بعد الحالة
        headers = [
            "🔢 ID",
            "🔢 رقم المبيعة",
            "🧑‍💼 العميل",
            "📦 المنتج",
            "🔢 الكمية",
            "💲 سعر الوحدة",
            "💰 الإجمالي",
            "💵 المدفوع",
            "🏷️ الخصم",
            "📊 الضريبة",
            "🎯 الحالة",
            "📅 التاريخ",
            "💳 طريقة الدفع",
            "📋 ملاحظات"
        ]
        self.sales_table.setHorizontalHeaderLabels(headers)

        # إخفاء الأعمدة المطلوبة (البيانات تُحفظ كاملة ولكن لا تُعرض)
        self.sales_table.setColumnHidden(1, True)   # إخفاء عمود "رقم المبيعة"
        self.sales_table.setColumnHidden(8, True)   # إخفاء عمود "الخصم"
        self.sales_table.setColumnHidden(9, True)   # إخفاء عمود "الضريبة"
        self.sales_table.setColumnHidden(12, True)  # إخفاء عمود "طريقة الدفع"

        # إعداد التحديد للسطر كاملاً مع التحديد المتعدد
        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setSelectionMode(QAbstractItemView.ExtendedSelection)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.sales_table)

        # ربط معالج التحديد
        self.sales_table.itemSelectionChanged.connect(self.on_sales_selection_changed)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.sales_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.sales_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.sales_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.sales_table.verticalHeader().setDefaultSectionSize(45)
        self.sales_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_sales_table()

        # إعدادات عرض الأعمدة مع التكيف الكامل مع الشاشة (جميع الأعمدة متكيفة)
        # جميع الأعمدة تتكيف مع حجم الشاشة
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # رقم المبيعة (مخفي)
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # العميل
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # الكمية
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # سعر الوحدة
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # الإجمالي
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # المدفوع
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # الخصم (مخفي)
        header.setSectionResizeMode(9, QHeaderView.Stretch)  # الضريبة (مخفي)
        header.setSectionResizeMode(10, QHeaderView.Stretch) # الحالة
        header.setSectionResizeMode(11, QHeaderView.Stretch) # التاريخ
        header.setSectionResizeMode(12, QHeaderView.Stretch) # طريقة الدفع (مخفي)
        header.setSectionResizeMode(13, QHeaderView.Stretch) # الملاحظات

        # مع التكيف الكامل، جميع الأعمدة تتوسع تلقائياً حسب حجم الشاشة
        # لا حاجة لتحديد عرض ثابت للأعمدة

        # إضافة معالج التمرير المخصص
        self._setup_custom_wheel_event()

        main_layout.addWidget(self.sales_table, 1)

        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 0, 0, 0)  # مطابق للعملاء
        buttons_layout.setSpacing(4)  # مطابق للعملاء

        # أزرار العمليات مثل المخزون مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة مبيعة")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_sale)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_sale)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_sale)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')  # سماوي للتفاصيل - مطابق للعملاء والموردين
        self.view_button.clicked.connect(self.view_sale_details)  # ربط مباشر بدون قائمة
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)  # أسود للتصدير - مطابق للعملاء والموردين
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير المتقدمة مطابقة لجميع الأقسام
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 15px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: center;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(lambda: self.safe_export_call('export_excel_advanced'))
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(lambda: self.safe_export_call('export_csv_advanced'))
        export_menu.addAction(csv_action)

        pdf_action = QAction("📋 تصدير PDF تفصيلي", self)
        pdf_action.triggered.connect(lambda: self.safe_export_call('export_pdf_advanced'))
        export_menu.addAction(pdf_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        customer_report_action = QAction("👥 تقرير العملاء", self)
        customer_report_action.triggered.connect(lambda: self.safe_export_call('export_customer_report'))
        export_menu.addAction(customer_report_action)

        revenue_analysis_action = QAction("💰 تحليل الإيرادات", self)
        revenue_analysis_action.triggered.connect(lambda: self.safe_export_call('export_revenue_analysis'))
        export_menu.addAction(revenue_analysis_action)

        monthly_report_action = QAction("📅 التقرير الشهري", self)
        monthly_report_action.triggered.connect(lambda: self.safe_export_call('export_monthly_report'))
        export_menu.addAction(monthly_report_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(lambda: self.safe_export_call('export_custom'))
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(lambda: self.safe_export_call('export_backup'))
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(lambda: self.safe_export_call('restore_backup'))
        export_menu.addAction(restore_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'cyan')  # مطابق لزر عرض التفاصيل
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()

        # إجمالي المبيعات مطور ليتشابه مع الفواتير
        self.total_sales_label = QLabel("إجمالي المبيعات: 0 | القيمة الإجمالية: 0.00 ج.م")
        self.total_sales_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_sales_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.view_button)
        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(self.statistics_button)
        buttons_layout.addWidget(self.columns_visibility_button)
        buttons_layout.addWidget(self.total_sales_label)

        # إنشاء حاوي عمودي للتوسيط الحقيقي - مطابق للعملاء
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(buttons_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # تعيين التخطيط للإطار السفلي
        buttons_frame.setLayout(bottom_container)
        main_layout.addWidget(buttons_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        # تهيئة حالة الأزرار (جميع الأزرار مفعلة ومنيرة في البداية)
        QTimer.singleShot(100, self.initialize_button_states)

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث
            self.search_edit.textChanged.connect(self.filter_sales)

            # ربط خاصية النقر المزدوج للتعديل مطابق للعملاء
            self.sales_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

            # ربط حدث تغيير التحديد - مطابق للمشاريع
            self.sales_table.itemSelectionChanged.connect(self.on_sales_selection_changed)

        except Exception as e:
            print(f"❌ خطأ في ربط أحداث المبيعات: {str(e)}")
            print(f"تفاصيل الخطأ: {e}")

    def safe_export_call(self, method_name):
        """استدعاء آمن لدوال التصدير مع تسجيل مفصل"""
        try:
            method = getattr(self, method_name, None)
            if method and callable(method):
                print(f"تنفيذ دالة التصدير: {method_name}")
                method()
                print(f"تم تنفيذ {method_name} بنجاح")
            else:
                print(f"تحذير: دالة التصدير {method_name} غير متاحة")
                self.show_warning_message(f"ميزة {method_name} غير متاحة حالياً\nيرجى التحقق من التحديثات")
        except Exception as e:
            print(f"خطأ في تنفيذ دالة التصدير {method_name}: {e}")
            self.show_error_message(f"حدث خطأ في {method_name}: {str(e)}")

    # دوال التصدير العادية
    def export_to_excel(self):
        """تصدير المبيعات إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير المبيعات إلى CSV"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", f"المبيعات_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).order_by(Sale.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التصدير
                    writer.writerow(['تصدير المبيعات'])
                    writer.writerow([f'تاريخ التصدير: {format_datetime_for_export()}'])
                    writer.writerow([f'إجمالي المبيعات: {len(sales)}'])
                    writer.writerow([])

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المتبقي', 'الحالة'])

                    # كتابة البيانات
                    for sale in sales:
                        client_name = sale.client.name if sale.client else "غير محدد"
                        date_str = sale.date.strftime("%Y-%m-%d") if sale.date else ""

                        writer.writerow([
                            sale.id,
                            client_name,
                            date_str,
                            sale.total_amount or 0,
                            sale.paid_amount or 0,
                            (sale.total_amount or 0) - (sale.paid_amount or 0),
                            sale.status or ""
                        ])

                self.show_success_message(f"تم تصدير المبيعات بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير: {str(e)}")

    # دوال التصدير المتقدمة
    def export_excel_advanced(self):
        """تصدير Excel متقدم للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", f"مبيعات_excel_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تصدير Excel متقدم للمبيعات'])
                    writer.writerow([f'تاريخ التصدير: {format_datetime_for_export()}'])
                    writer.writerow([f'إجمالي المبيعات: {len(sales)}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['الرقم', 'رقم المبيعة', 'العميل', 'التاريخ', 'الإجمالي', 'المدفوع', 'الخصم', 'الضريبة', 'الحالة', 'طريقة الدفع', 'الملاحظات'])

                    # البيانات
                    for sale in sales:
                        client_name = sale.client.name if sale.client else 'عميل نقدي'
                        date_str = sale.date.strftime('%Y-%m-%d') if sale.date else 'غير محدد'

                        writer.writerow([
                            sale.id,
                            sale.sale_number or 'غير محدد',
                            client_name,
                            date_str,
                            f"{sale.total_amount:.2f}" if sale.total_amount else '0.00',
                            f"{sale.paid_amount:.2f}" if sale.paid_amount else '0.00',
                            f"{sale.discount_amount:.2f}" if sale.discount_amount else '0.00',
                            f"{sale.tax_amount:.2f}" if sale.tax_amount else '0.00',
                            sale.status or 'معلق',
                            sale.payment_method or 'نقدي',
                            sale.notes or 'لا توجد ملاحظات'
                        ])

                self.show_success_message(f"تم تصدير Excel بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير Excel: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", f"مبيعات_csv_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير مع إحصائيات
                    writer.writerow(['تصدير CSV شامل للمبيعات'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])

                    # إحصائيات سريعة
                    total_amount = sum(sale.total_amount or 0 for sale in sales)
                    paid_amount = sum(sale.paid_amount or 0 for sale in sales)
                    completed_sales = len([s for s in sales if s.status == 'completed'])

                    writer.writerow([f'إجمالي المبلغ: {total_amount:,.2f} جنيه'])
                    writer.writerow([f'المبلغ المدفوع: {paid_amount:,.2f} جنيه'])
                    writer.writerow([f'المبيعات المكتملة: {completed_sales}'])
                    writer.writerow([])

                    # رؤوس الأعمدة التفصيلية
                    writer.writerow(['الرقم', 'رقم المبيعة', 'العميل', 'التاريخ', 'الإجمالي', 'المدفوع', 'المتبقي', 'الحالة', 'طريقة الدفع', 'الملاحظات'])

                    # البيانات التفصيلية
                    for sale in sales:
                        client_name = sale.client.name if sale.client else 'عميل نقدي'
                        date_str = sale.date.strftime('%Y-%m-%d') if sale.date else 'غير محدد'
                        remaining = (sale.total_amount or 0) - (sale.paid_amount or 0)

                        writer.writerow([
                            sale.id,
                            sale.sale_number or 'غير محدد',
                            client_name,
                            date_str,
                            f"{sale.total_amount:.2f}" if sale.total_amount else '0.00',
                            f"{sale.paid_amount:.2f}" if sale.paid_amount else '0.00',
                            f"{remaining:.2f}",
                            sale.status or 'معلق',
                            sale.payment_method or 'نقدي',
                            sale.notes or 'لا توجد ملاحظات'
                        ])

                self.show_success_message(f"تم تصدير CSV بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير CSV: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للمبيعات"""
        self.show_warning_message("ميزة تصدير PDF المتقدم غير متاحة حالياً")

    def export_custom(self):
        """تصدير مخصص للمبيعات مطابق تماماً لنافذة العملاء"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QPushButton, QLabel, QWidget

            # إنشاء نافذة مطابقة لنافذة الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("📊 تصدير مخصص - المبيعات")
            dialog.setModal(True)
            dialog.resize(500, 650)

            # تطبيق نفس خلفية نافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # تخصيص شريط العنوان
            self.customize_title_bar_for_dialog(dialog)

            layout = QVBoxLayout(dialog)
            layout.setSpacing(8)
            layout.setContentsMargins(20, 15, 20, 15)

            # عنوان النافذة مطابق لنافذة الإحصائيات
            title_label = QLabel("📊 اختر البيانات المراد تصديرها")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 8px;
                    margin-bottom: 10px;
                }
            """)
            layout.addWidget(title_label)

            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def customize_title_bar_for_dialog(self, dialog):
        """تخصيص شريط العنوان للنافذة"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(dialog)
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان للتصدير: {e}")
            # المتابعة بدون تخصيص شريط العنوان

    def export_customer_report(self):
        """تقرير العملاء المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv
            from sqlalchemy import func

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير العملاء", f"تقرير_عملاء_مبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # تجميع البيانات حسب العميل
                customer_stats = self.session.query(
                    Sale.client_id,
                    func.count(Sale.id).label('sales_count'),
                    func.sum(Sale.total_amount).label('total_amount')
                ).group_by(Sale.client_id).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير العملاء المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['اسم العميل', 'عدد المبيعات', 'إجمالي المبلغ', 'متوسط المبيعة', 'هاتف العميل'])

                    # البيانات
                    for stat in customer_stats:
                        client = self.session.query(Client).filter_by(id=stat.client_id).first() if stat.client_id else None
                        client_name = client.name if client else 'عميل نقدي'
                        client_phone = client.phone if client else 'غير متاح'
                        avg_sale = stat.total_amount / stat.sales_count if stat.sales_count > 0 else 0

                        writer.writerow([
                            client_name,
                            stat.sales_count,
                            f"{stat.total_amount:.2f}" if stat.total_amount else "0.00",
                            f"{avg_sale:.2f}",
                            client_phone
                        ])

                self.show_success_message(f"تم إنشاء تقرير العملاء بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء تقرير العملاء: {str(e)}")

    def export_revenue_analysis(self):
        """تحليل الإيرادات المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime, timedelta
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تحليل الإيرادات", f"تحليل_ايرادات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تحليل الإيرادات المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تحليل زمني
                    now = datetime.now()
                    last_month = now - timedelta(days=30)
                    last_week = now - timedelta(days=7)

                    monthly_sales = [s for s in sales if s.date and s.date >= last_month.date()]
                    weekly_sales = [s for s in sales if s.date and s.date >= last_week.date()]

                    monthly_revenue = sum(s.total_amount or 0 for s in monthly_sales)
                    weekly_revenue = sum(s.total_amount or 0 for s in weekly_sales)
                    total_revenue = sum(s.total_amount or 0 for s in sales)

                    writer.writerow(['التحليل الزمني'])
                    writer.writerow(['الفترة', 'عدد المبيعات', 'إجمالي الإيرادات'])
                    writer.writerow(['آخر أسبوع', len(weekly_sales), f"{weekly_revenue:.2f}"])
                    writer.writerow(['آخر شهر', len(monthly_sales), f"{monthly_revenue:.2f}"])
                    writer.writerow(['الإجمالي', len(sales), f"{total_revenue:.2f}"])

                self.show_success_message(f"تم إنشاء تحليل الإيرادات بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تحليل الإيرادات: {str(e)}")

    def export_monthly_report(self):
        """التقرير الشهري للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv
            from collections import defaultdict

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير الشهري", f"تقرير_شهري_مبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['التقرير الشهري للمبيعات'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تجميع البيانات حسب الشهر
                    monthly_data = defaultdict(lambda: {'count': 0, 'total': 0, 'paid': 0})

                    for sale in sales:
                        if sale.date:
                            month_key = sale.date.strftime('%Y-%m')
                            monthly_data[month_key]['count'] += 1
                            monthly_data[month_key]['total'] += sale.total_amount or 0
                            monthly_data[month_key]['paid'] += sale.paid_amount or 0

                    # رؤوس الأعمدة
                    writer.writerow(['الشهر', 'عدد المبيعات', 'إجمالي المبلغ', 'المبلغ المدفوع', 'المتبقي'])

                    # البيانات
                    for month, data in sorted(monthly_data.items()):
                        remaining = data['total'] - data['paid']
                        writer.writerow([
                            month,
                            data['count'],
                            f"{data['total']:.2f}",
                            f"{data['paid']:.2f}",
                            f"{remaining:.2f}"
                        ])

                self.show_success_message(f"تم إنشاء التقرير الشهري بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء التقرير الشهري: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_مبيعات_{format_datetime_for_filename()}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                sales = self.session.query(Sale).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(sales),
                        'backup_type': 'sales_full_backup',
                        'version': '1.0'
                    },
                    'sales': []
                }

                for sale in sales:
                    sale_data = {
                        'id': sale.id,
                        'sale_number': sale.sale_number,
                        'client_id': sale.client_id,
                        'client_name': sale.client.name if sale.client else None,
                        'date': sale.date.isoformat() if sale.date else None,
                        'total_amount': float(sale.total_amount) if sale.total_amount else 0.0,
                        'paid_amount': float(sale.paid_amount) if sale.paid_amount else 0.0,
                        'discount_amount': float(sale.discount_amount) if sale.discount_amount else 0.0,
                        'tax_amount': float(sale.tax_amount) if sale.tax_amount else 0.0,
                        'status': sale.status,
                        'payment_method': sale.payment_method,
                        'notes': sale.notes,
                        'created_at': sale.created_at.isoformat() if sale.created_at else datetime.now().isoformat()
                    }
                    backup_data['sales'].append(sale_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(sales)} مبيعة")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية مطابق للعملاء"""
        try:
            self.edit_sale()
        except Exception as e:
            pass  # خطأ في النقر المزدوج

    def filter_sales(self):
        """تصفية المبيعات"""
        search_text = self.search_edit.text().lower()
        status_filter = getattr(self, 'current_status_value', 'all')
        
        for row in range(self.sales_table.rowCount()):
            show_row = True
            
            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.sales_table.columnCount()):
                    item = self.sales_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # فلترة الحالة
            if status_filter != "all":
                status_item = self.sales_table.item(row, 7)
                if status_item and status_filter not in status_item.text():
                    show_row = False
            
            self.sales_table.setRowHidden(row, not show_row)
        
        # تحديث الملخص بعد التصفية
        self.update_summary()



    def add_sale(self):
        """إضافة مبيعة جديدة"""
        try:
            dialog = SaleDialog(self, None, self.session)
            if dialog.exec_() == QDialog.Accepted:
                self.show_success_message("تم إضافة المبيعة بنجاح")
                self.refresh_data()  # تحديث الجدول بعد الإضافة
        except Exception as e:
            self.show_error_message(f"حدث خطأ في إضافة المبيعة: {str(e)}")

    def edit_sale(self):
        """تعديل مبيعة"""
        safe_edit_item(self, self.sales_table, Sale, SaleDialog, self.session, "مبيعة")

    def save_sale_changes(self, row, invoice, customer, date, product, quantity, unit_price, total, status, notes, dialog):
        """حفظ تعديلات المبيعة"""
        try:
            # تحديث البيانات في الجدول
            self.sales_table.setItem(row, 0, QTableWidgetItem(invoice))
            self.sales_table.setItem(row, 1, QTableWidgetItem(customer))
            self.sales_table.setItem(row, 2, QTableWidgetItem(date))
            self.sales_table.setItem(row, 3, QTableWidgetItem(product))
            self.sales_table.setItem(row, 4, QTableWidgetItem(quantity))
            self.sales_table.setItem(row, 5, QTableWidgetItem(unit_price))
            self.sales_table.setItem(row, 6, QTableWidgetItem(total))
            self.sales_table.setItem(row, 7, QTableWidgetItem(status))
            self.sales_table.setItem(row, 8, QTableWidgetItem(notes))

            # تطبيق التنسيق
            for col in range(9):
                item = self.sales_table.item(row, col)
                if item:
                    item.setFont(QFont("Arial", 10, QFont.Bold))
                    item.setForeground(QColor("#000000"))

                    # تلوين حسب الحالة
                    if col == 7:  # عمود الحالة
                        if status == "مكتمل":
                            item.setBackground(QColor("#d1fae5"))
                        elif status == "معلق":
                            item.setBackground(QColor("#fef3c7"))
                        elif status == "ملغي":
                            item.setBackground(QColor("#fee2e2"))

            self.show_success_message("تم حفظ التعديلات بنجاح!")
            dialog.close()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في حفظ التعديلات: {str(e)}")

    def delete_selected_items(self):
        """حذف المبيعات المحددة مع المنطق المحاسبي"""
        try:
            self.update_selected_items()
            if not self.selected_items:
                return

            count = len(self.selected_items)
            if count == 1:
                self.delete_sale()
            else:
                if show_confirmation_message("تأكيد الحذف", f"هل تريد حذف {count} مبيعة؟"):
                    try:
                        for item_id in self.selected_items:
                            sale = self.session.query(Sale).get(item_id)
                            if sale:
                                # إعادة المبلغ لرصيد العميل عند الحذف (إلغاء الدين)
                                if sale.client_id:
                                    from database import Client
                                    client = self.session.query(Client).get(sale.client_id)
                                    if client:
                                        client.balance = (client.balance or 0) + sale.total_amount

                                self.session.delete(sale)

                        self.session.commit()
                        self.refresh_data()
                        self.show_success_message(f"تم حذف {count} مبيعة بنجاح وإعادة المبالغ إلى أرصدة العملاء")
                    except Exception as e:
                        self.session.rollback()
                        self.show_error_message(f"فشل في حذف المبيعات: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف المبيعات: {str(e)}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية للمبيعات"""
        try:
            menu = QMenu(self)

            single_actions = [
                ("✏️ تعديل", self.edit_sale),
                ("👁️ عرض التفاصيل", self.view_sale_details),
                ("🗑️ حذف", self.delete_sale)
            ]

            multi_actions = [
                ("🗑️ حذف {count} مبيعة", self.delete_selected_items)
            ]

            self.create_context_menu_actions(menu, single_actions, multi_actions)
            menu.exec_(self.sales_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في عرض القائمة السياقية للمبيعات: {e}")
            # عرض قائمة بسيطة كبديل
            try:
                menu = QMenu(self)
                menu.addAction("✏️ تعديل", self.edit_sale)
                menu.addAction("🗑️ حذف", self.delete_sale)
                menu.exec_(self.sales_table.mapToGlobal(position))
            except:
                print("فشل في عرض القائمة البديلة للمبيعات")

    def delete_sale(self):
        """حذف مبيعة مع نافذة تأكيد متطورة والمنطق المحاسبي"""
        try:
            current_row = self.sales_table.currentRow()
            if current_row < 0:
                self.show_warning_message("يرجى اختيار مبيعة للحذف")
                return

            # الحصول على معرف المبيعة من البيانات المخفية
            sale_id_item = self.sales_table.item(current_row, 0)
            if not sale_id_item:
                self.show_error_message("لا يمكن تحديد المبيعة المحددة")
                return

            sale_id = sale_id_item.data(Qt.UserRole)
            if not sale_id:
                self.show_error_message("معرف المبيعة غير صحيح")
                return

            # الحصول على بيانات المبيعة من قاعدة البيانات
            sale = self.session.query(Sale).get(sale_id)
            if not sale:
                self.show_error_message("المبيعة غير موجودة في قاعدة البيانات")
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            sale_data = {
                'sale_number': sale.sale_number,
                'client': sale.client.name if sale.client else "غير محدد",
                'total_amount': sale.total_amount
            }

            dialog = DeleteSaleDialog(self, sale_data)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # رسالة تأكيد العميل
                    client_message = ""

                    # إعادة المبلغ لرصيد العميل (إلغاء الدين)
                    if sale.client_id:
                        from database import Client
                        client = self.session.query(Client).get(sale.client_id)
                        if client:
                            old_client_balance = client.balance or 0
                            client.balance = old_client_balance + sale.total_amount  # إضافة المبلغ لرصيد العميل
                            client_message = (
                                f"تم إضافة {sale.total_amount:,.2f} جنيه لرصيد العميل {client.name}\n"
                                f"رصيد العميل السابق: {old_client_balance:,.2f} جنيه\n"
                                f"رصيد العميل الجديد: {client.balance:,.2f} جنيه"
                            )

                    # حذف المبيعة من قاعدة البيانات
                    self.session.delete(sale)
                    self.session.commit()

                    # إظهار رسالة نجاح شاملة
                    success_message = f"تم حذف المبيعة '{sale.sale_number}' بنجاح\n"
                    if client_message:
                        success_message += f"\n{client_message}"

                    self.show_success_message(success_message.strip())

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف المبيعة: {str(e)}")
        except ValueError:
            self.show_error_message("معرف المبيعة غير صحيح")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف المبيعة: {str(e)}")

    def get_sale_data_from_row(self, row):
        """استخراج بيانات المبيعة من الصف المحدد"""
        try:
            if row < 0 or row >= self.sales_table.rowCount():
                return None

            # استخراج البيانات من الجدول
            client = self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else ""
            item = self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else ""
            quantity = self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else ""
            price = self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else ""

            return {
                'client': client,
                'item': item,
                'quantity': quantity,
                'price': price
            }
        except Exception as e:
            return None

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مشابهة لنوافذ البرنامج"""
        dialog = WarningDialog(self, message)
        dialog.exec_()

    def add_watermark_to_sales_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.sales_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.sales_table.viewport())
                paint_watermark(painter, self.sales_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.sales_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.sales_table.viewport().update()
        self.sales_table.repaint()

    def on_sales_selection_changed(self):
        """معالج تحديد المبيعات مطابق للمشاريع"""
        try:
            print("🚨 تم استدعاء معالج تحديد المبيعات!")

            # تسجيل أن المستخدم تفاعل مع الجدول
            self.user_interacted_with_table = True
            print("👆 المستخدم تفاعل مع الجدول - سيتم تطبيق خاصية الإغلاق")

            self.update_selected_sales_list()
            self.update_button_states()

        except Exception as e:
            print(f"تحذير: فشل في معالجة تحديد المبيعات: {e}")
            # المتابعة بدون معالجة التحديد

    def update_selected_sales_list(self):
        """تحديث قائمة المبيعات المحددة مطابق للمشاريع"""
        try:
            if not hasattr(self, 'selected_sales'):
                self.selected_sales = []

            self.selected_sales = []
            selected_items = self.sales_table.selectedItems()

            # جمع معرفات المبيعات المحددة
            selected_rows = set()
            for item in selected_items:
                selected_rows.add(item.row())

            for row in selected_rows:
                id_item = self.sales_table.item(row, 0)  # عمود ID
                if id_item:
                    # استخدام البيانات المخفية للحصول على الـ ID الفعلي
                    sale_id = id_item.data(Qt.UserRole)
                    if sale_id:
                        self.selected_sales.append(sale_id)

        except Exception as e:
            print(f"تحذير: فشل في تحديث قائمة المبيعات المحددة: {e}")
            # المتابعة بدون تحديث القائمة

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مطابق للمشاريع"""
        try:
            # إذا لم يتفاعل المستخدم مع الجدول بعد، لا نغير حالة الأزرار
            if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                print("🔥 المستخدم لم يتفاعل مع الجدول بعد - الأزرار تبقى مفعلة")
                return

            if not hasattr(self, 'selected_sales'):
                self.selected_sales = []

            selected_count = len(self.selected_sales)
            has_selection = selected_count > 0
            has_single_selection = selected_count == 1



            # الأزرار التي تحتاج تحديد واحد فقط
            self.set_button_visibility(self.edit_button, has_single_selection)
            self.set_button_visibility(self.view_button, has_single_selection)

            # الأزرار التي تعمل مع التحديد المتعدد
            self.set_button_visibility(self.delete_button, has_selection)

            # الأزرار المتاحة دائماً
            self.set_button_visibility(self.add_button, True)  # زر الإضافة متاح دائماً

            # تحديث نص زر الحذف
            if has_selection:
                if selected_count > 1:
                    self.delete_button.setText(f"🗑️ حذف ({selected_count})")
                else:
                    self.delete_button.setText("🗑️ حذف")

        except Exception as e:
            print(f"تحذير: فشل في تحديث حالة الأزرار: {e}")
            # المتابعة بدون تحديث حالة الأزرار

    def set_button_visibility(self, button, visible):
        """تعيين رؤية الزر مع الحفاظ على الألوان الأصلية - طريقة مبسطة مطابقة للمشاريع"""
        try:
            if button:
                # التحقق من أن المستخدم تفاعل مع الجدول قبل تطبيق التأثيرات
                if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                    # إذا لم يتفاعل المستخدم بعد، نبقي الزر مفعلاً ومنيراً
                    button.setEnabled(True)
                    button.setGraphicsEffect(None)
                    return

                button.setEnabled(visible)

                # طريقة أبسط وأكثر أماناً لتطبيق الشفافية
                if visible:
                    # إزالة أي تأثير شفافية
                    button.setGraphicsEffect(None)
                else:
                    # تطبيق تأثير الشفافية مع الحفاظ على الألوان - 50%
                    from PyQt5.QtWidgets import QGraphicsOpacityEffect
                    opacity_effect = QGraphicsOpacityEffect()
                    opacity_effect.setOpacity(0.5)  # 50% شفافية
                    button.setGraphicsEffect(opacity_effect)

        except Exception as e:
            # في حالة فشل التأثير، استخدم الطريقة البسيطة
            if button:
                button.setEnabled(True)  # نبقيه مفعلاً في حالة الخطأ

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة مطابق للمشاريع"""
        try:
            print("🔧 بدء تهيئة حالة أزرار المبيعات...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة مبيعة"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.export_button, "📤 تصدير ▼"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    import re
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
        except Exception as e:
            print(f"تحذير: فشل في تهيئة حالة الأزرار: {e}")
            # المتابعة بدون تهيئة الأزرار

    def refresh_data(self):
        """تحديث البيانات من قاعدة البيانات مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # حفظ السطر المحدد حالياً
            current_row = self.sales_table.currentRow()

            # مسح الجدول
            self.sales_table.setRowCount(0)

            # تحميل البيانات من قاعدة البيانات
            sales = self.session.query(Sale).all()

            # ملء الجدول بالبيانات
            for sale in sales:
                self.add_sale_to_table(sale)

            self.update_summary()

            # استعادة التحديد إذا كان ممكناً
            if current_row >= 0 and current_row < self.sales_table.rowCount():
                self.sales_table.selectRow(current_row)

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تحديث البيانات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def add_sale_to_table(self, sale):
        """إضافة مبيعة إلى الجدول"""
        try:
            row = self.sales_table.rowCount()
            self.sales_table.insertRow(row)

            # الحصول على اسم العميل
            client_name = sale.client.name if sale.client else "عميل نقدي"

            # تحويل الحالة وطريقة الدفع إلى العربية للعرض
            status_map = {
                'pending': 'معلق',
                'completed': 'مكتمل',
                'cancelled': 'ملغي',
                'returned': 'مرتجع'
            }
            payment_method_map = {
                'cash': 'نقدي',
                'credit': 'ائتمان',
                'bank_transfer': 'تحويل بنكي',
                'check': 'شيك'
            }

            status_ar = status_map.get(sale.status, sale.status or 'معلق')
            payment_method_ar = payment_method_map.get(sale.payment_method, sale.payment_method or 'نقدي')

            # استخراج معلومات المنتج من الملاحظات
            product_name = "غير محدد"
            quantity = 0.0
            unit_price = 0.0

            if sale.notes:
                notes_lines = sale.notes.split('\n')
                for line in notes_lines:
                    if line.startswith('المنتج:'):
                        product_name = line.replace('المنتج:', '').strip()
                    elif line.startswith('الكمية:'):
                        try:
                            quantity_text = line.replace('الكمية:', '').replace('وحدة', '').strip()
                            quantity = float(quantity_text)
                        except:
                            quantity = 0.0
                    elif line.startswith('سعر الوحدة:'):
                        try:
                            price_text = line.replace('سعر الوحدة:', '').replace('جنيه', '').strip()
                            unit_price = float(price_text)
                        except:
                            unit_price = 0.0

            # إضافة البيانات إلى الجدول بالترتيب الجديد:
            # "🔢 ID", "🔢 رقم المبيعة", "🧑‍💼 العميل", "📦 المنتج", "🔢 الكمية", "💲 سعر الوحدة",
            # "💰 الإجمالي", "💵 المدفوع", "🏷️ الخصم", "📊 الضريبة", "🎯 الحالة", "📅 التاريخ", "💳 طريقة الدفع", "📋 ملاحظات"
            items = [
                str(sale.id),  # ID
                getattr(sale, 'sale_number', None) or "غير محدد",  # رقم المبيعة
                client_name,  # العميل
                product_name,  # المنتج
                f"{quantity:.2f} وحدة" if quantity > 0 else "غير محدد",  # الكمية
                f"{unit_price:.2f} جنيه" if unit_price > 0 else "غير محدد",  # سعر الوحدة
                f"{getattr(sale, 'total_amount', 0):.0f} جنيه",  # الإجمالي
                f"{getattr(sale, 'paid_amount', 0):.0f} جنيه",  # المدفوع
                f"{getattr(sale, 'discount_amount', 0):.0f} جنيه",  # الخصم
                f"{getattr(sale, 'tax_amount', 0):.0f} جنيه",  # الضريبة
                status_ar,  # الحالة
                sale.date.strftime("%Y-%m-%d") if sale.date else "غير محدد",  # التاريخ
                payment_method_ar,  # طريقة الدفع
                getattr(sale, 'notes', None) or "لا توجد ملاحظات"  # الملاحظات
            ]

            # دالة مساعدة لإنشاء العناصر مطابقة للعملاء
            def create_item(text, default="No Data"):
                return self._create_table_item(text, default)

            for col, item_text in enumerate(items):
                if col == 0:  # ID - استخدام رقم متسلسل بدلاً من ID الفعلي
                    sequential_number = row + 1
                    item = QTableWidgetItem(f"🔢 {sequential_number}")
                    item.setTextAlignment(Qt.AlignCenter)
                    # حفظ الـ ID الفعلي كبيانات مخفية للاستخدام في العمليات
                    item.setData(Qt.UserRole, sale.id)
                    item.setForeground(QColor("#000000"))
                elif col == 10:  # عمود الحالة (العمود 10 حسب الترتيب الجديد)
                    item = QTableWidgetItem(f"🎯 {item_text}")
                    item.setTextAlignment(Qt.AlignCenter)
                    # تلوين الحالة
                    if sale.status == "completed":
                        item.setBackground(QColor("#d1fae5"))
                        item.setForeground(QColor("#065f46"))
                    elif sale.status == "pending":
                        item.setBackground(QColor("#fef3c7"))
                        item.setForeground(QColor("#92400e"))
                    elif sale.status == "cancelled":
                        item.setBackground(QColor("#fee2e2"))
                        item.setForeground(QColor("#991b1b"))
                    elif sale.status == "returned":
                        item.setBackground(QColor("#fde2e7"))
                        item.setForeground(QColor("#be185d"))
                else:
                    # باقي الأعمدة مع الأيقونات
                    # الترتيب الجديد: ID, رقم المبيعة, العميل, المنتج, الكمية, سعر الوحدة, الإجمالي, المدفوع, الخصم, الضريبة, الحالة, التاريخ, طريقة الدفع, الملاحظات
                    icons = ["🔢", "🔢", "🧑‍💼", "📦", "🔢", "💲", "💰", "💵", "🏷️", "📊", "🎯", "📅", "💳", "📋"]
                    if col < len(icons) and icons[col]:
                        item = create_item(f"{icons[col]} {item_text}")
                    else:
                        item = create_item(item_text)

                    # تلوين المبالغ المالية
                    if col in [6, 7, 8, 9]:  # الإجمالي، المدفوع، الخصم، الضريبة
                        if col == 6:  # الإجمالي
                            item.setForeground(QColor("#1f2937"))
                        elif col == 7:  # المدفوع
                            item.setForeground(QColor("#059669"))
                        elif col == 8:  # الخصم
                            item.setForeground(QColor("#dc2626"))
                        elif col == 9:  # الضريبة
                            item.setForeground(QColor("#7c2d12"))

                self.sales_table.setItem(row, col, item)

        except Exception as e:
            print(f"تحذير: فشل في إضافة المبيعة للجدول: {e}")
            # المتابعة بدون إضافة هذه المبيعة للجدول



    def update_summary(self):
        """تحديث ملخص المبيعات"""
        try:
            total_count = 0
            total_value = 0.0

            for row in range(self.sales_table.rowCount()):
                if not self.sales_table.isRowHidden(row):
                    total_count += 1
                    total_item = self.sales_table.item(row, 6)  # عمود الإجمالي (العمود 6 حسب الترتيب الجديد)
                    if total_item:
                        # إزالة الأيقونات والنصوص الإضافية واستخراج الرقم فقط
                        text = total_item.text().replace('💰', '').replace('جنيه', '').replace(',', '').strip()
                        # تحسين استخراج الرقم
                        try:
                            import re
                            number_match = re.search(r'[\d.]+', text)
                            if number_match:
                                total_value += float(number_match.group())
                        except (ValueError, AttributeError):
                            # تجاهل أخطاء التحويل - هذا طبيعي للنصوص غير الرقمية
                            continue

            self.total_sales_label.setText(f"إجمالي المبيعات: {total_count} | القيمة الإجمالية: {total_value:.2f} ج.م")

        except Exception as e:
            print(f"❌ خطأ في تحديث ملخص المبيعات: {e}")
            # عرض قيم افتراضية بدلاً من إخفاء الخطأ
            self.total_sales_label.setText("إجمالي المبيعات: خطأ في التحميل | القيمة الإجمالية: خطأ في الحساب")
            if hasattr(self, 'show_error_message'):
                self.show_error_message(f"خطأ في حساب إجمالي المبيعات: {str(e)}")

    def show_statistics(self):
        """عرض نافذة إحصائيات المبيعات"""
        try:
            dialog = SalesStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            self.show_error_message(f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        self.columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        self.columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("🧑‍💼 العميل", 1),
            ("📦 المنتج", 2),
            ("🔢 الكمية", 3),
            ("💰 السعر", 4),
            ("💵 الإجمالي", 5),
            ("📅 التاريخ", 6),
            ("📋 ملاحظات", 7)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            self.columns_menu.addAction(action)

        # إضافة فاصل
        self.columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        self.columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        self.columns_menu.addAction(hide_all_action)

        # حفظ مرجع للقائمة
        columns_menu = self.columns_menu

        # تطبيق تصميم المحاذاة اليمنى للعناصر المحددة
        for action in columns_menu.actions():
            if action.text() and not action.isSeparator():
                # محاذاة النص لليمين مع مسافة كافية
                current_text = action.text()
                right_aligned_text = f"{current_text:>30}"
                action.setText(right_aligned_text)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            self.columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = self.columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            self.columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'sales_table') and self.sales_table:
                if visible:
                    self.sales_table.showColumn(column_index)
                else:
                    self.sales_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"تحذير: فشل في تبديل رؤية العمود: {e}")
            # المتابعة بدون تبديل العمود

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'sales_table') and self.sales_table:
                for i in range(self.sales_table.columnCount()):
                    self.sales_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"تحذير: فشل في إظهار جميع الأعمدة: {e}")
            # المتابعة بدون إظهار الأعمدة

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'sales_table') and self.sales_table:
                for i in range(self.sales_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.sales_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"تحذير: فشل في إخفاء جميع الأعمدة: {e}")
            # المتابعة بدون إخفاء الأعمدة

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#2d2d2d', 'bg_bottom': '#404040',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#1a1a1a', 'pressed_bottom': '#2d2d2d', 'pressed_border': '#1a1a1a',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الواجهة الرئيسية: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def export_to_excel(self):
        """تصدير المبيعات إلى Excel (CSV)"""
        self.export_to_csv()

    def export_to_csv(self):
        """تصدير المبيعات إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ ملف CSV", "المبيعات.csv", "CSV Files (*.csv)")
            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    headers = ['الرقم', 'رقم المبيعة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'الحالة']
                    writer.writerow(headers)

                    for row in range(self.sales_table.rowCount()):
                        row_data = []
                        for col in range(self.sales_table.columnCount()):
                            item = self.sales_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)
                self.show_success_message(f"تم تصدير المبيعات بنجاح إلى:\n{file_path}")
        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير المبيعات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المبيعات", "تقرير_المبيعات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المبيعات</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>💰 تقرير المبيعات</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>رقم المبيعة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                        </tr>
                """

                total_amount = 0
                for row in range(self.sales_table.rowCount()):
                    sale_id = self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else ""
                    sale_number = self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else ""
                    customer = self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else ""
                    date = self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else ""
                    amount_text = self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "0"
                    status = self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""

                    try:
                        amount = float(amount_text.replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        amount = 0

                    html_content += f"""
                        <tr>
                            <td>{sale_id}</td>
                            <td>{sale_number}</td>
                            <td>{customer}</td>
                            <td>{date}</td>
                            <td>{int(amount):,} جنيه</td>
                            <td>{status}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي المبيعات: {int(total_amount):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                self.show_success_message(f"تم تصدير المبيعات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير المبيعات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "المبيعات.json", "JSON Files (*.json)"
            )

            if file_path:
                data = []
                for row in range(self.sales_table.rowCount()):
                    row_data = {
                        'الرقم': self.sales_table.item(row, 0).text() if self.sales_table.item(row, 0) else "",
                        'رقم المبيعة': self.sales_table.item(row, 1).text() if self.sales_table.item(row, 1) else "",
                        'العميل': self.sales_table.item(row, 2).text() if self.sales_table.item(row, 2) else "",
                        'التاريخ': self.sales_table.item(row, 3).text() if self.sales_table.item(row, 3) else "",
                        'المبلغ الإجمالي': self.sales_table.item(row, 4).text() if self.sales_table.item(row, 4) else "",
                        'الحالة': self.sales_table.item(row, 5).text() if self.sales_table.item(row, 5) else ""
                    }
                    data.append(row_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم تصدير المبيعات إلى JSON بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير JSON: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المبيعات.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المبيعات
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                self.show_success_message(f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_sale_details(self):
        """عرض تفاصيل المبيعة المحددة"""
        try:
            selected_row = self.sales_table.currentRow()
            if selected_row < 0:
                self.show_error_message("الرجاء اختيار مبيعة من القائمة")
                return

            # استخراج معرف المبيعة مع التعامل مع الرموز التعبيرية
            id_text = self.sales_table.item(selected_row, 0).text()
            import re
            sale_id = int(re.sub(r'[^\d]', '', id_text))

            # البحث عن المبيعة في قاعدة البيانات
            sale = self.session.query(Sale).get(sale_id)
            if not sale:
                self.show_error_message("لم يتم العثور على المبيعة")
                return

            # إنشاء نافذة المعلومات المتطورة
            info_dialog = SaleInfoDialog(self, sale)
            info_dialog.exec_()

        except Exception as e:
            self.show_error_message(f"فشل في عرض تفاصيل المبيعة: {str(e)}")



    def print_sale_details(self, invoice, customer, date, product, quantity, unit_price, total, status, notes):
        """طباعة تفاصيل المبيعة"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تفاصيل المبيعة", f"تفاصيل_المبيعة_{invoice}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            💰 تفاصيل المبيعة
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ الطباعة: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الطباعة: {QDate.currentDate().toString('hh:mm:ss')}

📋 معلومات المبيعة:
─────────────────────────────────────────────────────────────────────────────
رقم الفاتورة: {invoice}
العميل: {customer}
التاريخ: {date}
المنتج: {product}
الكمية: {quantity}
سعر الوحدة: {unit_price}
الإجمالي: {total}
الحالة: {status}
ملاحظات: {notes}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المبيعات
═══════════════════════════════════════════════════════════════════════════════
""")

                self.show_success_message(f"تم حفظ تفاصيل المبيعة بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في حفظ التفاصيل: {str(e)}")

    def view_sales_history(self):
        """عرض تاريخ المبيعات"""
        try:
            # إنشاء نافذة تاريخ المبيعات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QPushButton, QLabel, QDateEdit, QComboBox
            from PyQt5.QtCore import QDate

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 تاريخ المبيعات")
            dialog.setModal(True)
            dialog.resize(900, 600)

            layout = QVBoxLayout()

            # فلاتر التاريخ
            filter_layout = QHBoxLayout()

            filter_layout.addWidget(QLabel("من تاريخ:"))
            from_date = QDateEdit()
            from_date.setDate(QDate.currentDate().addDays(-30))  # آخر 30 يوم
            from_date.setCalendarPopup(True)
            filter_layout.addWidget(from_date)

            filter_layout.addWidget(QLabel("إلى تاريخ:"))
            to_date = QDateEdit()
            to_date.setDate(QDate.currentDate())
            to_date.setCalendarPopup(True)
            filter_layout.addWidget(to_date)

            filter_layout.addWidget(QLabel("العميل:"))
            customer_filter = QComboBox()
            customer_filter.addItem("جميع العملاء")
            # إضافة العملاء من البيانات الحالية
            customers = set()
            for row in range(self.sales_table.rowCount()):
                customer_item = self.sales_table.item(row, 1)
                if customer_item:
                    customers.add(customer_item.text())
            for customer in sorted(customers):
                customer_filter.addItem(customer)
            filter_layout.addWidget(customer_filter)

            filter_button = QPushButton("🔍 تطبيق الفلتر")
            filter_layout.addWidget(filter_button)

            layout.addLayout(filter_layout)

            # جدول التاريخ
            history_table = QTableWidget()
            history_table.setColumnCount(8)
            history_table.setHorizontalHeaderLabels([
                "التاريخ", "رقم الفاتورة", "العميل", "المنتج",
                "الكمية", "سعر الوحدة", "الإجمالي", "الحالة"
            ])

            # إعداد التحديد للسطر كاملاً
            history_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            history_table.setSelectionMode(QAbstractItemView.SingleSelection)

            # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
            history_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

            # ضبط إعدادات التمرير للتحكم الدقيق
            try:
                scrollbar = history_table.verticalScrollBar()
                if scrollbar:
                    scrollbar.setSingleStep(50)
                    scrollbar.setPageStep(200)
            except Exception:
                pass

            # إضافة معالج التمرير المخصص
            def history_wheelEvent(event):
                try:
                    delta = event.angleDelta().y()
                    if abs(delta) < 120:
                        event.accept()
                        return

                    scrollbar = history_table.verticalScrollBar()
                    if not scrollbar:
                        event.accept()
                        return

                    if delta > 0:
                        scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                    else:
                        scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                    event.accept()
                except Exception:
                    QTableWidget.wheelEvent(history_table, event)

            history_table.wheelEvent = history_wheelEvent

            # تحميل البيانات
            def load_history_data():
                # محاكاة بيانات تاريخية
                history_data = []
                for row in range(self.sales_table.rowCount()):
                    row_data = []
                    for col in [2, 0, 1, 3, 4, 5, 6, 7]:  # إعادة ترتيب الأعمدة
                        item = self.sales_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    history_data.append(row_data)

                # ترتيب حسب التاريخ (الأحدث أولاً)
                history_data.sort(key=lambda x: x[0], reverse=True)

                history_table.setRowCount(len(history_data))
                for row, data in enumerate(history_data):
                    for col, value in enumerate(data):
                        item = QTableWidgetItem(str(value))
                        item.setFont(QFont("Arial", 10, QFont.Bold))

                        # تلوين حسب الحالة
                        if col == 7:  # عمود الحالة
                            if value == "مكتمل":
                                item.setBackground(QColor("#d1fae5"))
                            elif value == "معلق":
                                item.setBackground(QColor("#fef3c7"))
                            elif value == "ملغي":
                                item.setBackground(QColor("#fee2e2"))

                        history_table.setItem(row, col, item)

                # تعديل عرض الأعمدة
                header = history_table.horizontalHeader()
                header.setStretchLastSection(True)
                for i in range(history_table.columnCount()):
                    header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

            # تحميل البيانات الأولية
            load_history_data()

            # ربط زر الفلتر
            filter_button.clicked.connect(load_history_data)

            layout.addWidget(history_table)

            # إحصائيات سريعة
            stats_layout = QHBoxLayout()

            total_sales = history_table.rowCount()
            total_amount = 0
            for row in range(history_table.rowCount()):
                amount_item = history_table.item(row, 6)
                if amount_item:
                    try:
                        amount = float(amount_item.text().replace(',', '').replace('جنيه', '').strip())
                        total_amount += amount
                    except:
                        pass

            stats_label = QLabel(f"📊 إجمالي المبيعات: {total_sales} | 💰 إجمالي المبلغ: {int(total_amount):,} جنيه")
            stats_label.setStyleSheet("font-weight: bold; font-size: 12px; padding: 10px; background-color: #f0f9ff; border-radius: 5px;")
            stats_layout.addWidget(stats_label)

            layout.addLayout(stats_layout)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_history_btn = QPushButton("📤 تصدير التاريخ")
            export_history_btn.clicked.connect(lambda: self.export_sales_history(history_table))
            buttons_layout.addWidget(export_history_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في عرض تاريخ المبيعات: {str(e)}")

    def export_sales_history(self, history_table):
        """تصدير تاريخ المبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtCore import QDate
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تاريخ المبيعات", "تاريخ_المبيعات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = []
                    for col in range(history_table.columnCount()):
                        headers.append(history_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(history_table.rowCount()):
                        row_data = []
                        for col in range(history_table.columnCount()):
                            item = history_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                self.show_success_message(f"تم تصدير تاريخ المبيعات بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التاريخ: {str(e)}")

    def view_customer_info(self):
        """عرض معلومات العميل"""
        selected_row = self.sales_table.currentRow()
        if selected_row < 0:
            self.show_warning_message("الرجاء اختيار مبيعة من القائمة")
            return

        customer = self.sales_table.item(selected_row, 2).text() if self.sales_table.item(selected_row, 2) else ""
        self.show_success_message(f"معلومات العميل: {customer}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = SaleWarningDialog(self, "تحذير", message, "⚠️")
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = SaleSuccessDialog(self, "نجح", message, "✅")
        dialog.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = SaleErrorDialog(self, "خطأ", message, "❌")
        dialog.exec_()

    def show_confirmation_message(self, title, message):
        """إظهار رسالة تأكيد متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = SaleConfirmationDialog(self, title, message, "❓")
        return dialog.exec_() == QDialog.Accepted

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للحالات مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.status_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار
        self.status_left_arrow = QPushButton("▼")
        self.status_left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # النص الحالي
        self.current_status_label = QLabel("جميع الحالات")
        self.current_status_label.setAlignment(Qt.AlignCenter)
        self.current_status_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.status_menu_button = QPushButton("▼")
        self.status_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # إضافة العناصر للتخطيط
        filter_layout.addWidget(self.status_left_arrow, 0)
        filter_layout.addWidget(self.current_status_label, 1)
        filter_layout.addWidget(self.status_menu_button, 0)

        # إنشاء القائمة المنسدلة للحالات
        self.status_menu = QMenu(self)
        self.status_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                margin: 2px 5px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية للحالات مع أيقونات مطابقة للعملاء
        status_options = [
            ("جميع الحالات", None),
            ("🟢 مدفوعة", "مدفوعة"),
            ("🟡 معلقة", "معلقة"),
            ("🔴 ملغية", "ملغية")
        ]

        for text, value in status_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_status_filter(v, t))
            self.status_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.status_menu_button.clicked.connect(self.show_status_menu)
        self.status_left_arrow.clicked.connect(self.show_status_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.status_frame_mouse_press_event
        self.current_status_label.mousePressEvent = self.status_frame_mouse_press_event

        # تعيين القيم الافتراضية
        self.current_status_value = "all"

    def show_status_menu(self):
        """عرض قائمة تصفية الحالات"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.status_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.status_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))
        except Exception as e:
            # عرض القائمة في موقع افتراضي
            self.status_menu.exec_(self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft()))

    def set_status_filter(self, value, text):
        """تعيين تصفية الحالة"""
        self.current_status_value = value
        self.current_status_label.setText(text)
        self.filter_sales()

    def status_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الحالة"""
        self.show_status_menu()


class SaleDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل مبيعة - مطابقة للعملاء والموردين"""

    def __init__(self, parent=None, sale=None, session=None):
        super().__init__(parent)
        self.sale = sale
        self.session = session
        self.parent_widget = parent
        self.is_edit_mode = sale is not None
        self.init_ui()

        # تحميل البيانات في وضع التعديل
        if self.is_edit_mode:
            self.load_sale_data()
        else:
            # حساب الإجمالي الأولي في وضع الإضافة
            self.calculate_total()

    def generate_next_sale_number(self):
        """توليد رقم المبيعة التالي تلقائياً"""
        try:
            # الحصول على آخر رقم مبيعة من قاعدة البيانات
            last_sale = self.session.query(Sale).order_by(Sale.id.desc()).first()
            if last_sale and last_sale.sale_number:
                # استخراج الرقم من آخر مبيعة
                try:
                    # إذا كان الرقم بصيغة SAL-0001
                    if 'SAL-' in last_sale.sale_number:
                        last_number = int(last_sale.sale_number.replace('SAL-', ''))
                        next_number = last_number + 1
                    else:
                        # إذا كان الرقم عادي
                        last_number = int(last_sale.sale_number)
                        next_number = last_number + 1
                except:
                    next_number = 1
            else:
                next_number = 1

            return f"SAL-{next_number:04d}"  # مثال: SAL-0001
        except Exception as e:
            return "SAL-0001"

    def setup_product_completer(self):
        """إعداد الإكمال التلقائي للمنتجات من المخزون مع عرض الخيارات من البداية"""
        try:
            # تحميل جميع المنتجات للإكمال التلقائي
            from database import Inventory
            products = self.session.query(Inventory).all()

            # إنشاء قائمة بأسماء المنتجات مع معلومات إضافية
            product_names = []
            self.product_details = {}  # قاموس لحفظ تفاصيل المنتجات

            for product in products:
                if product.name:
                    # تنسيق اسم المنتج مع معلومات إضافية
                    display_name = f"{product.name}"
                    if product.selling_price:
                        display_name += f" - {product.selling_price:.2f} جنيه"
                    if product.quantity is not None:
                        display_name += f" (متوفر: {product.quantity:.2f})"

                    product_names.append(display_name)
                    # حفظ تفاصيل المنتج للاستخدام لاحقاً
                    self.product_details[product.name] = {
                        'selling_price': product.selling_price or 0.0,
                        'quantity': product.quantity or 0.0,
                        'display_name': display_name
                    }

            # إنشاء الإكمال التلقائي
            from PyQt5.QtCore import QStringListModel
            from PyQt5.QtWidgets import QCompleter
            from PyQt5.QtCore import Qt
            model = QStringListModel(product_names)
            self.product_completer = QCompleter(model)
            self.product_completer.setCaseSensitivity(Qt.CaseInsensitive)
            self.product_completer.setFilterMode(Qt.MatchContains)

            # إعدادات لعرض الخيارات من البداية
            self.product_completer.setCompletionMode(QCompleter.PopupCompletion)
            self.product_completer.setMaxVisibleItems(10)  # عرض 10 خيارات كحد أقصى

            # تخصيص مظهر القائمة المنسدلة
            popup = self.product_completer.popup()
            if popup:
                popup.setStyleSheet("""
                    QListView {
                        background-color: white;
                        border: 2px solid #3b82f6;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: bold;
                        color: #1f2937;
                        selection-background-color: #3b82f6;
                        selection-color: white;
                        padding: 5px;
                    }
                    QListView::item {
                        padding: 8px 12px;
                        border-bottom: 1px solid #e5e7eb;
                        border-radius: 4px;
                        margin: 2px;
                    }
                    QListView::item:hover {
                        background-color: #dbeafe;
                        color: #1e40af;
                    }
                    QListView::item:selected {
                        background-color: #3b82f6;
                        color: white;
                        font-weight: bold;
                    }
                """)

            self.product_edit.setCompleter(self.product_completer)

            # ربط الأحداث
            self.product_edit.textChanged.connect(self.update_price_from_inventory)
            self.product_edit.focusInEvent = self.on_product_focus_in

            # ربط اختيار المنتج من القائمة المنسدلة
            if hasattr(self, 'product_completer'):
                self.product_completer.activated.connect(self.on_product_selected)

            # حفظ قائمة المنتجات للاستخدام لاحقاً
            self.product_names = product_names

        except Exception as e:
            print(f"تحذير: فشل في إعداد الإكمال التلقائي للمنتجات: {e}")
            # المتابعة بدون إكمال تلقائي

    def on_product_focus_in(self, event):
        """عرض جميع الخيارات عند التركيز على حقل المنتج"""
        try:
            # استدعاء الحدث الأصلي أولاً
            QLineEdit.focusInEvent(self.product_edit, event)

            # إذا كان الحقل فارغاً، اعرض جميع الخيارات
            if not self.product_edit.text().strip():
                # تحديث النموذج بجميع المنتجات
                if hasattr(self, 'product_names') and self.product_names:
                    from PyQt5.QtCore import QStringListModel
                    model = QStringListModel(self.product_names)
                    self.product_completer.setModel(model)

                    # إظهار القائمة المنسدلة
                    self.product_completer.complete()

        except Exception as e:
            print(f"تحذير: فشل في عرض خيارات المنتجات: {e}")
            # المتابعة بدون عرض الخيارات

    def on_product_mouse_press(self, event):
        """عرض الخيارات عند النقر على حقل المنتج"""
        try:
            # استدعاء الحدث الأصلي أولاً
            QLineEdit.mousePressEvent(self.product_edit, event)

            # عرض جميع الخيارات
            self.show_all_product_options()

        except Exception as e:
            print(f"تحذير: فشل في معالجة النقر على المنتج: {e}")
            # المتابعة بدون معالجة النقر

    def on_product_key_press(self, event):
        """عرض الخيارات عند الضغط على مفاتيح معينة"""
        try:
            from PyQt5.QtCore import Qt

            # استدعاء الحدث الأصلي أولاً
            QLineEdit.keyPressEvent(self.product_edit, event)

            # عرض الخيارات عند الضغط على السهم لأسفل أو Ctrl+Space
            if (event.key() == Qt.Key_Down or
                (event.key() == Qt.Key_Space and event.modifiers() == Qt.ControlModifier)):
                self.show_all_product_options()

        except Exception as e:
            print(f"تحذير: فشل في معالجة ضغط المفاتيح: {e}")
            # المتابعة بدون معالجة المفاتيح

    def show_all_product_options(self):
        """عرض جميع خيارات المنتجات"""
        try:
            if hasattr(self, 'product_names') and self.product_names:
                from PyQt5.QtCore import QStringListModel

                # تحديث النموذج بجميع المنتجات
                model = QStringListModel(self.product_names)
                self.product_completer.setModel(model)

                # إظهار القائمة المنسدلة
                self.product_completer.complete()

        except Exception as e:
            print(f"تحذير: فشل في عرض جميع خيارات المنتجات: {e}")
            # المتابعة بدون عرض الخيارات

    def update_price_from_inventory(self):
        """تحديث سعر الوحدة تلقائياً عند اختيار منتج من المخزون"""
        try:
            product_name = self.product_edit.text().strip()

            # إذا كان النص فارغاً، عرض جميع الخيارات
            if not product_name:
                self.unit_price_edit.setValue(0.0)
                self.show_all_product_options()
                return

            # إذا كان النص قصيراً (أقل من 3 أحرف)، عرض الخيارات المطابقة
            if len(product_name) < 3:
                self.filter_product_options(product_name)

            # البحث عن المنتج في التفاصيل المحفوظة أولاً
            product_found = False
            if hasattr(self, 'product_details'):
                for product_key, details in self.product_details.items():
                    if product_key.lower() in product_name.lower() or product_name.lower() in product_key.lower():
                        # تحديث سعر الوحدة من التفاصيل المحفوظة
                        self.unit_price_edit.setValue(float(details['selling_price']))
                        # حساب الإجمالي تلقائياً
                        self.calculate_total()
                        product_found = True
                        break

            # إذا لم يتم العثور على المنتج في التفاصيل، ابحث في قاعدة البيانات
            if not product_found:
                from database import Inventory
                product = self.session.query(Inventory).filter(
                    Inventory.name.ilike(f"%{product_name}%")
                ).first()

                if product and product.selling_price:
                    # تحديث سعر الوحدة من المخزون (سعر البيع)
                    self.unit_price_edit.setValue(float(product.selling_price))
                    # حساب الإجمالي تلقائياً
                    self.calculate_total()
                else:
                    # إذا لم يوجد المنتج، اجعل السعر قابل للتعديل يدوياً
                    if self.unit_price_edit.value() == 0.0:
                        self.unit_price_edit.setValue(0.0)

        except Exception as e:
            print(f"تحذير: فشل في تحديث السعر من المخزون: {e}")
            # المتابعة بدون تحديث السعر التلقائي

    def filter_product_options(self, text):
        """تصفية خيارات المنتجات بناءً على النص المكتوب"""
        try:
            if hasattr(self, 'product_names') and self.product_names:
                from PyQt5.QtCore import QStringListModel

                # تصفية المنتجات التي تحتوي على النص
                filtered_products = [
                    product for product in self.product_names
                    if text.lower() in product.lower()
                ]

                # إذا لم توجد نتائج، عرض جميع المنتجات
                if not filtered_products:
                    filtered_products = self.product_names

                # تحديث النموذج بالمنتجات المصفاة
                model = QStringListModel(filtered_products)
                self.product_completer.setModel(model)

                # إظهار القائمة المنسدلة
                self.product_completer.complete()

        except Exception as e:
            print(f"تحذير: فشل في تصفية خيارات المنتجات: {e}")
            # المتابعة بدون تصفية الخيارات

    def on_product_selected(self, selected_text):
        """معالجة اختيار منتج من القائمة المنسدلة"""
        try:
            # استخراج اسم المنتج الأصلي من النص المعروض
            product_name = selected_text.split(' - ')[0].strip()

            # تحديث حقل المنتج باسم المنتج فقط (بدون السعر والكمية)
            self.product_edit.setText(product_name)

            # تحديث السعر تلقائياً
            self.update_price_from_inventory()

        except Exception as e:
            print(f"تحذير: فشل في معالجة اختيار المنتج: {e}")
            # المتابعة بدون معالجة الاختيار

    def extract_product_name(self, display_text):
        """استخراج اسم المنتج من النص المعروض"""
        try:
            # إزالة معلومات السعر والكمية
            if ' - ' in display_text:
                return display_text.split(' - ')[0].strip()
            return display_text.strip()
        except:
            return display_text

    def calculate_total(self):
        """حساب الإجمالي تلقائياً (الكمية × سعر الوحدة)"""
        try:
            quantity = self.quantity_edit.value()
            unit_price = self.unit_price_edit.value()
            total = quantity * unit_price

            # تحديث المبلغ الإجمالي
            if hasattr(self, 'total_amount_edit'):
                self.total_amount_edit.setValue(total)

        except Exception as e:
            print(f"❌ خطأ في حساب المبلغ الإجمالي للمبيعة: {e}")
            # تعيين قيمة افتراضية بدلاً من إخفاء الخطأ
            if hasattr(self, 'total_amount_spinbox'):
                self.total_amount_spinbox.setValue(0)
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"خطأ في حساب المبلغ الإجمالي: {str(e)}")

    def _create_styled_label(self, text, icon, required=False):
        """إنشاء تسمية منسقة"""
        label = QLabel(f"{icon} {text}")
        if required:
            label.setText(f"{icon} {text} *")
        label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 5px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 16px;
                min-width: 120px;
                max-width: 120px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
            }
        """)
        label.setAlignment(Qt.AlignCenter)
        return label

    def init_ui(self):
        # إعداد نافذة الحوار مطابق للعملاء والموردين
        if self.sale:
            self.setWindowTitle("🛍️ تعديل مبيعة - نظام إدارة المبيعات المتطور والشامل")
        else:
            self.setWindowTitle("🛍️ إضافة مبيعة جديدة - نظام إدارة المبيعات المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        self.setModal(True)
        self.resize(700, 850)

        # إنشاء التخطيط الرئيسي مطابق للعملاء والموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # عنوان النافذة مطابق للعملاء والموردين
        title_text = "تعديل بيانات المبيعة" if self.sale else "إضافة مبيعة جديدة"
        title_label = QLabel(f"🛍️ {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
                font-weight: bold;
                font-size: 20px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء نموذج معلومات المبيعة مطابق للعملاء والموردين
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل - مطابقة للعملاء والموردين
        def create_styled_label(text, icon, required=False):
            return self._create_styled_label(text, icon, required)

        # حقل العميل مطابق للعملاء والموردين
        self.client_combo = QComboBox()
        self.client_combo.addItem("-- اختر عميل --", None)
        self.client_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)

        # إضافة العملاء من قاعدة البيانات
        if self.session:
            clients = self.session.query(Client).all()
            for client in clients:
                self.client_combo.addItem(client.name, client.id)

        form_layout.addRow(create_styled_label("العميل", "👤", True), self.client_combo)

        # رقم المبيعة (تلقائي)
        self.sale_number_edit = QLineEdit()
        # توليد رقم تلقائي للمبيعة
        next_sale_number = self.generate_next_sale_number()
        self.sale_number_edit.setText(next_sale_number)
        self.sale_number_edit.setReadOnly(True)  # جعل الحقل للقراءة فقط
        self.sale_number_edit.setPlaceholderText("رقم المبيعة التلقائي")
        self.sale_number_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 240, 240, 0.95),
                    stop:0.2 rgba(235, 235, 235, 0.98),
                    stop:0.4 rgba(230, 230, 230, 0.95),
                    stop:0.6 rgba(235, 235, 235, 0.98),
                    stop:0.8 rgba(240, 240, 240, 0.95),
                    stop:1 rgba(220, 220, 220, 0.9));
                border: 3px solid rgba(156, 163, 175, 0.8);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #374151;
                min-height: 30px;
                min-width: 350px;
                text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1),
                           0 4px 12px rgba(156, 163, 175, 0.3);
            }
        """)
        form_layout.addRow(create_styled_label("رقم المبيعة", "🔢", True), self.sale_number_edit)

        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("التاريخ", "📅"), self.date_edit)

        # اسم المنتج (حقل كتابة مع إكمال تلقائي)
        self.product_edit = QLineEdit()
        self.product_edit.setPlaceholderText("اكتب اسم المنتج أو انقر لعرض الخيارات...")
        self.product_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)

        # إضافة معالج الأحداث للنقر والمفاتيح
        self.product_edit.mousePressEvent = self.on_product_mouse_press
        self.product_edit.keyPressEvent = self.on_product_key_press

        # إعداد الإكمال التلقائي للمنتجات
        self.setup_product_completer()

        # إنشاء تخطيط أفقي للمنتج مع زر قراءة الباركود
        product_layout = QHBoxLayout()
        product_layout.addWidget(self.product_edit)

        # زر قراءة الباركود
        scan_barcode_btn = QPushButton("📷 مسح")
        scan_barcode_btn.setToolTip("قراءة باركود لاختيار المنتج")
        scan_barcode_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:1 rgba(37, 99, 235, 0.8));
                border: 2px solid rgba(59, 130, 246, 0.7);
                border-radius: 8px;
                padding: 8px 12px;
                color: white;
                font-weight: bold;
                min-width: 80px;
                max-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:1 rgba(29, 78, 216, 0.8));
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(1px);
            }
        """)
        scan_barcode_btn.clicked.connect(self.scan_barcode_for_product)
        product_layout.addWidget(scan_barcode_btn)

        form_layout.addRow(create_styled_label("المنتج", "📦", False), product_layout)

        # حقل الكمية
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setRange(0.01, 999999.99)
        self.quantity_edit.setDecimals(2)
        self.quantity_edit.setValue(1.0)
        self.quantity_edit.setSuffix(" وحدة")
        self.quantity_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
        """)
        self.quantity_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow(create_styled_label("الكمية", "🔢", False), self.quantity_edit)

        # حقل سعر الوحدة (يتم ملؤه تلقائياً من المخزون)
        self.unit_price_edit = QDoubleSpinBox()
        self.unit_price_edit.setRange(0.0, 999999.99)
        self.unit_price_edit.setDecimals(2)
        self.unit_price_edit.setValue(0.0)
        self.unit_price_edit.setSuffix(" جنيه")
        self.unit_price_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
            }
        """)
        self.unit_price_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow(create_styled_label("سعر الوحدة", "💰", False), self.unit_price_edit)

        # حقل المبلغ الإجمالي (تلقائي - للقراءة فقط)
        self.total_amount_edit = QDoubleSpinBox()
        self.total_amount_edit.setRange(0, 10000000)
        self.total_amount_edit.setDecimals(2)
        self.total_amount_edit.setSingleStep(100)
        self.total_amount_edit.setSuffix(" جنيه")
        self.total_amount_edit.setReadOnly(True)  # جعل الحقل للقراءة فقط
        self.total_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 240, 240, 0.95),
                    stop:0.2 rgba(235, 235, 235, 0.98),
                    stop:0.4 rgba(230, 230, 230, 0.95),
                    stop:0.6 rgba(235, 235, 235, 0.98),
                    stop:0.8 rgba(240, 240, 240, 0.95),
                    stop:1 rgba(220, 220, 220, 0.9));
                border: 3px solid rgba(34, 197, 94, 0.8);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #16a34a;
                min-height: 30px;
                min-width: 350px;
                text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1),
                           0 4px 12px rgba(34, 197, 94, 0.3);
            }
        """)
        form_layout.addRow(create_styled_label("المبلغ الإجمالي", "💰", True), self.total_amount_edit)

        # المبلغ المدفوع
        self.paid_amount_edit = QDoubleSpinBox()
        self.paid_amount_edit.setRange(0, 10000000)
        self.paid_amount_edit.setDecimals(2)
        self.paid_amount_edit.setSingleStep(100)
        self.paid_amount_edit.setSuffix(" جنيه")
        self.paid_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("المبلغ المدفوع", "💵"), self.paid_amount_edit)

        # مبلغ الخصم
        self.discount_amount_edit = QDoubleSpinBox()
        self.discount_amount_edit.setRange(0, 10000000)
        self.discount_amount_edit.setDecimals(2)
        self.discount_amount_edit.setSingleStep(10)
        self.discount_amount_edit.setSuffix(" جنيه")
        self.discount_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("مبلغ الخصم", "🏷️"), self.discount_amount_edit)

        # مبلغ الضريبة
        self.tax_amount_edit = QDoubleSpinBox()
        self.tax_amount_edit.setRange(0, 10000000)
        self.tax_amount_edit.setDecimals(2)
        self.tax_amount_edit.setSingleStep(10)
        self.tax_amount_edit.setSuffix(" جنيه")
        self.tax_amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("مبلغ الضريبة", "📊"), self.tax_amount_edit)

        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["معلق", "مكتمل", "ملغي", "مرتجع"])
        self.status_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الحالة", "🎯"), self.status_combo)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "ائتمان", "تحويل بنكي", "شيك"])
        self.payment_method_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("طريقة الدفع", "💳"), self.payment_method_combo)

        # حقل الملاحظات مطابق للعملاء والموردين
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل ملاحظات حول المبيعة...")
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 60px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        # خيار الدفع من العميل (اختياري)
        self.payment_checkbox = QCheckBox("تم استلام المبلغ من العميل")
        self.payment_checkbox.setStyleSheet("""
            QCheckBox {
                color: #1f2937;
                font-size: 14px;
                font-weight: bold;
                spacing: 8px;
                padding: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 8px;
            }
            QCheckBox:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.9);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:0.5 rgba(16, 185, 129, 0.8),
                    stop:1 rgba(5, 150, 105, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)
        form_layout.addRow(create_styled_label("حالة الدفع", "💳"), self.payment_checkbox)

        main_layout.addLayout(form_layout)

        # أزرار التحكم - مطابقة للعملاء والموردين (ترتيب صحيح)
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.save_sale)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # ترتيب صحيح: الإلغاء أولاً ثم الحفظ
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان - مطابق للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للمبيعات مطابقة للعملاء والموردين
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            from PyQt5.QtGui import QLinearGradient
            gradient = QLinearGradient(0, 0, 32, 32)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.drawRoundedRect(2, 2, 28, 28, 6, 6)

            # إضافة رمز المبيعات
            painter.setPen(QColor(255, 255, 255))
            font = painter.font()
            font.setPointSize(16)
            font.setBold(True)
            painter.setFont(font)
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "🛍️")

            painter.end()

            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان للحوار: {e}")
            # المتابعة بدون تخصيص شريط العنوان

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - نسخة مبسطة لنافذة الحوار مطابقة للعملاء والموردين"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل مطابق للعملاء والموردين
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الحوار: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def save_sale(self):
        """حفظ بيانات المبيعة"""
        try:
            # جمع البيانات من جميع الحقول
            client_id = self.client_combo.currentData() if hasattr(self, 'client_combo') else None
            sale_number = self.sale_number_edit.text().strip() if hasattr(self, 'sale_number_edit') else ""
            date = self.date_edit.date().toPyDate() if hasattr(self, 'date_edit') else datetime.date.today()
            product_display_text = self.product_edit.text().strip() if hasattr(self, 'product_edit') else ""
            product_name = self.extract_product_name(product_display_text)  # استخراج اسم المنتج الصحيح
            quantity = self.quantity_edit.value() if hasattr(self, 'quantity_edit') else 1.0
            unit_price = self.unit_price_edit.value() if hasattr(self, 'unit_price_edit') else 0.0
            total_amount = self.total_amount_edit.value() if hasattr(self, 'total_amount_edit') else 0.0
            paid_amount = self.paid_amount_edit.value() if hasattr(self, 'paid_amount_edit') else 0.0
            discount_amount = self.discount_amount_edit.value() if hasattr(self, 'discount_amount_edit') else 0.0
            tax_amount = self.tax_amount_edit.value() if hasattr(self, 'tax_amount_edit') else 0.0
            status = self.status_combo.currentText() if hasattr(self, 'status_combo') else 'معلق'
            payment_method = self.payment_method_combo.currentText() if hasattr(self, 'payment_method_combo') else 'نقدي'
            notes = self.notes_edit.toPlainText().strip() if hasattr(self, 'notes_edit') else ""

            # حالة الدفع
            is_paid = self.payment_checkbox.isChecked()

            # التحقق من صحة البيانات
            if not client_id:
                if hasattr(self.parent_widget, 'show_warning_message'):
                    self.parent_widget.show_warning_message("يجب اختيار عميل")
                else:
                    dialog = SaleWarningDialog(self, "تحذير", "يجب اختيار عميل", "⚠️")
                    dialog.exec_()
                return

            if not sale_number:
                if hasattr(self.parent_widget, 'show_warning_message'):
                    self.parent_widget.show_warning_message("يجب إدخال رقم المبيعة")
                else:
                    dialog = SaleWarningDialog(self, "تحذير", "يجب إدخال رقم المبيعة", "⚠️")
                    dialog.exec_()
                return

            if total_amount <= 0:
                if hasattr(self.parent_widget, 'show_warning_message'):
                    self.parent_widget.show_warning_message("يجب إدخال مبلغ صحيح")
                else:
                    dialog = SaleWarningDialog(self, "تحذير", "يجب إدخال مبلغ صحيح", "⚠️")
                    dialog.exec_()
                return

            # تحويل طريقة الدفع إلى الإنجليزية لقاعدة البيانات
            payment_method_map = {
                'نقدي': 'cash',
                'ائتمان': 'credit',
                'تحويل بنكي': 'bank_transfer',
                'شيك': 'check'
            }
            payment_method_en = payment_method_map.get(payment_method, 'cash')

            # تحويل الحالة إلى الإنجليزية لقاعدة البيانات
            status_map = {
                'معلق': 'pending',
                'مكتمل': 'completed',
                'ملغي': 'cancelled',
                'مرتجع': 'returned'
            }
            status_en = status_map.get(status, 'pending')

            if self.is_edit_mode and self.sale:
                # تحديث مبيعة موجودة
                self.sale.client_id = client_id
                self.sale.sale_number = sale_number
                self.sale.date = datetime.datetime.combine(date, datetime.time())
                self.sale.total_amount = total_amount
                self.sale.paid_amount = paid_amount
                self.sale.discount_amount = discount_amount
                self.sale.tax_amount = tax_amount
                self.sale.status = status_en
                self.sale.payment_method = payment_method_en

                # إضافة معلومات المنتج للملاحظات إذا تم إدخالها
                final_notes = notes or ""
                if product_name:
                    product_info = f"المنتج: {product_name}"
                    if quantity > 0:
                        product_info += f"\nالكمية: {quantity:.2f} وحدة"
                    if unit_price > 0:
                        product_info += f"\nسعر الوحدة: {unit_price:.2f} جنيه"

                    if final_notes:
                        final_notes = f"{product_info}\n{final_notes}"
                    else:
                        final_notes = product_info

                self.sale.notes = final_notes or None

                message = f"تم تحديث المبيعة '{sale_number}' بنجاح!"
            else:
                # إضافة معلومات المنتج للملاحظات إذا تم إدخالها
                final_notes = notes or ""
                if product_name:
                    product_info = f"المنتج: {product_name}"
                    if quantity > 0:
                        product_info += f"\nالكمية: {quantity:.2f} وحدة"
                    if unit_price > 0:
                        product_info += f"\nسعر الوحدة: {unit_price:.2f} جنيه"

                    if final_notes:
                        final_notes = f"{product_info}\n{final_notes}"
                    else:
                        final_notes = product_info

                # إنشاء مبيعة جديدة
                new_sale = Sale(
                    client_id=client_id,
                    sale_number=sale_number,
                    date=datetime.datetime.combine(date, datetime.time()),
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    discount_amount=discount_amount,
                    tax_amount=tax_amount,
                    status=status_en,
                    payment_method=payment_method_en,
                    notes=final_notes or None
                )
                self.session.add(new_sale)

                # تحديث المخزون إذا تم تحديد منتج وكمية
                inventory_message = ""
                if product_name and quantity > 0:
                    try:
                        from database import Inventory
                        # البحث عن المنتج في المخزون
                        inventory_item = self.session.query(Inventory).filter(
                            Inventory.name.ilike(f"%{product_name}%")
                        ).first()

                        if inventory_item:
                            # خصم الكمية من المخزون
                            old_quantity = inventory_item.quantity or 0
                            new_quantity = old_quantity - quantity
                            inventory_item.quantity = max(0, new_quantity)  # لا تسمح بكمية سالبة

                            inventory_message = (
                                f"📦 تم تحديث المخزون للمنتج '{product_name}'\n"
                                f"الكمية السابقة: {old_quantity:.2f} وحدة\n"
                                f"الكمية المباعة: {quantity:.2f} وحدة\n"
                                f"الكمية الجديدة: {inventory_item.quantity:.2f} وحدة"
                            )

                            # تحذير إذا انخفضت الكمية عن الحد الأدنى
                            if inventory_item.min_quantity and inventory_item.quantity <= inventory_item.min_quantity:
                                inventory_message += f"\n⚠️ تحذير: الكمية أصبحت أقل من أو تساوي الحد الأدنى ({inventory_item.min_quantity:.2f})"
                        else:
                            inventory_message = f"⚠️ تحذير: لم يتم العثور على المنتج '{product_name}' في المخزون"
                    except Exception as e:
                        inventory_message = f"❌ خطأ في تحديث المخزون: {str(e)}"

                # تطبيق المنطق المحاسبي للعميل
                client_message = ""
                if is_paid:
                    # إذا تم الدفع، لا نخصم من رصيد العميل (تم التسوية)
                    client_message = f"✅ تم تسجيل استلام الدفع من العميل"
                else:
                    # إذا لم يتم الدفع، نخصم من رصيد العميل (دين عليه)
                    from database import Client
                    client = self.session.query(Client).get(client_id)
                    if client:
                        old_client_balance = client.balance or 0
                        client.balance = old_client_balance - total_amount  # خصم من رصيد العميل
                        client_message = (
                            f"💰 تم خصم {total_amount:,.2f} جنيه من رصيد العميل {client.name}\n"
                            f"رصيد العميل السابق: {old_client_balance:,.2f} جنيه\n"
                            f"رصيد العميل الجديد: {client.balance:,.2f} جنيه"
                        )

                message = f"تم إضافة المبيعة '{sale_number}' بنجاح!\n"
                if client_message:
                    message += f"\n{client_message}"
                if inventory_message:
                    message += f"\n\n{inventory_message}"

            self.session.commit()
            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message(message)
            else:
                show_info_message("نجح", message)
            self.accept()

        except Exception as e:
            self.session.rollback()
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في حفظ المبيعة: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في حفظ المبيعة: {str(e)}")

    def load_sale_data(self):
        """تحميل بيانات المبيعة في وضع التعديل"""
        if not self.sale:
            return

        try:
            # تحميل العميل
            if hasattr(self, 'client_combo') and self.sale.client_id:
                index = self.client_combo.findData(self.sale.client_id)
                if index >= 0:
                    self.client_combo.setCurrentIndex(index)

            # تحميل رقم المبيعة
            if hasattr(self, 'sale_number_edit') and self.sale.sale_number:
                self.sale_number_edit.setText(self.sale.sale_number)

            # تحميل التاريخ
            if hasattr(self, 'date_edit') and self.sale.date:
                self.date_edit.setDate(QDate(self.sale.date))

            # تحميل المبلغ الإجمالي
            if hasattr(self, 'total_amount_edit'):
                self.total_amount_edit.setValue(self.sale.total_amount or 0.0)

            # تحميل المبلغ المدفوع
            if hasattr(self, 'paid_amount_edit'):
                self.paid_amount_edit.setValue(self.sale.paid_amount or 0.0)

            # تحميل مبلغ الخصم
            if hasattr(self, 'discount_amount_edit'):
                self.discount_amount_edit.setValue(self.sale.discount_amount or 0.0)

            # تحميل مبلغ الضريبة
            if hasattr(self, 'tax_amount_edit'):
                self.tax_amount_edit.setValue(self.sale.tax_amount or 0.0)

            # تحميل الحالة
            if hasattr(self, 'status_combo') and self.sale.status:
                status_map = {
                    'pending': 'معلق',
                    'completed': 'مكتمل',
                    'cancelled': 'ملغي',
                    'returned': 'مرتجع'
                }
                status_ar = status_map.get(self.sale.status, 'معلق')
                self.status_combo.setCurrentText(status_ar)

            # تحميل طريقة الدفع
            if hasattr(self, 'payment_method_combo') and self.sale.payment_method:
                payment_method_map = {
                    'cash': 'نقدي',
                    'credit': 'ائتمان',
                    'bank_transfer': 'تحويل بنكي',
                    'check': 'شيك'
                }
                payment_method_ar = payment_method_map.get(self.sale.payment_method, 'نقدي')
                self.payment_method_combo.setCurrentText(payment_method_ar)

            # تحميل معلومات المنتج من الملاحظات
            if hasattr(self, 'product_edit') and self.sale.notes:
                # البحث عن معلومات المنتج في الملاحظات
                notes_lines = self.sale.notes.split('\n')
                product_name = ""
                quantity = 1.0
                unit_price = 0.0
                remaining_notes = []

                for line in notes_lines:
                    if line.startswith('المنتج:'):
                        product_name = line.replace('المنتج:', '').strip()
                    elif line.startswith('الكمية:'):
                        try:
                            quantity_text = line.replace('الكمية:', '').replace('وحدة', '').strip()
                            quantity = float(quantity_text)
                        except:
                            quantity = 1.0
                    elif line.startswith('سعر الوحدة:'):
                        try:
                            price_text = line.replace('سعر الوحدة:', '').replace('جنيه', '').strip()
                            unit_price = float(price_text)
                        except:
                            unit_price = 0.0
                    else:
                        remaining_notes.append(line)

                # تحميل البيانات في الحقول
                if product_name:
                    self.product_edit.setText(product_name)
                if hasattr(self, 'quantity_edit'):
                    self.quantity_edit.setValue(quantity)
                if hasattr(self, 'unit_price_edit'):
                    self.unit_price_edit.setValue(unit_price)

                # حساب الإجمالي تلقائياً بعد تحميل البيانات
                self.calculate_total()

                # تحميل باقي الملاحظات
                if hasattr(self, 'notes_edit'):
                    self.notes_edit.setPlainText('\n'.join(remaining_notes))
            else:
                # تحميل الملاحظات كاملة إذا لم يكن هناك منتج
                if hasattr(self, 'notes_edit'):
                    self.notes_edit.setPlainText(self.sale.notes or "")

            # تحميل حالة الدفع (افتراضياً غير مدفوع في وضع التعديل)
            self.payment_checkbox.setChecked(False)

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات المبيعة: {e}")
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في تحميل بيانات المبيعة: {str(e)}")
            # المتابعة بدون تحميل البيانات

    def get_data(self):
        """الحصول على بيانات المبيعة"""
        if self.sale:
            return {
                'client_id': getattr(self.sale, 'client_id', None),
                'total_amount': getattr(self.sale, 'total_amount', 0.0),
                'notes': getattr(self.sale, 'notes', None),
                'date': getattr(self.sale, 'date', datetime.now()),
                'status': getattr(self.sale, 'status', 'pending')
            }
        return None

    def scan_barcode_for_product(self):
        """قراءة باركود لاختيار المنتج"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            from utils.barcode_reader import read_barcode_from_file, find_product_by_barcode

            # خيارات القراءة
            reply = QMessageBox.question(
                self,
                "قراءة الباركود",
                "كيف تريد قراءة الباركود؟\n\n"
                "نعم: من ملف صورة\n"
                "لا: من الكاميرا (قريباً)",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )

            if reply == QMessageBox.Yes:
                # قراءة من ملف
                file_path, _ = QFileDialog.getOpenFileName(
                    self,
                    "اختر صورة الباركود",
                    "",
                    "صور (*.png *.jpg *.jpeg *.bmp *.gif)"
                )

                if file_path:
                    success, message, barcodes = read_barcode_from_file(file_path)

                    if success and barcodes:
                        # استخدام أول باركود مكتشف
                        barcode_data = barcodes[0]['data']

                        # البحث عن المنتج في قاعدة البيانات
                        found, search_message, product = find_product_by_barcode(self.session, barcode_data)

                        if found and product:
                            # تحديث حقل المنتج
                            self.product_edit.setText(product.name)

                            # تحديث سعر الوحدة إذا كان متاحاً
                            if product.selling_price and product.selling_price > 0:
                                self.unit_price_edit.setValue(product.selling_price)

                            # حساب الإجمالي تلقائياً
                            self.calculate_total()

                            # عرض رسالة نجاح
                            if hasattr(self.parent_widget, 'show_success_message'):
                                self.parent_widget.show_success_message(f"تم العثور على المنتج: {product.name}")
                            else:
                                QMessageBox.information(self, "نجح", f"تم العثور على المنتج: {product.name}")
                        else:
                            # عرض رسالة عدم وجود المنتج
                            if hasattr(self.parent_widget, 'show_error_message'):
                                self.parent_widget.show_error_message(f"لم يتم العثور على منتج بهذا الباركود: {barcode_data}")
                            else:
                                QMessageBox.warning(self, "تحذير", f"لم يتم العثور على منتج بهذا الباركود: {barcode_data}")
                    else:
                        # عرض رسالة فشل القراءة
                        if hasattr(self.parent_widget, 'show_error_message'):
                            self.parent_widget.show_error_message(f"فشل في قراءة الباركود: {message}")
                        else:
                            QMessageBox.critical(self, "خطأ", f"فشل في قراءة الباركود: {message}")

            elif reply == QMessageBox.No:
                # قراءة من الكاميرا (سيتم تطويرها لاحقاً)
                if hasattr(self.parent_widget, 'show_info_message'):
                    self.parent_widget.show_info_message("قراءة الباركود من الكاميرا غير متاحة حالياً")
                else:
                    QMessageBox.information(self, "معلومات", "قراءة الباركود من الكاميرا غير متاحة حالياً")

        except Exception as e:
            error_msg = f"خطأ في قراءة الباركود: {str(e)}"
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(error_msg)
            else:
                QMessageBox.critical(self, "خطأ", error_msg)


class SaleInfoDialog(QDialog):
    """نافذة تفاصيل المبيعة - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, sale=None):
        super().__init__(parent)
        self.sale = sale
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية - مطابق تماماً للعملاء"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
        self.setWindowTitle("🛍️📋 معلومات المبيعة - نظام إدارة المبيعات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل - مطابق للعملاء

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # ═══════════════════════════════════════════════════════════════
        # التخطيط الرئيسي المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # ═══════════════════════════════════════════════════════════════
        # عنوان النافذة المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        title_label = QLabel(f"💰 تفاصيل المبيعة: {client_name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 18px 25px;
                margin: 8px 0px 20px 0px;
            }
        """)
        main_layout.addWidget(title_label)

        # ═══════════════════════════════════════════════════════════════
        # منطقة التمرير المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                background: transparent;
                padding: 5px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                width: 14px;
                border-radius: 7px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(168, 85, 247, 0.6));
                border-radius: 6px;
                min-height: 25px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.9),
                    stop:1 rgba(168, 85, 247, 0.8));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق للعملاء
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)  # زيادة المسافة بين الأقسام لاستغلال المساحة الإضافية

        # إضافة معلومات المبيعة
        self.add_sale_info(info_layout)

        scroll_area.setWidget(info_widget)
        main_layout.addWidget(scroll_area)

        # ═══════════════════════════════════════════════════════════════
        # أزرار التحكم المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.create_control_buttons(main_layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def create_window_icon(self):
        """إنشاء أيقونة النافذة - مطابق للعملاء"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
            from PyQt5.QtCore import Qt

            # إنشاء أيقونة بسيطة
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(59, 130, 246)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.end()

            return QIcon(pixmap)
        except Exception as e:
            return QIcon()

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي - مطابق تماماً للعملاء"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """

    def add_sale_info(self, layout):
        """إضافة معلومات المبيعة إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.sale:
            return

        # قسم المعلومات الأساسية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.sale.id).zfill(8)}"),
            ("🔢 رقم المبيعة", getattr(self.sale, 'sale_number', None) or "غير محدد"),
            ("🧑‍💼 العميل", self.sale.client.name if self.sale.client else "عميل نقدي"),
            ("📅 تاريخ المبيعة", self.sale.date.strftime('%Y-%m-%d') if self.sale.date else "غير محدد"),
            ("🎯 حالة المبيعة", self.get_sale_status())
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم المعلومات المالية
        financial_info = [
            ("💰 المبلغ الإجمالي", f"{getattr(self.sale, 'total_amount', 0):,.0f} جنيه"),
            ("💵 المبلغ المدفوع", f"{getattr(self.sale, 'paid_amount', 0):,.0f} جنيه"),
            ("🏷️ قيمة الخصم", f"{getattr(self.sale, 'discount_amount', 0):,.0f} جنيه"),
            ("📊 قيمة الضريبة", f"{getattr(self.sale, 'tax_amount', 0):,.0f} جنيه"),
            ("💳 طريقة الدفع", self.get_payment_method())
        ]
        self.add_info_section(layout, "💰 المعلومات المالية", financial_info)

        # قسم تفاصيل إضافية
        additional_info = [
            ("📋 الملاحظات", getattr(self.sale, 'notes', None) or "لا توجد ملاحظات"),
            ("⏰ تاريخ الإنشاء", self.sale.created_at.strftime('%Y-%m-%d %H:%M') if hasattr(self.sale, 'created_at') and self.sale.created_at else "غير محدد"),
            ("🔄 آخر تحديث", self.sale.updated_at.strftime('%Y-%m-%d %H:%M') if hasattr(self.sale, 'updated_at') and self.sale.updated_at else "غير محدد"),
            ("💼 نوع المعاملة", self.get_transaction_type()),
            ("📊 ملخص المبيعة", self.get_sale_summary())
        ]
        self.add_info_section(layout, "📊 تفاصيل إضافية", additional_info)

    def add_info_section(self, layout, title, info_list):
        """إضافة قسم معلومات بدون إطار رئيسي"""
        # حاوي القسم بدون إطار
        section_frame = QWidget()
        section_frame.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
                margin: 5px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المبسط
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(139, 92, 246, 0.15),
                    stop:1 rgba(34, 197, 94, 0.1));
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                margin-bottom: 8px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(title_label)

        # معلومات القسم
        for i, (label_text, value_text) in enumerate(info_list):
            info_frame = QWidget()
            info_frame.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.02 + (i % 2) * 0.01}),
                        stop:0.5 rgba(248, 250, 252, {0.03 + (i % 2) * 0.01}),
                        stop:1 rgba(241, 245, 249, {0.02 + (i % 2) * 0.01}));
                    border: none;
                    border-radius: 4px;
                    margin: 1px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.05),
                        stop:0.5 rgba(139, 92, 246, 0.04),
                        stop:1 rgba(34, 197, 94, 0.03));
                    border: none;
                }}
            """)

            info_layout = QHBoxLayout(info_frame)
            info_layout.setContentsMargins(12, 8, 12, 8)
            info_layout.setSpacing(15)

            # التسمية المحسنة
            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: none;
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة
            value = QLabel(str(value_text))
            value.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }
            """)
            value.setWordWrap(True)

            info_layout.addWidget(label)
            info_layout.addWidget(value, 1)

            section_layout.addWidget(info_frame)

        layout.addWidget(section_frame)

    def get_sale_status(self):
        """حالة المبيعة"""
        status_map = {
            'pending': 'معلق ⏳',
            'completed': 'مكتمل ✅',
            'cancelled': 'ملغي ❌',
            'returned': 'مرتجع 🔄'
        }
        return status_map.get(self.sale.status, self.sale.status or 'غير محدد')

    def get_payment_method(self):
        """طريقة الدفع"""
        payment_map = {
            'cash': 'نقدي 💵',
            'credit': 'ائتمان 💳',
            'bank_transfer': 'تحويل بنكي 🏦',
            'check': 'شيك 📄'
        }
        return payment_map.get(getattr(self.sale, 'payment_method', None), 'نقدي 💵')

    def get_transaction_type(self):
        """نوع المعاملة"""
        if getattr(self.sale, 'total_amount', 0) > 10000:
            return "معاملة كبيرة 💎"
        elif getattr(self.sale, 'total_amount', 0) > 1000:
            return "معاملة متوسطة 💰"
        else:
            return "معاملة صغيرة 💵"

    def get_sale_summary(self):
        """ملخص المبيعة"""
        client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
        amount = getattr(self.sale, 'total_amount', 0)
        return f"مبيعة {client_name} بقيمة {amount:,.0f} جنيه"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;
                margin: 5px 0;
                min-height: 65px;
                max-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الإغلاق - في المقدمة
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)
        close_btn.setMaximumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)
        print_btn.setMaximumHeight(45)
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)
        export_pdf_btn.setMaximumHeight(45)
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)
        note_btn.setMaximumHeight(45)
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار
        buttons_layout.addWidget(close_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                if button_type in colors:
                    primary, secondary = colors[button_type]
                    button.setStyleSheet(f"""
                        QPushButton {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            color: #FFFFFF;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            border-radius: 8px;
                            font-size: 15px;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                            padding: 10px 14px;
                        }}
                        QPushButton:hover {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {secondary}, stop:1 {primary});
                            border: 3px solid rgba(255, 255, 255, 0.5);
                            transform: translateY(-2px);
                        }}
                        QPushButton:pressed {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            transform: translateY(1px);
                        }}
                    """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار المعلومات: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def print_info(self):
        """طباعة معلومات المبيعة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
                painter.drawText(100, y_position, f"تقرير المبيعة: {client_name}")
                y_position += line_height * 2

                # تاريخ الطباعة
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات الأساسية:")
                y_position += line_height

                painter.setFont(normal_font)
                basic_info = [
                    f"المعرف: #{str(self.sale.id).zfill(8)}",
                    f"رقم المبيعة: {getattr(self.sale, 'sale_number', None) or 'غير محدد'}",
                    f"العميل: {client_name}",
                    f"التاريخ: {self.sale.date.strftime('%Y-%m-%d') if self.sale.date else 'غير محدد'}",
                    f"الحالة: {self.get_sale_status()}"
                ]

                for info in basic_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                y_position += line_height

                # المعلومات المالية
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات المالية:")
                y_position += line_height

                painter.setFont(normal_font)
                financial_info = [
                    f"المبلغ الإجمالي: {getattr(self.sale, 'total_amount', 0):,.0f} جنيه",
                    f"المبلغ المدفوع: {getattr(self.sale, 'paid_amount', 0):,.0f} جنيه",
                    f"قيمة الخصم: {getattr(self.sale, 'discount_amount', 0):,.0f} جنيه",
                    f"قيمة الضريبة: {getattr(self.sale, 'tax_amount', 0):,.0f} جنيه",
                    f"طريقة الدفع: {self.get_payment_method()}"
                ]

                for info in financial_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                painter.end()
                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message("تم طباعة تفاصيل المبيعة بنجاح")
                else:
                    show_info_message("نجح", "تم طباعة تفاصيل المبيعة بنجاح")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"خطأ في الطباعة: {str(e)}")
            else:
                show_error_message("خطأ", f"خطأ في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات المبيعة إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont
            import os

            # اختيار مكان الحفظ
            client_name = self.sale.client.name if self.sale.client else "عميل_نقدي"
            default_filename = f"مبيعة_{client_name}_{self.sale.id}.pdf"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير المبيعة",
                default_filename,
                "PDF Files (*.pdf)"
            )

            if file_path:
                printer = QPrinter()
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                painter.drawText(100, y_position, f"تقرير المبيعة: {client_name}")
                y_position += line_height * 2

                # تاريخ التصدير
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات التفصيلية
                sections = [
                    ("المعلومات الأساسية", [
                        f"المعرف: #{str(self.sale.id).zfill(8)}",
                        f"رقم المبيعة: {getattr(self.sale, 'sale_number', None) or 'غير محدد'}",
                        f"العميل: {client_name}",
                        f"التاريخ: {self.sale.date.strftime('%Y-%m-%d') if self.sale.date else 'غير محدد'}",
                        f"الحالة: {self.get_sale_status()}"
                    ]),
                    ("المعلومات المالية", [
                        f"المبلغ الإجمالي: {getattr(self.sale, 'total_amount', 0):,.0f} جنيه",
                        f"المبلغ المدفوع: {getattr(self.sale, 'paid_amount', 0):,.0f} جنيه",
                        f"قيمة الخصم: {getattr(self.sale, 'discount_amount', 0):,.0f} جنيه",
                        f"قيمة الضريبة: {getattr(self.sale, 'tax_amount', 0):,.0f} جنيه",
                        f"طريقة الدفع: {self.get_payment_method()}"
                    ]),
                    ("تفاصيل إضافية", [
                        f"الملاحظات: {getattr(self.sale, 'notes', None) or 'لا توجد ملاحظات'}",
                        f"نوع المعاملة: {self.get_transaction_type()}",
                        f"ملخص المبيعة: {self.get_sale_summary()}"
                    ])
                ]

                for section_title, section_info in sections:
                    painter.setFont(header_font)
                    painter.drawText(100, y_position, section_title + ":")
                    y_position += line_height

                    painter.setFont(normal_font)
                    for info in section_info:
                        painter.drawText(120, y_position, info)
                        y_position += line_height

                    y_position += line_height // 2

                painter.end()
                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم تصدير تقرير المبيعة إلى:\n{file_path}")
                else:
                    show_info_message("نجح", f"تم تصدير تقرير المبيعة إلى:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في التصدير: {str(e)}")
            else:
                show_error_message("خطأ", f"فشل في التصدير: {str(e)}")

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddSaleNoteDialog(self, self.sale, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_sale_info()
        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في فتح نافذة الملاحظات: {str(e)}")
            else:
                show_error_message("خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_sale_info(self):
        """تحديث معلومات المبيعة"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                updated_sale = self.parent_widget.session.query(Sale).get(self.sale.id)
                if updated_sale:
                    self.sale = updated_sale
                    self.setup_ui()
        except Exception as e:
            print(f"تحذير: فشل في تحديث معلومات المبيعة: {e}")
            # المتابعة بدون تحديث المعلومات

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان للملاحظات: {e}")
            # المتابعة بدون تخصيص شريط العنوان

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تصميم شريط العنوان للملاحظات: {e}")
            # المتابعة بدون تصميم شريط العنوان


class AddSaleNoteDialog(QDialog):
    """نافذة ملاحظات المبيعة - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, sale=None, parent_widget=None):
        super().__init__(parent)
        self.sale = sale
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً - مطابق للنموذج المرجعي"""
        client_name = self.sale.client.name if self.sale.client else "عميل نقدي"
        self.setWindowTitle(f"📝 {client_name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(SaleInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # عنوان داخلي مطابق للنموذج المرجعي
        title_label = QLabel(f"💰 ملاحظات المبيعة: {client_name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 14px 22px;
                margin: 5px 0px 15px 0px;
            }
        """)
        layout.addWidget(title_label)

        # محرر النص مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.4),
                    stop:0.3 rgba(30, 41, 59, 0.35),
                    stop:0.7 rgba(51, 65, 85, 0.3),
                    stop:1 rgba(71, 85, 105, 0.25));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 16px;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                padding: 14px;
                selection-background-color: rgba(59, 130, 246, 0.4);
                selection-color: #FFFFFF;
            }
            QTextEdit:focus {
                border: 3px solid rgba(59, 130, 246, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.5),
                    stop:0.3 rgba(30, 41, 59, 0.45),
                    stop:0.7 rgba(51, 65, 85, 0.4),
                    stop:1 rgba(71, 85, 105, 0.35));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن المبيعة، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        cancel_btn.setMinimumHeight(45)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        save_btn.setMinimumHeight(45)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور - مطابق للعملاء"""
        # استخدام نفس التصميم المتطور من النموذج المرجعي
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            # تصميم متطور مطابق للنموذج المرجعي
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['bg_start']}, stop:0.3 {color_set['bg_mid']},
                        stop:0.7 {color_set['bg_end']}, stop:1 {color_set['bg_bottom']});
                    color: #FFFFFF;
                    border: 2px solid {color_set['border']};
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 10px 18px;
                    box-shadow: 0 4px 15px {color_set['shadow']};
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['hover_start']}, stop:0.3 {color_set['hover_mid']},
                        stop:0.7 {color_set['hover_end']}, stop:1 {color_set['hover_bottom']});
                    border: 3px solid {color_set['border']};
                    transform: translateY(-2px);
                    box-shadow: 0 6px 20px {color_set['shadow']};
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color_set['bg_start']}, stop:0.5 {color_set['bg_mid']},
                        stop:1 {color_set['bg_end']});
                    border: 1px solid {color_set['border']};
                    transform: translateY(1px);
                    box-shadow: 0 2px 8px {color_set['shadow']};
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة الحالية"""
        try:
            if self.sale and hasattr(self.sale, 'notes') and self.sale.notes:
                self.text_editor.setText(self.sale.notes)
        except Exception as e:
            print(f"تحذير: فشل في تحميل ملاحظة المبيعة: {e}")
            # المتابعة بدون تحميل الملاحظة

    def save_note(self):
        """حفظ الملاحظة - مطابق للنموذج المرجعي"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.sale.notes = note if note else None

            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message("حُفظت الملاحظة")
            else:
                show_info_message("تم", "حُفظت الملاحظة")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل الحفظ: {str(e)}")
            else:
                show_error_message("خطأ", f"فشل الحفظ: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليكون أسود"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            # تطبيق تصميم أسود بسيط للشريط
            pass


class SalesStatisticsDialog(QDialog):
    """نافذة إحصائيات المبيعات مطابقة للعملاء والموردين والعمال والمشاريع والعقارات والمخزون"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة - مطابقة للعملاء"""
        # إعداد النافذة الأساسي
        self.setWindowTitle("📊 إحصائيات المبيعات - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(520, 450)  # حجم أكبر قليلاً لاستيعاب الإحصائيات الجديدة

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)  # هوامش مضغوطة جداً
        layout.setSpacing(6)  # مسافات مضغوطة جداً

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 4px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(3)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات المبيعات")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                padding: 4px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة المبيعات والإيرادات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 13px;
                font-weight: normal;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                padding: 2px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(3)  # مسافات مضغوطة جداً
        stats_layout.setContentsMargins(8, 4, 8, 4)  # هوامش مضغوطة جداً

        # إنشاء قائمة الإحصائيات المحدثة والمتطورة
        stats_items = [
            ("💰", "إجمالي المبيعات المسجلة", str(self.total_sales), "#3B82F6", "📊"),
            ("✅", "المبيعات المكتملة بنجاح", str(self.completed_sales), "#10B981", "💚"),
            ("⏳", "المبيعات قيد المعالجة", str(self.pending_sales), "#F59E0B", "🟡"),
            ("❌", "المبيعات الملغية أو المرفوضة", str(self.cancelled_sales), "#EF4444", "🔴"),
            ("💵", "إجمالي قيمة المبيعات", format_currency(self.total_amount), "#10B981", "⬆️"),
            ("🏆", "أفضل العملاء شراءً", str(getattr(self, 'top_customers', 0)), "#059669", "💎"),
            ("👥", "العملاء الجدد هذا الشهر", str(getattr(self, 'new_customers', 0)), "#8B5CF6", "🆕"),
            ("🚀", "أعلى المبيعات ربحية", f"{getattr(self, 'highest_profit_sale', 0):,.0f} جنيه", "#DC2626", "⚡"),
            ("📉", "أقل المبيعات ربحية", f"{getattr(self, 'lowest_profit_sale', 0):,.0f} جنيه", "#6366F1", "🔻"),
            ("📈", "مبيعات هذا الشهر", f"{getattr(self, 'current_month_sales', 0):,.0f} جنيه", "#F59E0B", "📂")
        ]

        for icon, title, value, color, secondary_icon in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 1px;
                    margin: 0px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 3px;
                }
            """)
            item_widget.setFixedHeight(28)  # ارتفاع مضغوط جداً

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(6)
            item_layout.setContentsMargins(6, 2, 6, 2)

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(20, 20)  # حجم أصغر جداً
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                QLabel {{
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px 3px;
                }}
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px 6px;
                    min-width: 50px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            # الأيقونة الثانوية
            secondary_icon_label = QLabel(secondary_icon)
            secondary_icon_label.setFixedSize(16, 16)
            secondary_icon_label.setAlignment(Qt.AlignCenter)
            secondary_icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px;
                }}
            """)
            item_layout.addWidget(secondary_icon_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(8, 4, 8, 4)

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        self.style_advanced_button(close_button, 'danger')

        # زر تصدير
        export_pdf_button = QPushButton("📄 تصدير")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        self.style_advanced_button(export_pdf_button, 'info')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)



    def calculate_statistics(self):
        """حساب إحصائيات المبيعات"""
        try:
            # حساب إجمالي المبيعات
            self.total_sales = self.session.query(Sale).count()

            # حساب المبيعات حسب الحالة (افتراض أن هناك حقل status)
            self.completed_sales = self.session.query(Sale).filter(Sale.status == 'مكتمل').count()
            self.pending_sales = self.session.query(Sale).filter(Sale.status == 'قيد المعالجة').count()
            self.cancelled_sales = self.session.query(Sale).filter(Sale.status.in_(['ملغي', 'مرفوض'])).count()

            # حساب إجمالي المبلغ
            total_amount_result = self.session.query(func.sum(Sale.total_amount)).scalar()
            self.total_amount = total_amount_result or 0

            # حساب الإحصائيات المتطورة الجديدة
            self.calculate_advanced_sales_statistics()

        except Exception as e:
            self.total_sales = 0
            self.completed_sales = 0
            self.pending_sales = 0
            self.cancelled_sales = 0
            self.total_amount = 0
            # قيم افتراضية للإحصائيات الجديدة
            self.top_customers = 0
            self.new_customers = 0
            self.highest_profit_sale = 0
            self.lowest_profit_sale = 0
            self.current_month_sales = 0

    def calculate_advanced_sales_statistics(self):
        """حساب الإحصائيات المتطورة للمبيعات"""
        try:
            from datetime import datetime, timedelta
            from collections import Counter

            # جلب جميع المبيعات
            sales = self.session.query(Sale).all()

            # 1. حساب أفضل العملاء (الأكثر شراءً)
            customer_counts = Counter()
            for sale in sales:
                if hasattr(sale, 'customer_id') and sale.customer_id:
                    customer_counts[sale.customer_id] += 1

            self.top_customers = len([c for c, count in customer_counts.items() if count >= 3])  # عملاء بـ 3+ مبيعات

            # 2. حساب العملاء الجدد هذا الشهر
            current_date = datetime.now()
            start_of_month = current_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            # العملاء الذين لديهم أول مبيعة هذا الشهر
            new_customers_this_month = set()
            for sale in sales:
                if (hasattr(sale, 'sale_date') and sale.sale_date and
                    sale.sale_date >= start_of_month.date() and
                    hasattr(sale, 'customer_id') and sale.customer_id):

                    # التحقق من أن هذه أول مبيعة للعميل
                    earlier_sales = [s for s in sales
                                   if (hasattr(s, 'customer_id') and s.customer_id == sale.customer_id and
                                       hasattr(s, 'sale_date') and s.sale_date and
                                       s.sale_date < start_of_month.date())]

                    if not earlier_sales:
                        new_customers_this_month.add(sale.customer_id)

            self.new_customers = len(new_customers_this_month)

            # 3. حساب أعلى وأقل المبيعات ربحية
            profit_amounts = []
            for sale in sales:
                if (hasattr(sale, 'total_amount') and sale.total_amount and
                    hasattr(sale, 'cost_amount') and sale.cost_amount):
                    profit = sale.total_amount - sale.cost_amount
                    profit_amounts.append(profit)

            if profit_amounts:
                self.highest_profit_sale = max(profit_amounts)
                self.lowest_profit_sale = min(profit_amounts)
            else:
                self.highest_profit_sale = 0
                self.lowest_profit_sale = 0

            # 4. حساب مبيعات هذا الشهر
            current_month_sales = [
                s for s in sales
                if hasattr(s, 'sale_date') and s.sale_date and s.sale_date >= start_of_month.date()
            ]

            self.current_month_sales = sum(s.total_amount for s in current_month_sales if s.total_amount)

        except Exception as e:
            print(f"خطأ في حساب الإحصائيات المتطورة للمبيعات: {e}")
            # قيم افتراضية في حالة الخطأ
            self.top_customers = 0
            self.new_customers = 0
            self.highest_profit_sale = 0
            self.lowest_profit_sale = 0
            self.current_month_sales = 0

    def export_statistics_to_pdf(self):
        """تصدير إحصائيات المبيعات إلى ملف PDF باللغة العربية"""
        try:
            # التأكد من حساب الإحصائيات أولاً
            self.calculate_statistics()

            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from PyQt5.QtWidgets import QFileDialog
            import os

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير إحصائيات المبيعات",
                f"تقرير_إحصائيات_المبيعات_{self.format_datetime_for_filename()}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return

            # دالة لإصلاح النص العربي
            def fix_arabic_text(text):
                """إصلاح النص العربي للعرض الصحيح في PDF"""
                try:
                    if isinstance(text, str):
                        import arabic_reshaper
                        from bidi.algorithm import get_display
                        reshaped_text = arabic_reshaper.reshape(text)
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    return text
                except ImportError:
                    return text
                except Exception:
                    return text

            # إنشاء المستند
            doc = SimpleDocTemplate(
                file_path,
                pagesize=letter,
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=20,
                title="تقرير إحصائيات المبيعات"
            )

            story = []
            styles = getSampleStyleSheet()

            # تسجيل خط عربي
            try:
                arabic_font_path = "C:/Windows/Fonts/arial.ttf"
                if os.path.exists(arabic_font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'

            # العنوان الرئيسي
            title_style = ParagraphStyle(
                'ArabicTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=20,
                alignment=1,
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("💰 تقرير إحصائيات المبيعات"), title_style))
            story.append(Spacer(1, 8))

            # معلومات التقرير
            info_style = ParagraphStyle(
                'ArabicInfo',
                parent=styles['Normal'],
                fontSize=10,
                alignment=1,
                textColor=colors.grey,
                fontName=font_name,
                spaceAfter=5
            )

            story.append(Paragraph(fix_arabic_text(f"تاريخ التقرير: {self.format_datetime_for_export()}"), info_style))
            story.append(Spacer(1, 12))

            # عنوان قسم الإحصائيات
            section_style = ParagraphStyle(
                'SectionTitle',
                parent=styles['Heading2'],
                fontSize=13,
                spaceAfter=8,
                alignment=2,
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("📈 الإحصائيات الأساسية"), section_style))
            story.append(Spacer(1, 5))

            # إنشاء جدول الإحصائيات
            data = [
                [fix_arabic_text('البيان'), fix_arabic_text('القيمة'), fix_arabic_text('الوصف')],
                [fix_arabic_text('💰 إجمالي المبيعات'), str(self.total_sales), fix_arabic_text('العدد الكلي للمبيعات')],
                [fix_arabic_text('✅ المبيعات المكتملة'), str(self.completed_sales), fix_arabic_text('المبيعات المكتملة')],
                [fix_arabic_text('⏳ المبيعات قيد المعالجة'), str(self.pending_sales), fix_arabic_text('المبيعات قيد المعالجة')],
                [fix_arabic_text('❌ المبيعات الملغية'), str(self.cancelled_sales), fix_arabic_text('المبيعات الملغية')],
                [fix_arabic_text('💵 إجمالي القيمة'), f"{self.total_amount:,.0f} جنيه", fix_arabic_text('إجمالي قيمة المبيعات')],
                [fix_arabic_text('🏆 أفضل العملاء'), str(getattr(self, 'top_customers', 0)), fix_arabic_text('أفضل العملاء شراءً')],
                [fix_arabic_text('👥 العملاء الجدد'), str(getattr(self, 'new_customers', 0)), fix_arabic_text('العملاء الجدد هذا الشهر')],
                [fix_arabic_text('🚀 أعلى ربحية'), f"{getattr(self, 'highest_profit_sale', 0):,.0f} جنيه", fix_arabic_text('أعلى المبيعات ربحية')],
                [fix_arabic_text('📉 أقل ربحية'), f"{getattr(self, 'lowest_profit_sale', 0):,.0f} جنيه", fix_arabic_text('أقل المبيعات ربحية')],
                [fix_arabic_text('📈 مبيعات هذا الشهر'), f"{getattr(self, 'current_month_sales', 0):,.0f} جنيه", fix_arabic_text('مبيعات الشهر الحالي')]
            ]

            table = Table(data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BACKGROUND', (0, 1), (-1, 1), colors.lightgrey),
                ('BACKGROUND', (0, 3), (-1, 3), colors.lightgrey),
                ('BACKGROUND', (0, 5), (-1, 5), colors.lightgrey),
                ('BACKGROUND', (0, 7), (-1, 7), colors.lightgrey),
                ('BACKGROUND', (0, 9), (-1, 9), colors.lightgrey),
                ('BACKGROUND', (0, 11), (-1, 11), colors.lightgrey),
            ]))

            story.append(table)
            story.append(Spacer(1, 20))

            # ملاحظة
            note_style = ParagraphStyle(
                'Note',
                parent=styles['Normal'],
                fontSize=8,
                alignment=1,
                textColor=colors.grey,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة المبيعات"), note_style))

            # بناء المستند
            doc.build(story)

            # إظهار رسالة نجاح
            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message(f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            print(f"خطأ في تصدير التقرير: {e}")
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في تصدير التقرير: {str(e)}")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير التقرير: {str(e)}")

    def format_datetime_for_filename(self):
        """تنسيق التاريخ والوقت لاسم الملف"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    def format_datetime_for_export(self):
        """تنسيق التاريخ والوقت للتصدير"""
        from datetime import datetime
        return datetime.now().strftime("%Y/%m/%d %H:%M:%S")

    def show_info_message(self, message):
        """عرض رسالة معلومات"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "معلومات", message)
        except Exception as e:
            print(f"خطأ في عرض رسالة المعلومات: {e}")

    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", message)
        except Exception as e:
            print(f"خطأ في عرض رسالة الخطأ: {e}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الإحصائيات: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تصميم شريط العنوان للإحصائيات: {e}")
            # المتابعة بدون تصميم شريط العنوان







    def export_customer_report(self):
        """تقرير العملاء المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv
            from sqlalchemy import func

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير العملاء", f"تقرير_عملاء_مبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # تجميع البيانات حسب العميل
                customer_stats = self.session.query(
                    Sale.client_id,
                    func.count(Sale.id).label('sales_count'),
                    func.sum(Sale.total_amount).label('total_spent'),
                    func.avg(Sale.total_amount).label('avg_sale')
                ).group_by(Sale.client_id).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير العملاء المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['اسم العميل', 'عدد المبيعات', 'إجمالي المبلغ', 'متوسط المبيعة', 'هاتف العميل'])

                    # البيانات
                    for stat in sorted(customer_stats, key=lambda x: x.total_spent or 0, reverse=True):
                        if stat.client_id:
                            client = self.session.query(Client).filter_by(id=stat.client_id).first()
                            client_name = client.name if client else 'غير محدد'
                            client_phone = client.phone if client and client.phone else 'غير محدد'
                        else:
                            client_name = 'عميل نقدي'
                            client_phone = 'غير محدد'

                        writer.writerow([
                            client_name,
                            stat.sales_count or 0,
                            f"{stat.total_spent:.2f}" if stat.total_spent else '0.00',
                            f"{stat.avg_sale:.2f}" if stat.avg_sale else '0.00',
                            client_phone
                        ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء تقرير العملاء بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم إنشاء تقرير العملاء بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إنشاء تقرير العملاء: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير العملاء: {str(e)}")

    def export_revenue_analysis(self):
        """تحليل الإيرادات المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime, timedelta
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تحليل الإيرادات", f"تحليل_ايرادات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تحليل الإيرادات المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تحليل زمني
                    now = datetime.now()
                    last_month = now - timedelta(days=30)
                    last_week = now - timedelta(days=7)

                    monthly_sales = [s for s in sales if s.date and s.date >= last_month.date()]
                    weekly_sales = [s for s in sales if s.date and s.date >= last_week.date()]

                    monthly_revenue = sum(getattr(s, 'total_amount', 0) for s in monthly_sales)
                    weekly_revenue = sum(getattr(s, 'total_amount', 0) for s in weekly_sales)

                    writer.writerow(['التحليل الزمني:'])
                    writer.writerow([f'مبيعات آخر شهر: {len(monthly_sales)} بقيمة {monthly_revenue:.2f} جنيه'])
                    writer.writerow([f'مبيعات آخر أسبوع: {len(weekly_sales)} بقيمة {weekly_revenue:.2f} جنيه'])
                    writer.writerow([])

                    # تحليل طرق الدفع
                    payment_analysis = {}
                    for sale in sales:
                        method = sale.payment_method or 'نقدي'
                        if method not in payment_analysis:
                            payment_analysis[method] = {'count': 0, 'total': 0}
                        payment_analysis[method]['count'] += 1
                        payment_analysis[method]['total'] += getattr(sale, 'total_amount', 0)

                    writer.writerow(['تحليل طرق الدفع:'])
                    writer.writerow(['طريقة الدفع', 'عدد المبيعات', 'إجمالي المبلغ', 'النسبة المئوية'])

                    total_revenue = sum(data['total'] for data in payment_analysis.values())
                    for method, data in sorted(payment_analysis.items(), key=lambda x: x[1]['total'], reverse=True):
                        percentage = (data['total'] / total_revenue * 100) if total_revenue > 0 else 0
                        writer.writerow([
                            method,
                            data['count'],
                            f"{data['total']:.2f}",
                            f"{percentage:.1f}%"
                        ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء تحليل الإيرادات بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم إنشاء تحليل الإيرادات بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في تحليل الإيرادات: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في تحليل الإيرادات: {str(e)}")

    def export_monthly_report(self):
        """التقرير الشهري للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv
            from collections import defaultdict

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير الشهري", f"تقرير_شهري_مبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                sales = self.session.query(Sale).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['التقرير الشهري للمبيعات'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تجميع البيانات حسب الشهر
                    monthly_data = defaultdict(lambda: {'count': 0, 'total': 0, 'paid': 0})

                    for sale in sales:
                        if sale.date:
                            month_key = sale.date.strftime('%Y-%m')
                            monthly_data[month_key]['count'] += 1
                            monthly_data[month_key]['total'] += getattr(sale, 'total_amount', 0)
                            monthly_data[month_key]['paid'] += getattr(sale, 'paid_amount', 0)

                    # كتابة البيانات الشهرية
                    writer.writerow(['الشهر', 'عدد المبيعات', 'إجمالي المبلغ', 'إجمالي المدفوع', 'المتبقي', 'متوسط المبيعة'])

                    for month in sorted(monthly_data.keys(), reverse=True):
                        data = monthly_data[month]
                        remaining = data['total'] - data['paid']
                        avg_sale = data['total'] / data['count'] if data['count'] > 0 else 0

                        writer.writerow([
                            month,
                            data['count'],
                            f"{data['total']:.2f}",
                            f"{data['paid']:.2f}",
                            f"{remaining:.2f}",
                            f"{avg_sale:.2f}"
                        ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء التقرير الشهري بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم إنشاء التقرير الشهري بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إنشاء التقرير الشهري: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير الشهري: {str(e)}")









    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_مبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                sales = self.session.query(Sale).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(sales),
                        'backup_type': 'sales_full_backup',
                        'version': '1.0'
                    },
                    'sales': []
                }

                for sale in sales:
                    sale_data = {
                        'id': sale.id,
                        'product_name': sale.product_name,
                        'quantity': sale.quantity,
                        'unit_price': float(sale.unit_price) if sale.unit_price else 0.0,
                        'total_price': float(sale.total_price) if sale.total_price else 0.0,
                        'sale_date': sale.sale_date.isoformat() if sale.sale_date else None,
                        'customer_id': sale.customer_id if sale.customer_id else None,
                        'customer_name': sale.customer.name if sale.customer else None,
                        'notes': sale.notes,
                        'created_at': sale.sale_date.isoformat() if sale.sale_date else datetime.now().isoformat()
                    }
                    backup_data['sales'].append(sale_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(sales)} مبيعة")
                else:
                    show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(sales)} مبيعة")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للمبيعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة
                if hasattr(self.parent_widget, 'show_confirmation_message'):
                    reply = self.parent_widget.show_confirmation_message(
                        "تأكيد الاستعادة",
                        "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم دمج البيانات مع الموجودة حالياً!"
                    )
                else:
                    reply = QMessageBox.question(
                        self, "تأكيد الاستعادة",
                        "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم دمج البيانات مع الموجودة حالياً!",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    ) == QMessageBox.Yes

                if reply:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    if 'sales' in backup_data:
                        restored_count = 0
                        updated_count = 0

                        for sale_data in backup_data['sales']:
                            # التحقق من وجود المبيعة
                            existing = self.session.query(Sale).filter_by(product_name=sale_data.get('product_name')).first()

                            if existing:
                                # تحديث المبيعة الموجودة
                                existing.quantity = sale_data.get('quantity', 0)
                                existing.unit_price = sale_data.get('unit_price', 0)
                                existing.total_price = sale_data.get('total_price', 0)
                                existing.notes = sale_data.get('notes')
                                if sale_data.get('sale_date'):
                                    existing.sale_date = datetime.fromisoformat(sale_data['sale_date'])
                                updated_count += 1
                            else:
                                # إنشاء مبيعة جديدة
                                new_sale = Sale(
                                    product_name=sale_data.get('product_name'),
                                    quantity=sale_data.get('quantity', 0),
                                    unit_price=sale_data.get('unit_price', 0),
                                    total_price=sale_data.get('total_price', 0),
                                    sale_date=datetime.fromisoformat(sale_data['sale_date']) if sale_data.get('sale_date') else datetime.now(),
                                    customer_id=sale_data.get('customer_id'),
                                    notes=sale_data.get('notes')
                                )
                                self.session.add(new_sale)
                                restored_count += 1

                        self.session.commit()
                        self.refresh_data()  # إعادة تحميل البيانات

                        if hasattr(self.parent_widget, 'show_success_message'):
                            self.parent_widget.show_success_message(f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم إضافة {restored_count} مبيعة جديدة\nتم تحديث {updated_count} مبيعة موجودة")
                        else:
                            show_info_message("تم", f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم إضافة {restored_count} مبيعة جديدة\nتم تحديث {updated_count} مبيعة موجودة")
                    else:
                        if hasattr(self.parent_widget, 'show_error_message'):
                            self.parent_widget.show_error_message("ملف النسخة الاحتياطية غير صالح!")
                        else:
                            show_error_message("خطأ", "ملف النسخة الاحتياطية غير صالح!")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")

        if hasattr(self.parent_widget, 'show_warning_message'):
            self.parent_widget.show_warning_message("ميزة استعادة النسخ الاحتياطية غير متاحة حالياً")
        else:
            show_info_message("قريباً", "ميزة استعادة النسخ الاحتياطية غير متاحة حالياً")
