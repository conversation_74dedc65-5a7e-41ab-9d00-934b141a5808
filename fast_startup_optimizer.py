# -*- coding: utf-8 -*-
"""
محسن بدء التشغيل السريع
====================

نظام متقدم لتحسين وتسريع بدء تشغيل التطبيق من خلال:
- التحميل المتوازي للوحدات
- التخزين المؤقت الذكي
- تحسين استهلاك الذاكرة
- تأجيل التحميل للمكونات غير الحرجة

المؤلف: نظام إدارة الأعمال
الإصدار: 1.0
التاريخ: 2025
"""

import os
import sys
import time
import json
import threading
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

class FastStartupOptimizer:
    """محسن بدء التشغيل السريع"""
    
    def __init__(self):
        self.cache_file = "data/startup_cache.json"
        self.cache = self.load_cache()
        self.preloaded_modules = {}
        self.startup_metrics = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=3)
        self.pyqt5_available = self.check_pyqt5_availability()

    def check_pyqt5_availability(self):
        """فحص توفر PyQt5 وصحة التثبيت"""
        try:
            import PyQt5
            from PyQt5 import QtCore, QtWidgets, QtGui
            return True
        except ImportError:
            print("⚠️ PyQt5 غير مثبت أو غير متاح")
            return False
        except Exception as e:
            print(f"⚠️ مشكلة في PyQt5: {e}")
            return False

    def load_cache(self):
        """تحميل التخزين المؤقت"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"تحذير: فشل في تحميل التخزين المؤقت: {e}")
        return {}
    
    def save_cache(self):
        """حفظ التخزين المؤقت"""
        try:
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"تحذير: فشل في حفظ التخزين المؤقت: {e}")
    
    def preload_critical_modules(self):
        """تحميل الوحدات الحرجة مسبقاً بشكل متوازي مع معالجة محسنة للأخطاء"""
        # تحديد الوحدات بناءً على توفر PyQt5
        if self.pyqt5_available:
            critical_modules = [
                'PyQt5.QtCore',
                'PyQt5.QtWidgets',
                'PyQt5.QtGui',
                'sqlalchemy.orm',
                'sqlite3'
            ]
        else:
            critical_modules = [
                'sqlalchemy.orm',
                'sqlite3'
            ]
            print("⚠️ تخطي وحدات PyQt5 - غير متاحة")

        def load_module(module_name):
            try:
                start_time = time.time()

                # معالجة خاصة لوحدات PyQt5
                if module_name.startswith('PyQt5'):
                    # التأكد من تحميل PyQt5 أولاً
                    import PyQt5
                    # ثم تحميل الوحدة المحددة
                    if module_name == 'PyQt5.QtCore':
                        from PyQt5 import QtCore
                    elif module_name == 'PyQt5.QtWidgets':
                        from PyQt5 import QtWidgets
                    elif module_name == 'PyQt5.QtGui':
                        from PyQt5 import QtGui
                else:
                    __import__(module_name)

                load_time = time.time() - start_time
                self.preloaded_modules[module_name] = True
                self.startup_metrics[f"module_{module_name}"] = load_time
                return True, module_name, load_time

            except ImportError as e:
                return False, module_name, 0, str(e)
            except Exception as e:
                return False, module_name, 0, str(e)

        # تحميل متسلسل للوحدات الحرجة (أكثر استقراراً من المتوازي)
        successful_loads = 0
        total_modules = len(critical_modules)

        for module in critical_modules:
            if module not in self.preloaded_modules:
                try:
                    result = load_module(module)
                    if len(result) == 3:  # نجح التحميل
                        success, module_name, load_time = result
                        if success:
                            print(f"✅ تم تحميل {module_name} في {load_time:.3f}s")
                            successful_loads += 1
                        else:
                            print(f"⚠️ فشل تحميل {module_name}")
                    else:  # فشل التحميل مع رسالة خطأ
                        success, module_name, load_time, error = result
                        print(f"⚠️ فشل تحميل {module_name}: {error}")

                except Exception as e:
                    print(f"❌ خطأ في تحميل {module}: {e}")

        # تحديث معدل النجاح
        success_rate = (successful_loads / total_modules) * 100
        self.startup_metrics['module_success_rate'] = success_rate

        return successful_loads == total_modules
    
    def optimize_database_connection(self):
        """تحسين اتصال قاعدة البيانات"""
        try:
            # فحص وجود قاعدة البيانات
            db_file = "accounting.db"
            if os.path.exists(db_file):
                # حفظ معلومات قاعدة البيانات في التخزين المؤقت
                stat = os.stat(db_file)
                self.cache['db_size'] = stat.st_size
                self.cache['db_modified'] = stat.st_mtime
                return True
        except Exception as e:
            print(f"تحذير: فشل في تحسين اتصال قاعدة البيانات: {e}")
        return False
    
    def preload_ui_resources(self):
        """تحميل موارد واجهة المستخدم مسبقاً"""
        try:
            resources_dir = Path("resources")
            if resources_dir.exists():
                # تحميل الأيقونات الأساسية فقط
                essential_icons = [
                    "logo.png",
                    "dashboard.png", 
                    "settings.png"
                ]
                
                for icon in essential_icons:
                    icon_path = resources_dir / icon
                    if icon_path.exists():
                        # حفظ معلومات الأيقونة في التخزين المؤقت
                        self.cache[f"icon_{icon}"] = str(icon_path)
                
                return True
        except Exception as e:
            print(f"تحذير: فشل في تحميل موارد واجهة المستخدم: {e}")
        return False
    
    def optimize_memory_usage(self):
        """تحسين استهلاك الذاكرة"""
        try:
            import gc
            # تشغيل جامع القمامة
            gc.collect()
            
            # تحسين إعدادات Python
            if hasattr(sys, 'setswitchinterval'):
                sys.setswitchinterval(0.005)  # تحسين الأداء
            
            return True
        except Exception as e:
            print(f"تحذير: فشل في تحسين استهلاك الذاكرة: {e}")
        return False
    
    def check_system_performance(self):
        """فحص أداء النظام"""
        try:
            import psutil
            
            # فحص استهلاك الذاكرة
            memory = psutil.virtual_memory()
            if memory.percent > 80:
                print("⚠️ تحذير: استهلاك الذاكرة مرتفع")
                return False
            
            # فحص استهلاك المعالج
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > 80:
                print("⚠️ تحذير: استهلاك المعالج مرتفع")
                return False
            
            self.startup_metrics['memory_usage'] = memory.percent
            self.startup_metrics['cpu_usage'] = cpu_percent
            
            return True
        except ImportError:
            # psutil غير متاح
            return True
        except Exception as e:
            print(f"تحذير: فشل في فحص أداء النظام: {e}")
            return True
    
    def run_full_optimization(self):
        """تشغيل التحسين الكامل مع معالجة محسنة للأخطاء"""
        print("🚀 بدء تحسين بدء التشغيل...")
        start_time = time.time()

        # تشغيل التحسينات بترتيب محسن
        optimizations = [
            ("تحميل الوحدات الحرجة", self.preload_critical_modules),
            ("تحسين الذاكرة", self.optimize_memory_usage),
            ("تحسين قاعدة البيانات", self.optimize_database_connection),
            ("تحميل موارد واجهة المستخدم", self.preload_ui_resources),
            ("فحص أداء النظام", self.check_system_performance)
        ]

        results = {}
        successful_optimizations = 0

        for name, func in optimizations:
            try:
                opt_start = time.time()
                result = func()
                opt_time = time.time() - opt_start
                results[name] = (result, opt_time)

                if result:
                    print(f"✅ {name} - {opt_time:.3f}s")
                    successful_optimizations += 1
                else:
                    print(f"⚠️ {name} - فشل")
            except Exception as e:
                print(f"❌ خطأ في {name}: {e}")
                results[name] = (False, 0)

        # حساب معدل النجاح الإجمالي
        total_optimizations = len(optimizations)
        overall_success_rate = (successful_optimizations / total_optimizations) * 100

        # حفظ النتائج
        total_time = time.time() - start_time
        self.startup_metrics['total_optimization_time'] = total_time
        self.startup_metrics['optimization_results'] = results
        self.startup_metrics['overall_success_rate'] = overall_success_rate
        self.startup_metrics['successful_optimizations'] = successful_optimizations

        # حفظ التخزين المؤقت
        self.save_cache()

        # عرض ملخص النتائج
        if overall_success_rate >= 80:
            status = "✅ ممتاز"
        elif overall_success_rate >= 60:
            status = "⚠️ جيد"
        else:
            status = "❌ يحتاج تحسين"

        print(f"{status} اكتمل تحسين بدء التشغيل في {total_time:.3f}s")
        print(f"📊 معدل النجاح: {overall_success_rate:.1f}% ({successful_optimizations}/{total_optimizations})")

        return results
    
    def get_metrics(self):
        """الحصول على مقاييس الأداء"""
        return self.startup_metrics
    
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            self.thread_pool.shutdown(wait=False)
        except Exception:
            pass

# إنشاء مثيل عام للمحسن
fast_optimizer = FastStartupOptimizer()

def optimize_startup():
    """دالة سريعة لتحسين بدء التشغيل"""
    return fast_optimizer.run_full_optimization()

def get_startup_metrics():
    """الحصول على مقاييس بدء التشغيل"""
    return fast_optimizer.get_metrics()

# تم إزالة دوال الاختبار
