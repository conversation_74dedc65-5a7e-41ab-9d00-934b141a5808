# -*- coding: utf-8 -*-
"""
نظام مراقبة الأداء المتقدم
Advanced Performance Monitoring System
"""

import os
import time
import psutil
import threading
import json
from datetime import datetime, timedelta
from pathlib import Path
from collections import deque
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional
import sqlite3

@dataclass
class PerformanceMetric:
    """مقياس أداء"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    database_size_mb: float
    active_connections: int
    query_count: int
    cache_hit_rate: float

class AdvancedPerformanceMonitor:
    """مراقب الأداء المتقدم"""
    
    def __init__(self, db_path="accounting.db"):
        self.db_path = db_path
        self.data_dir = Path("data/performance")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # قوائم البيانات
        self.metrics_history = deque(maxlen=1000)  # آخر 1000 قياس
        self.alerts = deque(maxlen=100)  # آخر 100 تنبيه
        self.query_stats = {}
        
        # إعدادات المراقبة
        self.monitoring_active = False
        self.monitoring_interval = 5  # ثواني
        self.monitoring_thread = None
        
        # حدود التنبيهات
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'query_time': 2.0,  # ثواني
            'cache_hit_rate': 50.0  # أقل من 50%
        }
        
        # إحصائيات الجلسة
        self.session_stats = {
            'start_time': datetime.now(),
            'total_queries': 0,
            'slow_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'alerts_generated': 0
        }
        
        self.load_historical_data()
    
    def load_historical_data(self):
        """تحميل البيانات التاريخية"""
        try:
            history_file = self.data_dir / "performance_history.json"
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # تحميل آخر 100 قياس
                for metric_data in data.get('metrics', [])[-100:]:
                    metric = PerformanceMetric(**metric_data)
                    self.metrics_history.append(metric)
                
                print(f"✅ تم تحميل {len(self.metrics_history)} قياس تاريخي")
        except Exception as e:
            print(f"⚠️ فشل في تحميل البيانات التاريخية: {e}")
    
    def save_historical_data(self):
        """حفظ البيانات التاريخية"""
        try:
            history_file = self.data_dir / "performance_history.json"
            data = {
                'last_updated': datetime.now().isoformat(),
                'metrics': [asdict(metric) for metric in self.metrics_history],
                'session_stats': self.session_stats
            }
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"⚠️ فشل في حفظ البيانات التاريخية: {e}")
    
    def collect_system_metrics(self) -> PerformanceMetric:
        """جمع مقاييس النظام"""
        try:
            # مقاييس المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # مقاييس الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            memory_available_mb = memory.available / (1024 * 1024)
            
            # مقاييس القرص
            disk = psutil.disk_usage('.')
            disk_usage_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024 * 1024 * 1024)
            
            # مقاييس قاعدة البيانات
            database_size_mb = 0
            if os.path.exists(self.db_path):
                database_size_mb = os.path.getsize(self.db_path) / (1024 * 1024)
            
            # مقاييس التخزين المؤقت (من النظام المتطور)
            cache_hit_rate = 0
            try:
                from advanced_cache_system import get_cache_system
                cache_stats = get_cache_system().get_stats()
                cache_hit_rate = float(cache_stats['hit_rate'].replace('%', ''))
            except:
                pass
            
            metric = PerformanceMetric(
                timestamp=datetime.now().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_usage_percent=disk_usage_percent,
                disk_free_gb=disk_free_gb,
                database_size_mb=database_size_mb,
                active_connections=1,  # SQLite لا يدعم اتصالات متعددة
                query_count=self.session_stats['total_queries'],
                cache_hit_rate=cache_hit_rate
            )
            
            return metric
            
        except Exception as e:
            print(f"خطأ في جمع مقاييس النظام: {e}")
            return None
    
    def check_alerts(self, metric: PerformanceMetric):
        """فحص التنبيهات"""
        alerts = []
        
        # فحص استخدام المعالج
        if metric.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append({
                'type': 'CPU_HIGH',
                'message': f"استخدام المعالج عالي: {metric.cpu_percent:.1f}%",
                'severity': 'warning' if metric.cpu_percent < 95 else 'critical',
                'timestamp': metric.timestamp
            })
        
        # فحص استخدام الذاكرة
        if metric.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append({
                'type': 'MEMORY_HIGH',
                'message': f"استخدام الذاكرة عالي: {metric.memory_percent:.1f}%",
                'severity': 'warning' if metric.memory_percent < 95 else 'critical',
                'timestamp': metric.timestamp
            })
        
        # فحص استخدام القرص
        if metric.disk_usage_percent > self.alert_thresholds['disk_usage_percent']:
            alerts.append({
                'type': 'DISK_HIGH',
                'message': f"استخدام القرص عالي: {metric.disk_usage_percent:.1f}%",
                'severity': 'warning' if metric.disk_usage_percent < 98 else 'critical',
                'timestamp': metric.timestamp
            })
        
        # فحص معدل إصابة التخزين المؤقت
        if metric.cache_hit_rate < self.alert_thresholds['cache_hit_rate']:
            alerts.append({
                'type': 'CACHE_LOW',
                'message': f"معدل إصابة التخزين المؤقت منخفض: {metric.cache_hit_rate:.1f}%",
                'severity': 'info',
                'timestamp': metric.timestamp
            })
        
        # إضافة التنبيهات
        for alert in alerts:
            self.alerts.append(alert)
            self.session_stats['alerts_generated'] += 1
            print(f"🚨 تنبيه {alert['severity'].upper()}: {alert['message']}")
    
    def start_monitoring(self):
        """بدء المراقبة"""
        if self.monitoring_active:
            print("⚠️ المراقبة نشطة بالفعل")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        print("✅ تم بدء مراقبة الأداء")
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        # حفظ البيانات عند الإيقاف
        self.save_historical_data()
        print("✅ تم إيقاف مراقبة الأداء")
    
    def _monitoring_loop(self):
        """حلقة المراقبة الرئيسية"""
        while self.monitoring_active:
            try:
                # جمع المقاييس
                metric = self.collect_system_metrics()
                if metric:
                    self.metrics_history.append(metric)
                    
                    # فحص التنبيهات
                    self.check_alerts(metric)
                    
                    # حفظ البيانات كل 10 دورات
                    if len(self.metrics_history) % 10 == 0:
                        self.save_historical_data()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                print(f"خطأ في حلقة المراقبة: {e}")
                time.sleep(self.monitoring_interval)
    
    def get_current_status(self) -> Dict:
        """الحصول على الحالة الحالية"""
        if not self.metrics_history:
            return {'status': 'no_data'}
        
        latest_metric = self.metrics_history[-1]
        
        # تحديد الحالة العامة
        status = 'excellent'
        if (latest_metric.cpu_percent > 70 or 
            latest_metric.memory_percent > 80 or 
            latest_metric.disk_usage_percent > 85):
            status = 'warning'
        
        if (latest_metric.cpu_percent > 90 or 
            latest_metric.memory_percent > 95 or 
            latest_metric.disk_usage_percent > 95):
            status = 'critical'
        
        return {
            'status': status,
            'timestamp': latest_metric.timestamp,
            'cpu_percent': latest_metric.cpu_percent,
            'memory_percent': latest_metric.memory_percent,
            'memory_used_gb': latest_metric.memory_used_mb / 1024,
            'disk_usage_percent': latest_metric.disk_usage_percent,
            'disk_free_gb': latest_metric.disk_free_gb,
            'database_size_mb': latest_metric.database_size_mb,
            'cache_hit_rate': latest_metric.cache_hit_rate,
            'uptime': str(datetime.now() - self.session_stats['start_time']),
            'total_queries': self.session_stats['total_queries'],
            'recent_alerts': len([a for a in self.alerts if 
                                datetime.fromisoformat(a['timestamp']) > 
                                datetime.now() - timedelta(hours=1)])
        }
    
    def get_performance_trends(self, hours: int = 24) -> Dict:
        """الحصول على اتجاهات الأداء"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_metrics = [
            m for m in self.metrics_history 
            if datetime.fromisoformat(m.timestamp) > cutoff_time
        ]
        
        if not recent_metrics:
            return {'error': 'no_recent_data'}
        
        # حساب المتوسطات والاتجاهات
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_cache_hit = sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics)
        
        # اتجاه الأداء (مقارنة النصف الأول بالثاني)
        mid_point = len(recent_metrics) // 2
        if mid_point > 0:
            first_half_cpu = sum(m.cpu_percent for m in recent_metrics[:mid_point]) / mid_point
            second_half_cpu = sum(m.cpu_percent for m in recent_metrics[mid_point:]) / (len(recent_metrics) - mid_point)
            cpu_trend = 'improving' if second_half_cpu < first_half_cpu else 'degrading'
        else:
            cpu_trend = 'stable'
        
        return {
            'period_hours': hours,
            'metrics_count': len(recent_metrics),
            'averages': {
                'cpu_percent': round(avg_cpu, 2),
                'memory_percent': round(avg_memory, 2),
                'cache_hit_rate': round(avg_cache_hit, 2)
            },
            'trends': {
                'cpu': cpu_trend,
                'performance_score': self._calculate_performance_score(recent_metrics)
            },
            'peak_usage': {
                'cpu': max(m.cpu_percent for m in recent_metrics),
                'memory': max(m.memory_percent for m in recent_metrics)
            }
        }
    
    def _calculate_performance_score(self, metrics: List[PerformanceMetric]) -> int:
        """حساب نقاط الأداء (0-100)"""
        if not metrics:
            return 0
        
        # حساب النقاط بناءً على المقاييس المختلفة
        avg_cpu = sum(m.cpu_percent for m in metrics) / len(metrics)
        avg_memory = sum(m.memory_percent for m in metrics) / len(metrics)
        avg_cache = sum(m.cache_hit_rate for m in metrics) / len(metrics)
        
        # نقاط المعالج (كلما قل الاستخدام، زادت النقاط)
        cpu_score = max(0, 100 - avg_cpu)
        
        # نقاط الذاكرة
        memory_score = max(0, 100 - avg_memory)
        
        # نقاط التخزين المؤقت
        cache_score = min(100, avg_cache)
        
        # المتوسط المرجح
        total_score = (cpu_score * 0.4 + memory_score * 0.4 + cache_score * 0.2)
        
        return int(total_score)
    
    def generate_performance_report(self) -> Dict:
        """إنشاء تقرير أداء شامل"""
        current_status = self.get_current_status()
        trends_24h = self.get_performance_trends(24)
        trends_1h = self.get_performance_trends(1)
        
        # أهم التنبيهات الأخيرة
        recent_alerts = list(self.alerts)[-10:] if self.alerts else []
        
        # توصيات التحسين
        recommendations = self._generate_recommendations(current_status, trends_24h)
        
        report = {
            'generated_at': datetime.now().isoformat(),
            'current_status': current_status,
            'trends': {
                'last_24_hours': trends_24h,
                'last_hour': trends_1h
            },
            'recent_alerts': recent_alerts,
            'session_statistics': self.session_stats,
            'recommendations': recommendations,
            'system_health': self._assess_system_health(current_status, trends_24h)
        }
        
        return report
    
    def _generate_recommendations(self, status: Dict, trends: Dict) -> List[str]:
        """إنشاء توصيات التحسين"""
        recommendations = []
        
        if status.get('cpu_percent', 0) > 80:
            recommendations.append("تحسين الاستعلامات لتقليل استخدام المعالج")
        
        if status.get('memory_percent', 0) > 85:
            recommendations.append("زيادة ذاكرة النظام أو تحسين إدارة الذاكرة")
        
        if status.get('cache_hit_rate', 100) < 60:
            recommendations.append("تحسين استراتيجية التخزين المؤقت")
        
        if trends.get('averages', {}).get('cpu_percent', 0) > 70:
            recommendations.append("النظر في ترقية الأجهزة أو تحسين الكود")
        
        if not recommendations:
            recommendations.append("الأداء ممتاز! استمر في المراقبة المنتظمة")
        
        return recommendations
    
    def _assess_system_health(self, status: Dict, trends: Dict) -> str:
        """تقييم صحة النظام"""
        score = trends.get('trends', {}).get('performance_score', 0)
        
        if score >= 90:
            return "ممتاز"
        elif score >= 75:
            return "جيد جداً"
        elif score >= 60:
            return "جيد"
        elif score >= 40:
            return "متوسط"
        else:
            return "يحتاج تحسين"

# إنشاء مثيل مراقب الأداء المتقدم
performance_monitor = AdvancedPerformanceMonitor()

def start_performance_monitoring():
    """بدء مراقبة الأداء"""
    performance_monitor.start_monitoring()

def stop_performance_monitoring():
    """إيقاف مراقبة الأداء"""
    performance_monitor.stop_monitoring()

def get_performance_status():
    """الحصول على حالة الأداء"""
    return performance_monitor.get_current_status()

def get_performance_report():
    """الحصول على تقرير الأداء"""
    return performance_monitor.generate_performance_report()

def test_performance_monitor():
    """اختبار مراقب الأداء"""
    print("🧪 اختبار مراقب الأداء المتقدم...")
    
    # بدء المراقبة
    start_performance_monitoring()
    
    # انتظار لجمع بعض البيانات
    time.sleep(10)
    
    # الحصول على الحالة
    status = get_performance_status()
    print(f"✅ الحالة الحالية: {status['status']}")
    print(f"   • استخدام المعالج: {status['cpu_percent']:.1f}%")
    print(f"   • استخدام الذاكرة: {status['memory_percent']:.1f}%")
    print(f"   • معدل إصابة التخزين المؤقت: {status['cache_hit_rate']:.1f}%")
    
    # إنشاء تقرير
    report = get_performance_report()
    print(f"✅ تقرير الأداء:")
    print(f"   • صحة النظام: {report['system_health']}")
    print(f"   • عدد التوصيات: {len(report['recommendations'])}")
    
    # إيقاف المراقبة
    stop_performance_monitoring()

if __name__ == "__main__":
    test_performance_monitor()
