# -*- coding: utf-8 -*-
"""
مدير الصلاحيات
Permissions Manager Module
"""

import json
import os
from datetime import datetime
from pathlib import Path

class PermissionsManager:
    """مدير الصلاحيات والأذونات"""
    
    def __init__(self):
        self.permissions_file = Path("data/permissions.json")
        self.permissions_file.parent.mkdir(exist_ok=True)
        self.default_permissions = {
            "admin": {
                "clients": ["read", "write", "delete"],
                "suppliers": ["read", "write", "delete"],
                "employees": ["read", "write", "delete"],
                "projects": ["read", "write", "delete"],
                "inventory": ["read", "write", "delete"],
                "invoices": ["read", "write", "delete"],
                "reports": ["read", "write", "delete"],
                "settings": ["read", "write", "delete"],
                "backup": ["read", "write", "delete"]
            },
            "user": {
                "clients": ["read", "write"],
                "suppliers": ["read", "write"],
                "employees": ["read"],
                "projects": ["read", "write"],
                "inventory": ["read", "write"],
                "invoices": ["read", "write"],
                "reports": ["read"],
                "settings": ["read"],
                "backup": ["read"]
            },
            "viewer": {
                "clients": ["read"],
                "suppliers": ["read"],
                "employees": ["read"],
                "projects": ["read"],
                "inventory": ["read"],
                "invoices": ["read"],
                "reports": ["read"],
                "settings": [],
                "backup": []
            }
        }
        self.load_permissions()
    
    def load_permissions(self):
        """تحميل الصلاحيات من الملف"""
        try:
            if self.permissions_file.exists():
                with open(self.permissions_file, 'r', encoding='utf-8') as f:
                    self.permissions = json.load(f)
            else:
                self.permissions = self.default_permissions.copy()
                self.save_permissions()
        except Exception as e:
            print(f"خطأ في تحميل الصلاحيات: {e}")
            self.permissions = self.default_permissions.copy()
    
    def save_permissions(self):
        """حفظ الصلاحيات في الملف"""
        try:
            with open(self.permissions_file, 'w', encoding='utf-8') as f:
                json.dump(self.permissions, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الصلاحيات: {e}")
            return False
    
    def create_role(self, role_name, permissions_dict):
        """إنشاء دور جديد"""
        try:
            if role_name in self.permissions:
                return False, "الدور موجود بالفعل"
            
            self.permissions[role_name] = permissions_dict
            if self.save_permissions():
                return True, f"تم إنشاء الدور: {role_name}"
            else:
                return False, "خطأ في حفظ الدور"
        except Exception as e:
            return False, f"خطأ في إنشاء الدور: {e}"
    
    def delete_role(self, role_name):
        """حذف دور"""
        try:
            if role_name not in self.permissions:
                return False, "الدور غير موجود"
            
            if role_name in ["admin", "user", "viewer"]:
                return False, "لا يمكن حذف الأدوار الأساسية"
            
            del self.permissions[role_name]
            if self.save_permissions():
                return True, f"تم حذف الدور: {role_name}"
            else:
                return False, "خطأ في حفظ التغييرات"
        except Exception as e:
            return False, f"خطأ في حذف الدور: {e}"
    
    def update_role_permissions(self, role_name, module, permissions_list):
        """تحديث صلاحيات دور معين"""
        try:
            if role_name not in self.permissions:
                return False, "الدور غير موجود"
            
            self.permissions[role_name][module] = permissions_list
            if self.save_permissions():
                return True, f"تم تحديث صلاحيات {role_name} للوحدة {module}"
            else:
                return False, "خطأ في حفظ التغييرات"
        except Exception as e:
            return False, f"خطأ في تحديث الصلاحيات: {e}"
    
    def check_permission(self, role_name, module, action):
        """فحص صلاحية معينة"""
        try:
            if role_name not in self.permissions:
                return False
            
            if module not in self.permissions[role_name]:
                return False
            
            return action in self.permissions[role_name][module]
        except Exception as e:
            print(f"خطأ في فحص الصلاحية: {e}")
            return False
    
    def get_role_permissions(self, role_name):
        """الحصول على صلاحيات دور معين"""
        try:
            return self.permissions.get(role_name, {})
        except Exception as e:
            print(f"خطأ في الحصول على صلاحيات الدور: {e}")
            return {}
    
    def get_all_roles(self):
        """الحصول على جميع الأدوار"""
        try:
            return list(self.permissions.keys())
        except Exception as e:
            print(f"خطأ في الحصول على الأدوار: {e}")
            return []
    
    def get_modules(self):
        """الحصول على جميع الوحدات"""
        try:
            modules = set()
            for role_permissions in self.permissions.values():
                modules.update(role_permissions.keys())
            return list(modules)
        except Exception as e:
            print(f"خطأ في الحصول على الوحدات: {e}")
            return []
    
    def get_available_actions(self):
        """الحصول على الإجراءات المتاحة"""
        return ["read", "write", "delete", "export", "import"]
    
    def reset_to_default(self):
        """إعادة تعيين الصلاحيات للقيم الافتراضية"""
        try:
            self.permissions = self.default_permissions.copy()
            if self.save_permissions():
                return True, "تم إعادة تعيين الصلاحيات للقيم الافتراضية"
            else:
                return False, "خطأ في حفظ التغييرات"
        except Exception as e:
            return False, f"خطأ في إعادة التعيين: {e}"
    
    def export_permissions(self, export_path):
        """تصدير الصلاحيات إلى ملف"""
        try:
            export_data = {
                "permissions": self.permissions,
                "exported_at": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True, f"تم تصدير الصلاحيات إلى: {export_path}"
        except Exception as e:
            return False, f"خطأ في تصدير الصلاحيات: {e}"
    
    def import_permissions(self, import_path):
        """استيراد الصلاحيات من ملف"""
        try:
            if not os.path.exists(import_path):
                return False, "ملف الاستيراد غير موجود"
            
            with open(import_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if "permissions" not in import_data:
                return False, "ملف الاستيراد غير صحيح"
            
            # إنشاء نسخة احتياطية من الصلاحيات الحالية
            backup_path = f"permissions_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            self.export_permissions(backup_path)
            
            # استيراد الصلاحيات الجديدة
            self.permissions = import_data["permissions"]
            if self.save_permissions():
                return True, f"تم استيراد الصلاحيات من: {import_path}"
            else:
                return False, "خطأ في حفظ الصلاحيات المستوردة"
        except Exception as e:
            return False, f"خطأ في استيراد الصلاحيات: {e}"
    
    def validate_permissions_structure(self, permissions_dict):
        """التحقق من صحة هيكل الصلاحيات"""
        try:
            if not isinstance(permissions_dict, dict):
                return False, "الصلاحيات يجب أن تكون قاموس"
            
            valid_actions = self.get_available_actions()
            
            for role, modules in permissions_dict.items():
                if not isinstance(modules, dict):
                    return False, f"صلاحيات الدور {role} يجب أن تكون قاموس"
                
                for module, actions in modules.items():
                    if not isinstance(actions, list):
                        return False, f"إجراءات الوحدة {module} يجب أن تكون قائمة"
                    
                    for action in actions:
                        if action not in valid_actions:
                            return False, f"الإجراء {action} غير صحيح"
            
            return True, "هيكل الصلاحيات صحيح"
        except Exception as e:
            return False, f"خطأ في التحقق من الصلاحيات: {e}"

# إنشاء مثيل عام
permissions_manager = PermissionsManager()

# دوال مساعدة للاستخدام المباشر
def check_permission(role_name, module, action):
    """فحص صلاحية معينة"""
    return permissions_manager.check_permission(role_name, module, action)

def get_role_permissions(role_name):
    """الحصول على صلاحيات دور معين"""
    return permissions_manager.get_role_permissions(role_name)

def get_all_roles():
    """الحصول على جميع الأدوار"""
    return permissions_manager.get_all_roles()

def create_role(role_name, permissions_dict):
    """إنشاء دور جديد"""
    return permissions_manager.create_role(role_name, permissions_dict)

def update_role_permissions(role_name, module, permissions_list):
    """تحديث صلاحيات دور معين"""
    return permissions_manager.update_role_permissions(role_name, module, permissions_list)

print("✅ تم تحميل مدير الصلاحيات بنجاح")
