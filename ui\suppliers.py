import re
import platform
import csv
import json
import os
import subprocess
import webbrowser
import urllib.parse
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QComboBox, QSizePolicy, QMenu, QAction,
                            QDialog, QFormLayout, QTextEdit, QDoubleSpinBox, QMessageBox,
                            QScrollArea, QGraphicsDropShadowEffect, QDateEdit, QGroupBox,
                            QFileDialog, QApplication, QCheckBox, QListWidget, QListWidgetItem,
                            QShortcut)
from PyQt5.QtCore import Qt, QTimer, QDate, QPoint
from PyQt5.QtCore import QRect
from PyQt5.QtGui import QFont, QColor, QPainter, QPixmap, QBrush, QPen, QIcon, QRadialGradient, QKeySequence
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Supplier, Document, update_supplier_balance
from utils import (format_currency, show_info_message, show_error_message, qdate_to_datetime,
                   auto_create_installment_for_negative_balance, format_datetime_for_export, format_datetime_for_filename)
from ui.common_dialogs import WarningDialog
from sqlalchemy import func
from ui.unified_styles import StyledButton
try:
    from ui.title_bar_utils import TitleBarStyler
except ImportError:
    # إذا لم يكن المودول متاحاً، نستخدم دالة بديلة
    print("تحذير: لا يمكن استيراد TitleBarStyler، سيتم استخدام دالة بديلة")
    class TitleBarStyler:
        @staticmethod
        def apply_advanced_title_bar_styling(dialog):
            print("تحذير: تخصيص شريط العنوان غير متاح")


class SuppliersWidget(QWidget):
    """واجهة إدارة الموردين مطابقة للفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.current_filter_value = None
        self.init_ui()

    def init_ui(self):
        """إنشاء واجهة الموردين مطابقة للفواتير"""
        # تهيئة التحديد المتعدد
        self.selected_items = []

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)
        main_layout.setSpacing(2)

        # العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🚛 إدارة الموردين المتطورة - نظام شامل ومتقدم لإدارة الموردين مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي للبحث والتصفية متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(3)  # مسافة أقل بين العناصر

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(4, 0, 4, 0)  # هوامش جانبية أقل
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث مطورة بألوان احترافية مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث باسم المورد، الهاتف، البريد أو العنوان...")
        self.search_edit.textChanged.connect(self.filter_suppliers)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_suppliers)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        search_frame.setLayout(top_container)

        # إنشاء الجدول
        self.create_suppliers_table()

        # إنشاء إطار الأزرار
        self.create_action_buttons()

        # إضافة العناصر للتخطيط الرئيسي
        main_layout.addWidget(search_frame)
        main_layout.addWidget(self.suppliers_table, 1)
        main_layout.addWidget(self.buttons_frame)

        self.setLayout(main_layout)

        # تهيئة المتغيرات
        self.current_filter_value = None

        # ربط الأحداث
        self.search_edit.textChanged.connect(self.filter_suppliers)

        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(50, self.refresh_data)

        # تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة
        QTimer.singleShot(75, self.initialize_button_states)

    def create_suppliers_table(self):
        """إنشاء جدول الموردين مطابق للعملاء مع 9 أعمدة"""
        # إنشاء الجدول
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(9)

        # عناوين الأعمدة مع الأيقونات مطابقة للعملاء (الترتيب الجديد مع الملاحظات والتاريخ في النهاية)
        headers = [
            "🔢 ID",
            "🚛 اسم المورد",
            "🏠 العنوان",
            "📧 البريد الإلكتروني",
            "📱 رقم الهاتف",
            "💵 الرصيد",
            "⭐ حالة المورد",
            "📋 الملاحظات",
            "🗓️ تاريخ الإضافة"
        ]

        self.suppliers_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مع التحديد المتعدد
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.suppliers_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_table.setAlternatingRowColors(False)
        self.suppliers_table.setSortingEnabled(True)

        # إعدادات الصفوف والأعمدة مطابقة للعملاء
        self.suppliers_table.verticalHeader().setDefaultSectionSize(50)
        self.suppliers_table.verticalHeader().setVisible(False)

        header = self.suppliers_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)

        # إضافة خاصية التكيف التلقائي مطابقة للفواتير والعملاء مع مقاسات مخصصة
        header.setSectionResizeMode(QHeaderView.Stretch)

        # تعيين مقاسات ثابتة لأعمدة محددة مع الحفاظ على التكيف التلقائي للباقي
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # عمود اسم المورد
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # عمود العنوان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # عمود البريد الإلكتروني
        self.suppliers_table.setColumnWidth(0, 120)  # ID - 120 بكسل
        self.suppliers_table.setColumnWidth(1, 300)  # اسم المورد - 300 بكسل
        self.suppliers_table.setColumnWidth(2, 300)  # العنوان - 300 بكسل
        self.suppliers_table.setColumnWidth(3, 250)  # البريد الإلكتروني - 250 بكسل

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.suppliers_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.suppliers_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط التمرير للموردين: {e}")
            # المتابعة بدون تصميم متقدم لشريط التمرير

        # تطبيق التصميم والتفاعل مطابق للعملاء
        self.apply_table_style()
        self.add_watermark_to_table()
        self.setup_table_interactions()

    def filter_suppliers(self):
        """تصفية الموردين بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = self.current_filter_value

            # بناء الاستعلام
            query = self.session.query(Supplier)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Supplier.name.like(f"%{search_text}%") |
                    Supplier.phone.like(f"%{search_text}%") |
                    Supplier.email.like(f"%{search_text}%") |
                    Supplier.address.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status == "active":
                query = query.filter(Supplier.balance > 0)
            elif status == "normal":
                query = query.filter(Supplier.balance == 0)
            elif status == "debtor":
                query = query.filter(Supplier.balance < 0)

            # تنفيذ الاستعلام (من الأقدم للأحدث)
            suppliers = query.order_by(Supplier.id.asc()).all()

            # تحديث الجدول والملخص
            self.populate_table(suppliers)
            self.update_summary(suppliers)

        except Exception as e:
            print(f"حدث خطأ أثناء تصفية البيانات: {str(e)}")

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول مطابق للفواتير"""
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));

                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية للجدول مطابقة للفواتير"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))

            # رسم النص الرئيسي بحجم خط كبير جداً (180) في منتصف الارتفاع
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)

            # حساب منتصف الارتفاع
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")

            painter.restore()

        # تطبيق العلامة المائية
        original_paint = self.suppliers_table.paintEvent
        def new_paint_event(event):
            original_paint(event)
            painter = QPainter(self.suppliers_table.viewport())
            paint_watermark(painter, self.suppliers_table.viewport().rect())
            painter.end()

        self.suppliers_table.paintEvent = new_paint_event

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول مع التحديد المتعدد"""
        self.suppliers_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.suppliers_table.cellDoubleClicked.connect(self.edit_supplier)
        self.setup_multi_selection()

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.suppliers_table.itemAt(event.pos())
            if item is None:
                self.suppliers_table.clearSelection()
            QTableWidget.mousePressEvent(self.suppliers_table, event)

        self.suppliers_table.mousePressEvent = mousePressEvent

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.suppliers_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.suppliers_table, event)

        self.suppliers_table.wheelEvent = wheelEvent

    def on_selection_changed(self):
        """معالج تغيير التحديد مع دعم التحديد المتعدد"""
        try:
            self.update_selected_items()
            selected_count = len(self.selected_items)
            has_selection = selected_count > 0
            has_single = selected_count == 1

            # تفعيل/تعطيل الأزرار حسب التحديد مع الشفافية 50%
            if hasattr(self, 'add_button'):
                self.set_button_visibility(self.add_button, True)  # زر الإضافة متاح دائماً
            if hasattr(self, 'edit_button'):
                self.set_button_visibility(self.edit_button, has_single)
            if hasattr(self, 'delete_button'):
                self.set_button_visibility(self.delete_button, has_selection)
                if selected_count > 1:
                    self.delete_button.setText(f"🗑️ حذف ({selected_count})")
                else:
                    self.delete_button.setText("🗑️ حذف")
            if hasattr(self, 'view_button'):
                self.set_button_visibility(self.view_button, has_single)
            if hasattr(self, 'add_payment_button'):
                self.set_button_visibility(self.add_payment_button, has_single)
            if hasattr(self, 'documents_button'):
                self.set_button_visibility(self.documents_button, has_single)
            if hasattr(self, 'whatsapp_button'):
                self.set_button_visibility(self.whatsapp_button, has_single)  # اتصال واتساب يحتاج تحديد مورد واحد
        except Exception as e:
            print(f"خطأ في معالجة التحديد: {e}")

    def set_button_visibility(self, button, enabled):
        """تعيين حالة الزر مع تأثير الظهور/الاختفاء السلس - شفافية 50% عند التعطيل"""
        try:
            if enabled:
                # إظهار الزر بشفافية كاملة مع إعادة تفعيل الأحداث
                print(f"🟢 تفعيل الزر: {button.text()}")

                # إزالة أي تأثيرات شفافية سابقة
                button.setGraphicsEffect(None)

                # إعادة تفعيل الأحداث
                button.setAttribute(Qt.WA_TransparentForMouseEvents, False)
                button.setFocusPolicy(Qt.StrongFocus)

                # إزالة خاصية التعطيل المخصصة
                button.setProperty("custom_disabled", False)

                # التأكد من أن الزر مفعل
                button.setEnabled(True)
                button.show()
            else:
                # تعطيل الزر مع الحفاظ على الألوان الأصلية - الحل النهائي
                print(f"🔴 تعطيل الزر: {button.text()}")

                # لا نستخدم setEnabled(False) للحفاظ على الألوان
                # بدلاً من ذلك نمنع الأحداث ونطبق الشفافية

                # استخدام QGraphicsOpacityEffect لضمان ظهور الشفافية 50%
                from PyQt5.QtWidgets import QGraphicsOpacityEffect
                opacity_effect = QGraphicsOpacityEffect()
                opacity_effect.setOpacity(0.5)  # 50% شفافية
                button.setGraphicsEffect(opacity_effect)

                # منع جميع الأحداث دون تغيير المظهر
                button.setAttribute(Qt.WA_TransparentForMouseEvents, True)
                button.setFocusPolicy(Qt.NoFocus)

                # إضافة خاصية مخصصة لتتبع حالة التعطيل
                button.setProperty("custom_disabled", True)

        except Exception as e:
            # في حالة الخطأ، استخدم الطريقة التقليدية
            button.setEnabled(enabled)

    def edit_supplier(self):
        """تعديل بيانات مورد"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                show_supplier_advanced_warning(self, "تحذير", error)
                return

            # الحصول على بيانات المورد من قاعدة البيانات
            supplier = self.session.query(Supplier).get(supplier_id)
            if supplier:
                # فتح نافذة التعديل (نفس نافذة الإضافة مع تحميل البيانات)
                dialog = AddSupplierDialog(self.session, self, supplier)
                if dialog.exec_() == QDialog.Accepted:
                    self.refresh_data()  # تحديث الجدول بعد التعديل
            else:
                show_supplier_advanced_error(self, "خطأ", "لم يتم العثور على المورد المحدد")
        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في تعديل المورد: {str(e)}")

    def get_selected_supplier_id(self):
        """استخراج معرف المورد المحدد من الجدول مطابق للفواتير"""
        try:
            selected_row = self.suppliers_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار مورد من القائمة"

            if not self.suppliers_table.item(selected_row, 0):
                return None, "الرجاء اختيار مورد صالح من القائمة"

            # استخراج الـ ID الفعلي من البيانات المخفية
            id_item = self.suppliers_table.item(selected_row, 0)
            supplier_id = id_item.data(Qt.UserRole)
            if not supplier_id:
                return None, "لا يمكن استخراج رقم المورد"
            return supplier_id, None

        except Exception as e:
            return None, f"خطأ في استخراج معرف المورد: {str(e)}"

    def setup_multi_selection(self):
        """إعداد التحديد المتعدد المبسط"""
        try:

            # اختصارات لوحة المفاتيح
            QShortcut(QKeySequence.SelectAll, self.suppliers_table, self.select_all)
            QShortcut(QKeySequence("Ctrl+D"), self.suppliers_table, self.deselect_all)
            QShortcut(QKeySequence.Delete, self.suppliers_table, self.delete_selected)

            # القائمة السياقية
            self.suppliers_table.setContextMenuPolicy(Qt.CustomContextMenu)
            self.suppliers_table.customContextMenuRequested.connect(self.show_context_menu)
        except Exception as e:
            print(f"خطأ في إعداد التحديد المتعدد: {e}")

    def update_selected_items(self):
        """تحديث قائمة العناصر المحددة"""
        try:
            self.selected_items = []
            for item in self.suppliers_table.selectedItems():
                if item.column() == 0:  # عمود ID
                    id_text = ''.join(filter(str.isdigit, item.text()))
                    if id_text:
                        self.selected_items.append(int(id_text))
        except Exception as e:
            print(f"خطأ في تحديث العناصر المحددة: {e}")

    def select_all(self):
        """تحديد جميع العناصر"""
        self.suppliers_table.selectAll()
        self.update_selected_items()

    def deselect_all(self):
        """إلغاء جميع التحديدات"""
        self.suppliers_table.clearSelection()
        self.selected_items = []

    def delete_selected(self):
        """حذف العناصر المحددة"""
        try:
            self.update_selected_items()
            if not self.selected_items:
                return

            count = len(self.selected_items)
            if count == 1:
                self.delete_supplier()
            else:
                if show_supplier_advanced_confirmation(self, "تأكيد الحذف", f"هل تريد حذف {count} مورد؟"):
                    for item_id in self.selected_items:
                        supplier = self.session.query(Supplier).get(item_id)
                        if supplier:
                            self.session.delete(supplier)
                    self.session.commit()
                    self.refresh_data()
        except Exception as e:
            print(f"خطأ في حذف العناصر: {e}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية"""
        try:

            self.update_selected_items()
            menu = QMenu(self)

            if len(self.selected_items) <= 1:
                menu.addAction("✏️ تعديل", self.edit_supplier)
                menu.addAction("👁️ عرض التفاصيل", self.view_supplier_details)
                menu.addSeparator()
                menu.addAction("🗑️ حذف", self.delete_supplier)
            else:
                menu.addAction(f"🗑️ حذف {len(self.selected_items)} مورد", self.delete_selected)

            menu.addSeparator()
            menu.addAction("✅ تحديد الكل", self.select_all)
            menu.addAction("❌ إلغاء التحديد", self.deselect_all)

            menu.exec_(self.suppliers_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في القائمة السياقية: {e}")

    def refresh_data(self):
        """تحديث بيانات الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً مع حماية إضافية
            if hasattr(self, 'refresh_button') and self.refresh_button is not None:
                try:
                    if hasattr(self.refresh_button, 'setEnabled') and callable(self.refresh_button.setEnabled):
                        self.refresh_button.setEnabled(False)
                        self.refresh_button.setText("🔄 جاري التحديث...")
                except RuntimeError as re:
                    print(f"تم تجاهل تحديث زر التحديث: الكائن محذوف - {re}")
                except Exception as be:
                    print(f"خطأ في تحديث زر التحديث: {be}")

            # جلب جميع الموردين من قاعدة البيانات (من الأقدم للأحدث)
            suppliers = self.session.query(Supplier).order_by(Supplier.id.asc()).all()
            self.populate_table(suppliers)
            self.update_summary(suppliers)

        except Exception as e:
            print(f"حدث خطأ أثناء تحديث البيانات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def update_summary(self, suppliers):
        """تحديث ملخص الموردين مطابق للفواتير"""
        try:
            # حساب الإحصائيات
            total_suppliers = len(suppliers)
            active_suppliers = len([s for s in suppliers if (s.balance or 0) > 0])
            debtor_suppliers = len([s for s in suppliers if (s.balance or 0) < 0])
            normal_suppliers = len([s for s in suppliers if (s.balance or 0) == 0])

            # حساب المبالغ
            total_positive = sum(s.balance for s in suppliers if (s.balance or 0) > 0)
            total_negative = sum(abs(s.balance) for s in suppliers if (s.balance or 0) < 0)
            net_balance = sum(s.balance or 0 for s in suppliers)

            # تحديث النصوص (إذا كان هناك عنصر ملخص) مع حماية إضافية
            if hasattr(self, 'summary_label') and self.summary_label is not None:
                try:
                    if hasattr(self.summary_label, 'setText') and callable(self.summary_label.setText):
                        summary_text = (f"إجمالي الموردين: {total_suppliers} | "
                                      f"نشط: {active_suppliers} | "
                                      f"مدين: {debtor_suppliers} | "
                                      f"عادي: {normal_suppliers}")
                        self.summary_label.setText(summary_text)
                except RuntimeError as re:
                    print(f"تم تجاهل تحديث حالة المزامنة: كائن الملخص محذوف - {re}")
                except Exception as le:
                    print(f"خطأ في تحديث تسمية الملخص: {le}")

            if hasattr(self, 'balance_label') and self.balance_label is not None:
                try:
                    if hasattr(self.balance_label, 'setText') and callable(self.balance_label.setText):
                        balance_text = (f"الأرصدة الموجبة: {format_currency(total_positive)} | "
                                      f"الأرصدة السالبة: {format_currency(total_negative)} | "
                                      f"صافي الرصيد: {format_currency(net_balance)}")
                        self.balance_label.setText(balance_text)
                except RuntimeError as re:
                    print(f"تم تجاهل تحديث حالة المزامنة: كائن الرصيد محذوف - {re}")
                except Exception as le:
                    print(f"خطأ في تحديث تسمية الرصيد: {le}")



        except Exception as e:
            print(f"خطأ في تحديث ملخص الموردين: {str(e)}")

    def populate_table(self, suppliers):
        """ملء الجدول بالبيانات مطابق للفواتير"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.suppliers_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.suppliers_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن مطابق للفواتير
            for row, supplier in enumerate(suppliers):
                try:
                    self.suppliers_table.insertRow(row)

                    # 1. الرقم التسلسلي مع أيقونة حسب الرصيد مطابق للعملاء
                    balance_value = supplier.balance or 0
                    if balance_value > 0:
                        id_icon = "💰"
                    elif balance_value < 0:
                        id_icon = "🔴"
                    else:
                        id_icon = "🔢"

                    # إنشاء عنصر ID مع رقم متسلسل بدلاً من ID الفعلي
                    sequential_number = row + 1
                    id_item = QTableWidgetItem(f"{id_icon} {sequential_number}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
                    # حفظ الـ ID الفعلي كبيانات مخفية للاستخدام في العمليات
                    id_item.setData(Qt.UserRole, supplier.id)
                    self.suppliers_table.setItem(row, 0, id_item)

                    # دالة مساعدة لإنشاء العناصر مطابق للعملاء
                    def create_item(icon, text, default="No Data"):
                        display_text = text if text and text.strip() else default
                        item = QTableWidgetItem(f"{icon} {display_text}")
                        item.setTextAlignment(Qt.AlignCenter)
                        if display_text == default:
                            item.setForeground(QColor("#ef4444"))
                        return item

                    self.suppliers_table.setItem(row, 1, create_item("🚛", supplier.name))
                    self.suppliers_table.setItem(row, 2, create_item("🏠", supplier.address))
                    self.suppliers_table.setItem(row, 3, create_item("📧", supplier.email))

                    self.suppliers_table.setItem(row, 4, create_item("📱", supplier.phone))

                    # 6. الرصيد مع ألوان حسب القيمة مطابق للعملاء
                    balance_value = supplier.balance or 0
                    balance_text = format_currency(balance_value)
                    if balance_value > 0:
                        balance_item = QTableWidgetItem(f"💰 {balance_text}")
                        balance_item.setForeground(QColor("#059669"))  # أخضر للموجب
                    elif balance_value < 0:
                        balance_item = QTableWidgetItem(f"💸 {balance_text}")
                        balance_item.setForeground(QColor("#dc2626"))  # أحمر للسالب
                    else:
                        balance_item = QTableWidgetItem(f"💵 {balance_text}")
                        balance_item.setForeground(QColor("#000000"))  # أسود للصفر
                    balance_item.setTextAlignment(Qt.AlignCenter)
                    self.suppliers_table.setItem(row, 5, balance_item)

                    # 7. حالة المورد مطابق للعملاء
                    status_item = QTableWidgetItem(self.get_supplier_status(balance_value))
                    status_item.setTextAlignment(Qt.AlignCenter)
                    self.suppliers_table.setItem(row, 6, status_item)

                    # الملاحظات والتاريخ مطابق للعملاء
                    notes_text = supplier.notes if hasattr(supplier, 'notes') and supplier.notes and supplier.notes.strip() else None
                    date_text = supplier.created_at.strftime("%Y-%m-%d") if hasattr(supplier, 'created_at') and supplier.created_at else None

                    self.suppliers_table.setItem(row, 7, create_item("📋", notes_text))
                    self.suppliers_table.setItem(row, 8, create_item("🗓️", date_text))

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تفعيل تحديث الجدول
            self.suppliers_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تفعيل تحديث الجدول في حالة الخطأ
            self.suppliers_table.setUpdatesEnabled(True)
            print(f"حدث خطأ أثناء تحديث جدول الموردين: {str(e)}")

    def get_supplier_status(self, balance):
        """تحديد حالة المورد بناءً على الرصيد"""
        if balance > 0:
            return "🟢 نشط"
        elif balance == 0:
            return "🟡 عادي"
        else:
            return "🔴 مدين"



    def create_action_buttons(self):
        """إنشاء إطار الأزرار مطابق للفواتير"""

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        self.buttons_frame = QFrame()
        self.buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار النظيفة والمرتبة مطابق للفواتير

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة مورد")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_supplier)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_supplier)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_supplier)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')
        self.view_button.clicked.connect(self.view_supplier)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر تعديل المبلغ (إضافة أو تقليل)
        self.add_payment_button = QPushButton("💰 تعديل المبلغ")
        self.style_advanced_button(self.add_payment_button, 'orange')
        self.add_payment_button.clicked.connect(self.add_payment)
        self.add_payment_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)



        # زر التصدير المتطور مطابق للعملاء
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الاتصالات (واتساب)
        self.whatsapp_button = QPushButton("📞 إتصال واتساب")
        self.style_advanced_button(self.whatsapp_button, 'warning')
        self.whatsapp_button.clicked.connect(self.show_whatsapp_options)
        self.whatsapp_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير مع تحديد العرض والموضع
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 5px 10px 5px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: left;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                padding-left: 110px !important;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(self.export_to_excel_advanced)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(self.export_to_csv_advanced)
        export_menu.addAction(csv_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        detailed_action = QAction("📊 تقرير تفصيلي", self)
        detailed_action.triggered.connect(self.export_detailed_report)
        export_menu.addAction(detailed_action)

        balance_action = QAction("💰 تقرير الأرصدة", self)
        balance_action.triggered.connect(self.export_balance_report)
        export_menu.addAction(balance_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(self.export_custom)
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(self.export_backup)
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(self.restore_backup)
        export_menu.addAction(restore_action)

        # تخصيص موضع وعرض القائمة المتطورة
        def show_export_menu():
            """عرض قائمة التصدير فوق الزر مباشرة بنفس العرض مع تحسينات متقدمة"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.export_button.mapToGlobal(self.export_button.rect().topLeft())

            # تحديد عرض القائمة مع تكبير ربع درجة
            button_width = self.export_button.width()
            export_menu.setFixedWidth(max(button_width, 190))

            # ترحيل القائمة نصف درجة لليسار
            button_pos.setX(button_pos.x() - 10)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = export_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            export_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة المتطورة
        self.export_button.clicked.connect(show_export_menu)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إدارة الوثائق - مطابق للعملاء
        self.documents_button = QPushButton("📁 إدارة الوثائق")
        self.style_advanced_button(self.documents_button, 'gray')
        self.documents_button.clicked.connect(self.manage_documents)
        self.documents_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()



        # إضافة الأزرار للتخطيط - مطابق تماماً لترتيب العملاء
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.add_payment_button)
        actions_layout.addWidget(self.documents_button)
        actions_layout.addWidget(self.whatsapp_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.columns_visibility_button)

        # تعيين التخطيط للإطار السفلي
        self.buttons_frame.setLayout(bottom_container)

        # تعطيل الأزرار التي تحتاج تحديد عنصر مع الشفافية 50%
        self.set_button_visibility(self.add_button, False)  # إضافة مورد
        self.set_button_visibility(self.edit_button, False)
        self.set_button_visibility(self.delete_button, False)
        self.set_button_visibility(self.view_button, False)
        self.set_button_visibility(self.add_payment_button, False)
        self.set_button_visibility(self.documents_button, False)
        self.set_button_visibility(self.whatsapp_button, False)  # اتصال واتساب

    def closeEvent(self, event):
        """معالج إغلاق النافذة - مطابق للعملاء"""
        try:
            # يمكن إضافة أي عمليات تنظيف هنا إذا لزم الأمر
            event.accept()
        except Exception as e:
            print(f"خطأ في إغلاق نافذة الموردين: {e}")
            event.accept()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة - مطابق تماماً للعملاء"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق تماماً للعملاء
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                },
                'purple': {
                    'bg_start': '#581c87', 'bg_mid': '#7c3aed', 'bg_end': '#8b5cf6', 'bg_bottom': '#a855f7',
                    'hover_start': '#a855f7', 'hover_mid': '#c084fc', 'hover_end': '#d8b4fe', 'hover_bottom': '#e9d5ff',
                    'hover_border': '#c084fc', 'pressed_start': '#3b0764', 'pressed_mid': '#581c87',
                    'pressed_end': '#6b21a8', 'pressed_bottom': '#7c3aed', 'pressed_border': '#6b21a8',
                    'border': '#a855f7', 'text': '#ffffff', 'shadow': 'rgba(168, 85, 247, 0.6)'
                },
                'gray': {
                    'bg_start': '#2d3748', 'bg_mid': '#4a5568', 'bg_end': '#718096', 'bg_bottom': '#a0aec0',
                    'hover_start': '#a0aec0', 'hover_mid': '#cbd5e0', 'hover_end': '#e2e8f0', 'hover_bottom': '#f7fafc',
                    'hover_border': '#cbd5e0', 'pressed_start': '#1a202c', 'pressed_mid': '#2d3748',
                    'pressed_end': '#4a5568', 'pressed_bottom': '#718096', 'pressed_border': '#4a5568',
                    'border': '#718096', 'text': '#ffffff', 'shadow': 'rgba(113, 128, 150, 0.8)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#2d2d2d', 'bg_bottom': '#404040',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#1a1a1a', 'pressed_bottom': '#2d2d2d', 'pressed_border': '#1a1a1a',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                },
                'warning': {
                    'bg_start': '#451a03', 'bg_mid': '#78350f', 'bg_end': '#a16207', 'bg_bottom': '#eab308',
                    'hover_start': '#78350f', 'hover_mid': '#ca8a04', 'hover_end': '#eab308', 'hover_bottom': '#facc15',
                    'hover_border': '#eab308', 'pressed_start': '#451a03', 'pressed_mid': '#78350f',
                    'pressed_end': '#a16207', 'pressed_bottom': '#ca8a04', 'pressed_border': '#a16207',
                    'border': '#eab308', 'text': '#ffffff', 'shadow': 'rgba(234, 179, 8, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"تحذير: فشل في رسم العلامة المائية للموردين: {e}")
            # المتابعة بدون العلامة المائية

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)  # إزالة الهوامش العمودية
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)  # توسيط عمودي للعناصر

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر
        filter_options = [
            ("جميع الحالات", None),
            ("🟢 نشط (رصيد موجب)", "active"),
            ("🟡 عادي (رصيد صفر)", "normal"),
            ("🔴 مدين (رصيد سالب)", "debtor")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_suppliers()

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        self.columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        self.columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("🚛 اسم المورد", 1),
            ("🏠 العنوان", 2),
            ("📧 البريد الإلكتروني", 3),
            ("📱 رقم الهاتف", 4),
            ("💵 الرصيد", 5),
            ("⭐ حالة المورد", 6),
            ("📋 الملاحظات", 7),
            ("🗓️ تاريخ الإضافة", 8)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            self.columns_menu.addAction(action)

        # إضافة فاصل
        self.columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        self.columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        self.columns_menu.addAction(hide_all_action)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            self.columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = self.columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            self.columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'suppliers_table') and self.suppliers_table:
                if visible:
                    self.suppliers_table.showColumn(column_index)
                else:
                    self.suppliers_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"خطأ في تبديل إظهار العمود: {e}")

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'suppliers_table') and self.suppliers_table:
                for i in range(self.suppliers_table.columnCount()):
                    self.suppliers_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"خطأ في إظهار جميع الأعمدة: {e}")

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'suppliers_table') and self.suppliers_table:
                for i in range(self.suppliers_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.suppliers_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"خطأ في إخفاء الأعمدة: {e}")

    # ==================== دوال معالجة أحداث الأزرار ====================

    def add_supplier(self):
        """إضافة مورد جديد"""
        try:
            dialog = AddSupplierDialog(self.session, self)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()  # تحديث الجدول بعد الإضافة
        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في إضافة المورد: {str(e)}")

    def get_selected_supplier_id(self):
        """الحصول على معرف المورد المحدد"""
        try:
            current_row = self.suppliers_table.currentRow()
            if current_row >= 0:
                # استخراج الـ ID الفعلي من البيانات المخفية
                id_item = self.suppliers_table.item(current_row, 0)
                supplier_id = id_item.data(Qt.UserRole)
                if supplier_id:
                    return supplier_id, None
                else:
                    return None, "لا يمكن الحصول على معرف المورد"
            else:
                return None, "يرجى اختيار مورد من القائمة"
        except Exception as e:
            return None, f"خطأ في تحديد المورد: {str(e)}"

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مشابهة لنوافذ البرنامج"""
        dialog = WarningDialog(self, message)
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة"""
        show_supplier_advanced_info(self, "نجح", message)

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة"""
        show_supplier_advanced_error(self, "خطأ", message)

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة"""
        show_supplier_advanced_warning(self, "تحذير", message)

    def delete_supplier(self):
        """حذف مورد مع نافذة تأكيد متطورة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if supplier_id:
                # الحصول على بيانات المورد
                supplier = self.session.query(Supplier).filter(Supplier.id == supplier_id).first()
                if supplier:
                    # إنشاء نافذة حذف مشابهة لنافذة العملاء
                    dialog = DeleteSupplierDialog(self, supplier)
                    if dialog.exec_() == QDialog.Accepted:
                        try:
                            # حذف المورد من قاعدة البيانات
                            self.session.delete(supplier)
                            self.session.commit()

                            # إظهار رسالة نجاح متطورة
                            self.show_success_message(f"تم حذف المورد '{supplier.name}' بنجاح")

                            # تحديث الجدول
                            self.refresh_data()

                        except Exception as e:
                            self.session.rollback()
                            self.show_error_message(f"فشل في حذف المورد: {str(e)}")
                else:
                    self.show_error_message("لم يتم العثور على المورد المحدد")
            else:
                self.show_warning_message("يرجى اختيار مورد للحذف")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف المورد: {str(e)}")

    def view_supplier(self):
        """عرض تفاصيل المورد في نافذة متطورة"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                show_supplier_advanced_warning(self, "تحذير", "يرجى اختيار مورد لعرض تفاصيله")
                return

            print(f"عرض تفاصيل المورد رقم: {supplier_id}")
            supplier = self.session.query(Supplier).filter(Supplier.id == supplier_id).first()
            if supplier:
                # إنشاء نافذة المعلومات المتطورة
                info_dialog = SupplierInfoDialog(self, supplier)
                info_dialog.exec_()
            else:
                show_supplier_advanced_error(self, "خطأ", "لم يتم العثور على المورد")
        except Exception as e:
            print(f"خطأ في عرض تفاصيل المورد: {str(e)}")
            self.show_error_message(f"حدث خطأ في عرض تفاصيل المورد: {str(e)}")

    def add_payment(self):
        """تعديل رصيد المورد - إضافة أو تقليل المبلغ"""
        try:
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                show_supplier_advanced_warning(self, "تحذير", error)
                return

            supplier = self.session.query(Supplier).get(supplier_id)
            if not supplier:
                show_supplier_advanced_error(self, "خطأ", "لم يتم العثور على المورد")
                return

            # فتح نافذة تعديل المبلغ
            dialog = EditSupplierAmountDialog(self, supplier, self.session)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ: {str(e)}")

    def export_to_excel(self):
        """تصدير الموردين إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير الموردين إلى CSV"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", f"الموردين_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                suppliers = self.session.query(Supplier).order_by(Supplier.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التصدير
                    writer.writerow(['تصدير الموردين'])
                    writer.writerow([f'تاريخ التصدير: {format_datetime_for_export()}'])
                    writer.writerow([f'إجمالي الموردين: {len(suppliers)}'])
                    writer.writerow([])

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'اسم المورد', 'الهاتف', 'العنوان', 'البريد الإلكتروني', 'الرصيد', 'الملاحظات'])

                    # كتابة البيانات
                    for supplier in suppliers:
                        writer.writerow([
                            supplier.id,
                            supplier.name,
                            supplier.phone or "",
                            supplier.address or "",
                            supplier.email or "",
                            supplier.balance or 0,
                            supplier.notes or ""
                        ])

                show_supplier_advanced_info(self, "تم", f"تم تصدير الموردين بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في التصدير: {str(e)}")

    def export_to_excel_advanced(self):
        """تصدير بيانات الموردين إلى Excel متقدم مع تنسيق وإحصائيات"""
        try:

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel متقدم", f"الموردين_متقدم_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                suppliers = self.session.query(Supplier).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة عنوان التقرير
                    writer.writerow(['تقرير الموردين المتقدم'])
                    writer.writerow([f'تاريخ التصدير: {format_datetime_for_export()}'])
                    writer.writerow([f'إجمالي الموردين: {len(suppliers)}'])
                    writer.writerow([])  # سطر فارغ

                    # حساب الإحصائيات
                    total_balance = sum(supplier.balance for supplier in suppliers)
                    positive_balance = sum(supplier.balance for supplier in suppliers if supplier.balance > 0)
                    negative_balance = sum(supplier.balance for supplier in suppliers if supplier.balance < 0)

                    writer.writerow(['الإحصائيات العامة'])
                    writer.writerow(['إجمالي الأرصدة', f'{total_balance:,.2f} جنيه'])
                    writer.writerow(['الأرصدة الموجبة', f'{positive_balance:,.2f} جنيه'])
                    writer.writerow(['الأرصدة السالبة', f'{negative_balance:,.2f} جنيه'])
                    writer.writerow([])  # سطر فارغ

                    # كتابة رؤوس الأعمدة
                    headers = [
                        'الرقم التعريفي', 'اسم المورد', 'رقم الهاتف', 'العنوان',
                        'البريد الإلكتروني', 'الرصيد', 'حالة الرصيد', 'الملاحظات',
                        'تاريخ الإنشاء', 'آخر تحديث'
                    ]
                    writer.writerow(headers)

                    # كتابة بيانات الموردين
                    for supplier in suppliers:
                        balance_status = 'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'

                        row = [
                            supplier.id,
                            supplier.name or '',
                            supplier.phone or '',
                            supplier.address or '',
                            supplier.email or '',
                            f'{supplier.balance:,.2f}',
                            balance_status,
                            supplier.notes or '',
                            format_datetime_for_export(supplier.created_at) if supplier.created_at else '',
                            format_datetime_for_export(supplier.updated_at) if hasattr(supplier, 'updated_at') and supplier.updated_at else ''
                        ]
                        writer.writerow(row)

                show_supplier_advanced_info(self, "تم", f"تم تصدير الموردين بتنسيق متقدم بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في التصدير المتقدم: {str(e)}")

    def export_to_csv_advanced(self):
        """تصدير بيانات الموردين إلى CSV شامل مع تفاصيل إضافية"""
        try:

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV شامل", f"الموردين_شامل_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                suppliers = self.session.query(Supplier).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التقرير
                    writer.writerow(['نظام إدارة الموردين - تقرير شامل'])
                    writer.writerow([f'تاريخ الإنشاء: {format_datetime_for_export()}'])
                    writer.writerow([f'عدد الموردين: {len(suppliers)}'])
                    writer.writerow([])

                    # تحليل البيانات
                    active_suppliers = len([s for s in suppliers if s.balance != 0])
                    inactive_suppliers = len(suppliers) - active_suppliers

                    writer.writerow(['تحليل الموردين'])
                    writer.writerow(['الموردين النشطين', active_suppliers])
                    writer.writerow(['الموردين غير النشطين', inactive_suppliers])
                    writer.writerow([])

                    # البيانات التفصيلية
                    headers = [
                        'ID', 'الاسم الكامل', 'الهاتف الأساسي', 'الهاتف الثانوي',
                        'العنوان التفصيلي', 'البريد الإلكتروني', 'الرصيد الحالي',
                        'نوع المورد', 'الملاحظات', 'تاريخ التسجيل', 'حالة النشاط'
                    ]
                    writer.writerow(headers)

                    for supplier in suppliers:
                        supplier_type = 'VIP' if supplier.balance > 10000 else 'عادي' if supplier.balance >= 0 else 'مدين'
                        activity_status = 'نشط' if supplier.balance != 0 else 'غير نشط'

                        row = [
                            supplier.id,
                            supplier.name or 'غير محدد',
                            supplier.phone or 'غير محدد',
                            getattr(supplier, 'phone2', '') or 'غير محدد',
                            supplier.address or 'غير محدد',
                            supplier.email or 'غير محدد',
                            f'{supplier.balance:,.2f} جنيه',
                            supplier_type,
                            supplier.notes or 'لا توجد ملاحظات',
                            supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد',
                            activity_status
                        ]
                        writer.writerow(row)

                show_supplier_advanced_info(self, "تم", f"تم تصدير التقرير الشامل بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في التصدير الشامل: {str(e)}")

    def export_detailed_report(self):
        """تصدير تقرير تفصيلي شامل للموردين"""
        try:

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير التفصيلي", f"تقرير_تفصيلي_الموردين_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                suppliers = self.session.query(Supplier).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التقرير
                    writer.writerow(['التقرير التفصيلي الشامل للموردين'])
                    writer.writerow([f'تاريخ الإنشاء: {format_datetime_for_export()}'])
                    writer.writerow([f'إجمالي الموردين: {len(suppliers)}'])
                    writer.writerow([])

                    # تحليل مفصل
                    balances = [supplier.balance for supplier in suppliers]
                    avg_balance = sum(balances) / len(balances) if balances else 0
                    max_balance = max(balances) if balances else 0
                    min_balance = min(balances) if balances else 0

                    writer.writerow(['التحليل المالي المفصل'])
                    writer.writerow(['متوسط الرصيد', f'{avg_balance:,.2f} جنيه'])
                    writer.writerow(['أعلى رصيد', f'{max_balance:,.2f} جنيه'])
                    writer.writerow(['أقل رصيد', f'{min_balance:,.2f} جنيه'])
                    writer.writerow([])

                    # البيانات الكاملة
                    headers = [
                        'الرقم التعريفي', 'اسم المورد', 'رقم الهاتف', 'العنوان',
                        'البريد الإلكتروني', 'الرصيد الحالي', 'حالة الرصيد', 'تصنيف المورد',
                        'الملاحظات', 'تاريخ التسجيل', 'حالة النشاط', 'تقييم الائتمان'
                    ]
                    writer.writerow(headers)

                    for supplier in suppliers:
                        balance_status = 'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'
                        supplier_type = 'VIP' if supplier.balance > 10000 else 'عادي' if supplier.balance >= 0 else 'مدين'
                        activity_status = 'نشط' if supplier.balance != 0 else 'غير نشط'
                        credit_rating = 'ممتاز' if supplier.balance > 5000 else 'جيد' if supplier.balance >= 0 else 'ضعيف'

                        row = [
                            supplier.id,
                            supplier.name or 'غير محدد',
                            supplier.phone or 'غير محدد',
                            supplier.address or 'غير محدد',
                            supplier.email or 'غير محدد',
                            f'{supplier.balance:,.2f} جنيه',
                            balance_status,
                            supplier_type,
                            supplier.notes or 'لا توجد ملاحظات',
                            supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد',
                            activity_status,
                            credit_rating
                        ]
                        writer.writerow(row)

                show_supplier_advanced_info(self, "تم", f"تم إنشاء التقرير التفصيلي بنجاح:\n{file_path}")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في إنشاء التقرير التفصيلي: {str(e)}")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة المتخصص"""
        try:

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الأرصدة", f"تقرير_الأرصدة_الموردين_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                suppliers = self.session.query(Supplier).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير الأرصدة المتخصص للموردين'])
                    writer.writerow([f'تاريخ الإنشاء: {format_datetime_for_export()}'])
                    writer.writerow([])

                    # تصنيف الأرصدة
                    positive_suppliers = [s for s in suppliers if s.balance > 0]
                    negative_suppliers = [s for s in suppliers if s.balance < 0]
                    zero_suppliers = [s for s in suppliers if s.balance == 0]

                    # ملخص الأرصدة
                    writer.writerow(['ملخص الأرصدة'])
                    writer.writerow(['إجمالي الأرصدة الموجبة', f'{sum(s.balance for s in positive_suppliers):,.2f} جنيه'])
                    writer.writerow(['إجمالي الأرصدة السالبة', f'{sum(s.balance for s in negative_suppliers):,.2f} جنيه'])
                    writer.writerow(['صافي الأرصدة', f'{sum(s.balance for s in suppliers):,.2f} جنيه'])
                    writer.writerow([])

                    # الموردين الدائنين
                    if positive_suppliers:
                        writer.writerow(['الموردين الدائنين (مرتبين تنازلياً)'])
                        writer.writerow(['الترتيب', 'اسم المورد', 'الرصيد', 'الهاتف'])

                        positive_suppliers.sort(key=lambda x: x.balance, reverse=True)
                        for i, supplier in enumerate(positive_suppliers, 1):
                            writer.writerow([i, supplier.name or 'غير محدد', f'{supplier.balance:,.2f} جنيه', supplier.phone or 'غير محدد'])
                        writer.writerow([])

                    # الموردين المدينين
                    if negative_suppliers:
                        writer.writerow(['الموردين المدينين (مرتبين تصاعدياً)'])
                        writer.writerow(['الترتيب', 'اسم المورد', 'الرصيد', 'الهاتف'])

                        negative_suppliers.sort(key=lambda x: x.balance)
                        for i, supplier in enumerate(negative_suppliers, 1):
                            writer.writerow([i, supplier.name or 'غير محدد', f'{supplier.balance:,.2f} جنيه', supplier.phone or 'غير محدد'])
                        writer.writerow([])

                    # الموردين المتوازنين
                    if zero_suppliers:
                        writer.writerow(['الموردين المتوازنين (رصيد صفر)'])
                        writer.writerow(['الرقم', 'اسم المورد', 'الهاتف'])

                        for i, supplier in enumerate(zero_suppliers, 1):
                            writer.writerow([i, supplier.name or 'غير محدد', supplier.phone or 'غير محدد'])

                show_supplier_advanced_info(self, "تم", f"تم إنشاء تقرير الأرصدة بنجاح:\n{file_path}")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في إنشاء تقرير الأرصدة: {str(e)}")

    def export_custom(self):
        """تصدير مخصص مع خيارات متقدمة"""
        try:

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص للموردين - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(400, 380)  # تقليل الارتفاع

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للنافذة الحوارية: {e}")

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي مضغوط
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(15, 10, 15, 10)
            layout.setSpacing(8)

            # العنوان الرئيسي مضغوط
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(3)

            # الأيقونة والعنوان الرئيسي مضغوط
            main_title = QLabel("🔧 تصدير مخصص للموردين")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 22px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 5px;
                }
            """)

            # العنوان الفرعي التوضيحي مضغوط
            subtitle = QLabel("اختر البيانات المراد تصديرها بدقة")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # مجموعة البيانات الأساسية مطابقة لنافذة الإحصائيات
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)
            basic_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            basic_title = QLabel("🏪 البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)

            self.export_id = QCheckBox("🆔 الرقم التعريفي")
            self.export_name = QCheckBox("🏪 اسم المورد")
            self.export_phone = QCheckBox("📞 رقم الهاتف")
            self.export_address = QCheckBox("🏠 العنوان")
            self.export_email = QCheckBox("📧 البريد الإلكتروني")

            # تحديد افتراضي
            self.export_name.setChecked(True)
            self.export_phone.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات مع علامة صح خارجية
            checkboxes_data = [
                (self.export_id, "#3B82F6"),
                (self.export_name, "#10B981"),
                (self.export_phone, "#F59E0B"),
                (self.export_address, "#EF4444"),
                (self.export_email, "#8B5CF6")
            ]

            for checkbox, color in checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)
                item_layout.setContentsMargins(8, 3, 8, 3)

                # تصميم الـ CheckBox بدون علامة صح داخلية
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                basic_layout.addWidget(item_widget)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)



            # مجموعة البيانات المالية مطابقة لنافذة الإحصائيات
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)
            financial_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            financial_title = QLabel("💰 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)

            self.export_balance = QCheckBox("💵 الرصيد الحالي")
            self.export_balance_status = QCheckBox("📊 حالة الرصيد")
            self.export_supplier_type = QCheckBox("🏆 تصنيف المورد")

            self.export_balance.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات المالية
            financial_checkboxes_data = [
                (self.export_balance, "#10B981"),
                (self.export_balance_status, "#F59E0B"),
                (self.export_supplier_type, "#EF4444")
            ]

            for checkbox, color in financial_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)
                item_layout.setContentsMargins(8, 3, 8, 3)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة للبيانات المالية
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                financial_layout.addWidget(item_widget)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)





            # مجموعة البيانات الإضافية مطابقة لنافذة الإحصائيات
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)
            additional_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            additional_title = QLabel("📝 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)

            self.export_notes = QCheckBox("📋 الملاحظات")
            self.export_created_date = QCheckBox("📅 تاريخ الإنشاء")
            self.export_statistics = QCheckBox("📊 إضافة الإحصائيات")

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات الإضافية
            additional_checkboxes_data = [
                (self.export_notes, "#8B5CF6"),
                (self.export_created_date, "#F59E0B"),
                (self.export_statistics, "#3B82F6")
            ]

            for checkbox, color in additional_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)
                item_layout.setContentsMargins(8, 3, 8, 3)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة للبيانات الإضافية
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                additional_layout.addWidget(item_widget)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

            # أزرار التحكم مضغوطة
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(10)

            # زر الإلغاء مطابق للبرنامج الرئيسي
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            cancel_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export_suppliers(dialog))
            export_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def perform_custom_export_suppliers(self, dialog):
        """تنفيذ التصدير المخصص للموردين"""
        try:

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_موردين_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                suppliers = self.session.query(Supplier).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إضافة الإحصائيات إذا تم اختيارها
                    if self.export_statistics.isChecked():
                        writer.writerow(['تصدير مخصص للموردين'])
                        writer.writerow([f'تاريخ التصدير: {format_datetime_for_export()}'])
                        writer.writerow([f'إجمالي الموردين: {len(suppliers)}'])
                        writer.writerow([])

                    # إنشاء رؤوس الأعمدة حسب الاختيار
                    headers = []
                    if self.export_id.isChecked():
                        headers.append('الرقم التعريفي')
                    if self.export_name.isChecked():
                        headers.append('اسم المورد')
                    if self.export_phone.isChecked():
                        headers.append('رقم الهاتف')
                    if self.export_address.isChecked():
                        headers.append('العنوان')
                    if self.export_email.isChecked():
                        headers.append('البريد الإلكتروني')
                    if self.export_balance.isChecked():
                        headers.append('الرصيد')
                    if self.export_balance_status.isChecked():
                        headers.append('حالة الرصيد')
                    if self.export_supplier_type.isChecked():
                        headers.append('تصنيف المورد')
                    if self.export_notes.isChecked():
                        headers.append('الملاحظات')
                    if self.export_created_date.isChecked():
                        headers.append('تاريخ الإنشاء')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for supplier in suppliers:
                        row = []
                        if self.export_id.isChecked():
                            row.append(supplier.id)
                        if self.export_name.isChecked():
                            row.append(supplier.name or 'غير محدد')
                        if self.export_phone.isChecked():
                            row.append(supplier.phone or 'غير محدد')
                        if self.export_address.isChecked():
                            row.append(supplier.address or 'غير محدد')
                        if self.export_email.isChecked():
                            row.append(supplier.email or 'غير محدد')
                        if self.export_balance.isChecked():
                            row.append(f'{supplier.balance:,.2f} جنيه')
                        if self.export_balance_status.isChecked():
                            balance_status = 'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'
                            row.append(balance_status)
                        if self.export_supplier_type.isChecked():
                            supplier_type = 'VIP' if supplier.balance > 10000 else 'عادي' if supplier.balance >= 0 else 'مدين'
                            row.append(supplier_type)
                        if self.export_notes.isChecked():
                            row.append(supplier.notes or 'لا توجد ملاحظات')
                        if self.export_created_date.isChecked():
                            row.append(supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد')

                        writer.writerow(row)

                dialog.accept()
                show_supplier_advanced_info(self, "تم", f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة من بيانات الموردين"""
        try:

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_موردين_{format_datetime_for_filename()}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                suppliers = self.session.query(Supplier).all()

                backup_data = {
                    'metadata': {
                        'export_date': datetime.now().isoformat(),
                        'total_suppliers': len(suppliers),
                        'version': '1.0',
                        'type': 'suppliers_backup'
                    },
                    'suppliers': []
                }

                for supplier in suppliers:
                    supplier_data = {
                        'id': supplier.id,
                        'name': supplier.name,
                        'phone': supplier.phone,
                        'address': supplier.address,
                        'email': supplier.email,
                        'balance': float(supplier.balance),
                        'notes': supplier.notes,
                        'created_at': supplier.created_at.isoformat() if supplier.created_at else None
                    }
                    backup_data['suppliers'].append(supplier_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                show_supplier_advanced_info(self, "تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتحتوي على {len(suppliers)} مورد")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة بيانات الموردين من نسخة احتياطية"""
        try:

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختيار النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة
                reply = show_supplier_advanced_confirmation(
                    self, "تأكيد الاستعادة",
                    "هل أنت متأكد من استعادة البيانات؟\n\nتحذير: سيتم استبدال البيانات الحالية!"
                )

                if reply:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    # التحقق من صحة النسخة الاحتياطية
                    if 'metadata' not in backup_data or backup_data['metadata'].get('type') != 'suppliers_backup':
                        show_supplier_advanced_error(self, "خطأ", "ملف النسخة الاحتياطية غير صحيح أو تالف")
                        return

                    suppliers_data = backup_data.get('suppliers', [])

                    # عرض معلومات النسخة الاحتياطية
                    metadata = backup_data['metadata']
                    info_msg = f"""معلومات النسخة الاحتياطية:
تاريخ الإنشاء: {metadata.get('export_date', 'غير محدد')}
عدد الموردين: {metadata.get('total_suppliers', len(suppliers_data))}
الإصدار: {metadata.get('version', 'غير محدد')}

هل تريد المتابعة؟"""

                    final_reply = show_supplier_advanced_confirmation(
                        self, "تأكيد نهائي", info_msg
                    )

                    if final_reply:
                        # حذف البيانات الحالية (اختياري)
                        clear_reply = show_supplier_advanced_confirmation(
                            self, "حذف البيانات الحالية",
                            "هل تريد حذف جميع الموردين الحاليين قبل الاستعادة؟"
                        )

                        if clear_reply:
                            # حذف جميع الموردين الحاليين
                            self.session.query(Supplier).delete()

                        # استعادة البيانات
                        restored_count = 0
                        for supplier_data in suppliers_data:
                            try:
                                # التحقق من وجود المورد
                                existing_supplier = self.session.query(Supplier).filter_by(
                                    name=supplier_data.get('name')
                                ).first()

                                if existing_supplier and not clear_reply:
                                    # تحديث البيانات الموجودة
                                    existing_supplier.phone = supplier_data.get('phone')
                                    existing_supplier.address = supplier_data.get('address')
                                    existing_supplier.email = supplier_data.get('email')
                                    existing_supplier.balance = supplier_data.get('balance', 0)
                                    existing_supplier.notes = supplier_data.get('notes')
                                else:
                                    # إنشاء مورد جديد
                                    new_supplier = Supplier(
                                        name=supplier_data.get('name'),
                                        phone=supplier_data.get('phone'),
                                        address=supplier_data.get('address'),
                                        email=supplier_data.get('email'),
                                        balance=supplier_data.get('balance', 0),
                                        notes=supplier_data.get('notes')
                                    )
                                    self.session.add(new_supplier)

                                restored_count += 1

                            except Exception as e:
                                print(f"خطأ في استعادة المورد: {e}")
                                continue

                        # حفظ التغييرات
                        self.session.commit()

                        # تحديث الجدول
                        self.load_suppliers()

                        show_supplier_advanced_info(self, "تم", f"تم استعادة {restored_count} مورد بنجاح من النسخة الاحتياطية")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()

    def show_statistics(self):
        """عرض نافذة إحصائيات الموردين"""
        try:
            dialog = SupplierStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض إحصائيات الموردين: {e}")
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def manage_documents(self):
        """إدارة وثائق المورد المحدد"""
        try:
            print("📁 بدء إدارة وثائق المورد")

            # التحقق من وجود مورد محدد
            supplier_id, error = self.get_selected_supplier_id()
            if error:
                show_supplier_advanced_warning(self, "تحذير", error)
                return

            # الحصول على بيانات المورد من قاعدة البيانات
            supplier = self.session.query(Supplier).get(supplier_id)
            if supplier:
                print(f"✅ تم العثور على المورد: {supplier.name}")
                # فتح نافذة إدارة الوثائق
                dialog = SupplierDocumentsDialog(self, supplier, self.session)
                dialog.exec_()
            else:
                show_supplier_advanced_error(self, "خطأ", "لم يتم العثور على المورد المحدد")

        except Exception as e:
            print(f"❌ خطأ في إدارة وثائق المورد: {e}")
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في إدارة الوثائق: {str(e)}")

    def show_whatsapp_options(self):
        """عرض خيارات واتساب للمورد المحدد"""
        try:
            # التحقق من وجود مورد محدد
            selected_items = self.suppliers_table.selectedItems()
            if not selected_items:
                show_supplier_advanced_info(self, "تنبيه", "يرجى تحديد مورد أولاً للتواصل معه عبر الواتساب")
                return

            # الحصول على بيانات المورد المحدد
            row = selected_items[0].row()
            # استخراج الـ ID الفعلي من البيانات المخفية
            id_item = self.suppliers_table.item(row, 0)
            supplier_id = id_item.data(Qt.UserRole)
            if not supplier_id:
                show_supplier_advanced_error(self, "خطأ", "لا يمكن الحصول على معرف المورد")
                return
            supplier = self.session.query(Supplier).filter(Supplier.id == supplier_id).first()

            if not supplier:
                show_supplier_advanced_error(self, "خطأ", "لم يتم العثور على بيانات المورد")
                return

            # عرض نافذة خيارات الواتساب
            dialog = SupplierWhatsAppDialog(supplier, self)
            dialog.exec_()

        except Exception as e:
            print(f"خطأ في عرض خيارات الواتساب: {str(e)}")
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في عرض خيارات الواتساب: {str(e)}")

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة أزرار الموردين...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة مورد"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.add_payment_button, "💰 تعديل المبلغ"),
                (self.documents_button, "📁 إدارة الوثائق"),
                (self.whatsapp_button, "📞 إتصال واتساب"),
                (self.export_button, "📤 تصدير"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
                    print(f"🟢 تم تفعيل الزر: {name}")

            print("✅ تم تهيئة حالة أزرار الموردين بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تهيئة حالة أزرار الموردين: {str(e)}")


class AddSupplierDialog(QDialog):
    """نافذة إضافة أو تعديل مورد مع نظام تعدد الأرقام - مطابقة تماماً للعملاء"""

    def __init__(self, session, parent=None, supplier=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.supplier = supplier  # المورد للتعديل (None للإضافة)
        self.is_edit_mode = supplier is not None  # تحديد وضع التعديل

        # سيتم تحديد العنوان في setup_ui
        self.setModal(True)
        self.resize(650, 650)  # جعل النافذة مربعة
        self.setup_ui()

        # تحميل البيانات في وضع التعديل
        if self.is_edit_mode:
            self.load_supplier_data()

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار - نسخة مبسطة لنافذة الحوار"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def setup_ui(self):
        """إعداد واجهة النافذة - مطابقة تماماً للعملاء"""
        # استخدام شريط العنوان الطبيعي للنظام مع النص في المنتصف
        if self.is_edit_mode:
            self.setWindowTitle("✏️ تعديل - نظام إدارة الموردين المتطور والشامل")
        else:
            self.setWindowTitle("🚛 إضافة - نظام إدارة الموردين المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط المحتوى الرئيسي للنافذة - مطابق للعملاء
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(18)

        # إضافة عنوان النافذة الداخلي - يتغير حسب الوضع
        if self.is_edit_mode:
            title_text = "تعديل بيانات المورد"
            title_icon = "✏️"
        else:
            title_text = "إضافة مورد جديد"
            title_icon = "🏭"

        title_label = QLabel(f"{title_icon} {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 18px 25px;
                margin: 8px 5px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                           0 3px 12px rgba(37, 99, 235, 0.3);
                min-height: 50px;
                max-height: 50px;
            }
        """)
        layout.addWidget(title_label)

        # نموذج البيانات مع تصميم محسن - مطابق للعملاء
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)
        form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        form_layout.setHorizontalSpacing(15)
        form_layout.setVerticalSpacing(12)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل - مطابقة للعملاء
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # اسم المورد (مطلوب) - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المورد الكامل هنا... (مطلوب)")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("اسم المورد", "🚛", True), self.name_edit)

        # العنوان - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText("أدخل العنوان الكامل للمورد (الشارع، المدينة، المحافظة)...")
        self.address_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("العنوان", "🏠"), self.address_edit)

        # البريد الإلكتروني - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني الكامل (<EMAIL>)...")
        self.email_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("البريد الإلكتروني", "📧"), self.email_edit)

        # أرقام الهاتف (نظام تعدد الأرقام) - مطابق للعملاء
        phone_container = QVBoxLayout()

        # الهاتف الأساسي - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف الأساسي (مثال: 01234567890)...")
        self.phone_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        phone_container.addWidget(self.phone_edit)

        # أرقام إضافية
        self.additional_phones = []
        self.phone_widgets = []

        # زر إضافة رقم هاتف مع ارتفاع مقلل ومنزل للأسفل - مطابق للعملاء
        add_phone_btn = QPushButton("➕ إضافة رقم")
        self.style_advanced_button(add_phone_btn, 'info')
        add_phone_btn.clicked.connect(self.add_phone_field)

        # تقليل ارتفاع الزر وإنزاله للأسفل - مطابق للعملاء
        add_phone_btn.setStyleSheet(add_phone_btn.styleSheet() + """
            QPushButton {
                margin-top: 15px;
                margin-bottom: 8px;
                margin-left: 5px;
                margin-right: 5px;
                max-height: 35px;
                min-height: 35px;
                padding: 6px 12px;
                font-size: 14px;
            }
        """)

        phone_container.addWidget(add_phone_btn)

        form_layout.addRow(create_styled_label("أرقام الهاتف", "📱"), phone_container)

        # الرصيد الابتدائي - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.balance_spinbox = QDoubleSpinBox()
        self.balance_spinbox.setRange(-999999999, 999999999)
        self.balance_spinbox.setDecimals(0)  # إزالة الأرقام العشرية
        self.balance_spinbox.setValue(0)
        self.balance_spinbox.setSuffix(" جنيه")
        self.balance_spinbox.setButtonSymbols(QDoubleSpinBox.NoButtons)  # إزالة الأسهم
        self.balance_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 18px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الرصيد الابتدائي", "💰"), self.balance_spinbox)

        # الملاحظات - مع استغلال أفضل للمساحة - مطابق للعملاء
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات أو تفاصيل إضافية عن المورد هنا...")
        self.notes_edit.setMaximumHeight(100)  # ارتفاع أكبر قليلاً
        self.notes_edit.setMinimumWidth(350)   # عرض أكبر
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 15px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        layout.addLayout(form_layout)

        # أزرار التحكم - مطابقة للعملاء
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء - يتغير النص حسب الوضع
        if self.is_edit_mode:
            save_button = QPushButton("✏️ تحديث")
        else:
            save_button = QPushButton("💾 حفظ")
        self.parent_widget.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.save_supplier)

        # زر الإلغاء - أحمر للخطر - مطابق للعملاء
        cancel_button = QPushButton("❌ إلغاء")
        self.parent_widget.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)
        layout.addLayout(buttons_layout)

    def add_phone_field(self):
        """إضافة حقل هاتف جديد مع زر حذف"""
        try:
            # إنشاء حاوي أفقي للحقل والزر
            phone_container = QHBoxLayout()
            phone_container.setSpacing(8)

            # إنشاء حقل الهاتف الجديد
            phone_edit = QLineEdit()
            phone_edit.setPlaceholderText("رقم هاتف إضافي...")
            phone_edit.setStyleSheet(self.phone_edit.styleSheet())

            # إنشاء زر الحذف
            remove_btn = QPushButton("🗑️")
            self.parent_widget.style_advanced_button(remove_btn, 'danger')
            remove_btn.setMaximumWidth(40)
            remove_btn.setMaximumHeight(35)
            remove_btn.clicked.connect(lambda: self.remove_phone_field(phone_container, phone_edit))

            # إضافة العناصر للحاوي مع ترتيب معكوس - الحقل أولاً ثم الزر
            phone_container.addWidget(phone_edit)  # الحقل أولاً في اليمين
            phone_container.addWidget(remove_btn)  # الزر ثانياً في اليسار
            phone_container.addStretch()  # إضافة مساحة مرنة في النهاية

            # إضافة إلى القائمة
            self.additional_phones.append(phone_edit)
            self.phone_widgets.append(phone_container)

            # إضافة إلى الواجهة بطريقة محسنة
            self.add_phone_to_form(phone_container)

        except Exception as e:
            show_supplier_advanced_warning(self, "خطأ", f"حدث خطأ في إضافة حقل الهاتف: {str(e)}")

    def add_phone_to_form(self, phone_container):
        """إضافة حقل الهاتف إلى النموذج بطريقة محسنة"""
        try:
            # إنشاء widget للحاوي
            phone_widget = QWidget()
            phone_widget.setLayout(phone_container)

            # الحصول على النموذج الرئيسي - البحث عن form_layout
            main_layout = self.layout()
            form_layout = None

            # البحث عن QFormLayout في التخطيط الرئيسي
            for i in range(main_layout.count()):
                item = main_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    if hasattr(widget, 'layout') and widget.layout():
                        layout = widget.layout()
                        if isinstance(layout, QFormLayout):
                            form_layout = layout
                            break

            if form_layout:
                # إضافة الحقل الجديد بعد آخر حقل هاتف
                row_count = form_layout.rowCount()
                form_layout.insertRow(row_count - 2, "", phone_widget)  # إدراج قبل الملاحظات والرصيد
            else:
                print("لم يتم العثور على form_layout")

        except Exception as e:
            print(f"خطأ في إضافة حقل الهاتف للنموذج: {str(e)}")

    def remove_phone_field(self, phone_container, phone_edit):
        """حذف حقل هاتف"""
        try:
            # إزالة من القوائم
            if phone_edit in self.additional_phones:
                self.additional_phones.remove(phone_edit)
            if phone_container in self.phone_widgets:
                self.phone_widgets.remove(phone_container)

            # إزالة من الواجهة
            widget = phone_container.parent()
            if widget:
                widget.setParent(None)
                widget.deleteLater()

        except Exception as e:
            print(f"خطأ في حذف حقل الهاتف: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الطبيعي مع التدرجات والألوان الجديدة المتطورة"""
        try:
            # إنشاء أيقونة مخصصة متطورة مع تدرجات جديدة
            pixmap = QPixmap(48, 48)  # حجم أكبر للوضوح
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)

            # إنشاء تدرج متطور للأيقونة - مطابق للعملاء
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق مطابق للعملاء
            gradient.setColorAt(0.7, QColor(37, 99, 235))  # أزرق داكن
            gradient.setColorAt(1, QColor(29, 78, 216))  # أزرق عميق

            # رسم دائرة متدرجة
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز المورد - يتغير حسب الوضع
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))

            # اختيار الرمز حسب الوضع
            icon_text = "✏️" if self.is_edit_mode else "🚛"
            painter.drawText(12, 30, icon_text)

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

            # توسيط النص في شريط العنوان
            self.center_title_text()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            # تحديد العنوان حسب الوضع
            if self.is_edit_mode:
                original_title = "✏️ تعديل - نظام إدارة الموردين المتطور والشامل"
            else:
                original_title = "🚛 إضافة - نظام إدارة الموردين المتطور والشامل"

            # حساب المسافات المطلوبة للتوسيط
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"

            # تحديث العنوان مع التوسيط
            self.setWindowTitle(centered_title)

        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")

    def save_supplier(self):
        """حفظ المورد الجديد أو تحديث المورد الموجود مع نظام تعدد الأرقام"""
        try:
            # التحقق من صحة البيانات
            name = self.name_edit.text().strip()
            if not name:
                show_supplier_advanced_warning(self, "تحذير", "يرجى إدخال اسم المورد")
                self.name_edit.setFocus()
                return

            # جمع أرقام الهاتف
            phone_numbers = []

            # الرقم الأساسي
            main_phone = self.phone_edit.text().strip()
            if main_phone:
                phone_numbers.append(main_phone)

            # الأرقام الإضافية
            for phone_edit in self.additional_phones:
                additional_phone = phone_edit.text().strip()
                if additional_phone:
                    phone_numbers.append(additional_phone)

            # تحويل قائمة الأرقام إلى نص مفصول بفواصل
            phone_string = ", ".join(phone_numbers) if phone_numbers else None

            if self.is_edit_mode:
                # تحديث المورد الموجود
                old_balance = self.supplier.balance or 0
                new_balance = self.balance_spinbox.value()

                self.supplier.name = name
                self.supplier.address = self.address_edit.text().strip() or None
                self.supplier.email = self.email_edit.text().strip() or None
                self.supplier.phone = phone_string
                self.supplier.balance = new_balance
                self.supplier.notes = self.notes_edit.toPlainText().strip() or None

                # حفظ التغييرات
                self.session.commit()

                # التحقق من إنشاء قسط تلقائي إذا أصبح الرصيد سالباً
                if old_balance >= 0 and new_balance < 0:
                    try:
                        negative_amount = abs(new_balance)
                        auto_create_installment_for_negative_balance(
                            self.session, 'supplier', self.supplier.id, name, negative_amount
                        )
                        print(f"🔄 تم إنشاء قسط تلقائي للمورد {name} بسبب الرصيد السالب: {new_balance:,.2f} ج.م")
                    except Exception as e:
                        print(f"⚠️ خطأ في إنشاء القسط التلقائي للمورد {name}: {str(e)}")

                # رسالة نجاح التحديث
                phone_info = f"\nأرقام الهاتف: {phone_string}" if phone_string else "\nلم يتم إدخال أرقام هاتف"
                show_supplier_advanced_info(
                    self,
                    "تم التحديث",
                    f"تم تحديث بيانات المورد '{name}' بنجاح!{phone_info}"
                )
            else:
                # إنشاء مورد جديد
                balance = self.balance_spinbox.value()
                new_supplier = Supplier(
                    name=name,
                    address=self.address_edit.text().strip() or None,
                    email=self.email_edit.text().strip() or None,
                    phone=phone_string,  # حفظ جميع الأرقام
                    balance=balance,
                    notes=self.notes_edit.toPlainText().strip() or None
                )

                # حفظ في قاعدة البيانات
                self.session.add(new_supplier)
                self.session.commit()

                # التحقق من إنشاء قسط تلقائي إذا كان الرصيد سالباً
                if balance < 0:
                    try:
                        negative_amount = abs(balance)
                        auto_create_installment_for_negative_balance(
                            self.session, 'supplier', new_supplier.id, name, negative_amount
                        )
                        print(f"🔄 تم إنشاء قسط تلقائي للمورد الجديد {name} بسبب الرصيد السالب: {balance:,.2f} ج.م")
                    except Exception as e:
                        print(f"⚠️ خطأ في إنشاء القسط التلقائي للمورد الجديد {name}: {str(e)}")

                # رسالة نجاح الإضافة
                phone_info = f"\nأرقام الهاتف: {phone_string}" if phone_string else "\nلم يتم إدخال أرقام هاتف"
                show_supplier_advanced_info(
                    self,
                    "نجح",
                    f"تم إضافة المورد '{name}' بنجاح!{phone_info}"
                )

            self.accept()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في حفظ المورد: {str(e)}")

    def load_supplier_data(self):
        """تحميل بيانات المورد في وضع التعديل"""
        if not self.supplier:
            return

        try:
            # تحميل البيانات الأساسية
            self.name_edit.setText(self.supplier.name or "")
            self.address_edit.setText(self.supplier.address or "")
            self.email_edit.setText(self.supplier.email or "")
            self.balance_spinbox.setValue(self.supplier.balance or 0.0)
            self.notes_edit.setPlainText(self.supplier.notes or "")

            # تحميل أرقام الهاتف
            if self.supplier.phone:
                # تقسيم أرقام الهاتف المفصولة بفواصل
                phone_numbers = [phone.strip() for phone in self.supplier.phone.split(",") if phone.strip()]

                if phone_numbers:
                    # الرقم الأول في الحقل الأساسي
                    self.phone_edit.setText(phone_numbers[0])

                    # الأرقام الإضافية
                    for i, phone in enumerate(phone_numbers[1:], 1):
                        if i <= len(self.additional_phones):
                            # استخدام الحقول الموجودة
                            self.additional_phones[i-1].setText(phone)
                        else:
                            # إضافة حقول جديدة للأرقام الإضافية
                            self.add_phone_field()
                            if self.additional_phones:
                                self.additional_phones[-1].setText(phone)

        except Exception as e:
            print(f"خطأ في تحميل بيانات المورد: {e}")


class DeleteSupplierDialog(QDialog):
    """نافذة حذف المورد مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, supplier=None):
        super().__init__(parent)
        self.supplier = supplier
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("🏢 حذف - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("🏢 حذف المورد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات المورد مضغوطة
        if self.supplier:
            info_text = f"🏢 {self.supplier.name[:15]}{'...' if len(self.supplier.name) > 15 else ''}"
            if self.supplier.balance:
                info_text += f" | 💰 {self.supplier.balance:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("🏢 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🏢")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان موحد مع العمال"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class SupplierInfoDialog(QDialog):
    """
    نافذة عرض معلومات المورد التفصيلية - مطابقة للنموذج المرجعي

    هذه النافذة مطابقة للنموذج المرجعي في قسم العملاء
    المميزات:
    - تصميم موحد ومتسق مع حواف مربعة
    - ألوان واضحة ومتباينة للبيانات
    - تخطيط منظم ومرن
    - أزرار وظيفية متطورة
    - أيقونات محسنة ومتطورة
    """

    def __init__(self, parent=None, supplier=None):
        super().__init__(parent)
        self.supplier = supplier
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية
        # ═══════════════════════════════════════════════════════════════
        self.setWindowTitle("🏢📋 معلومات المورد - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # ═══════════════════════════════════════════════════════════════
        # التخطيط الرئيسي المرجعي
        # ═══════════════════════════════════════════════════════════════
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        self.setGraphicsEffect(self.create_shadow_effect())

        # عنوان النافذة الداخلي المحسن
        title_container = QFrame()
        title_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(8, 1, 8, 5)
        title_layout.setSpacing(5)

        # العنوان المضغوط مع اسم المورد - في المنتصف مع خط أكبر
        title_text = f"🏢 معلومات المورد: {self.supplier.name if self.supplier and self.supplier.name else 'غير محدد'}"
        main_title = QLabel(title_text)
        main_title.setAlignment(Qt.AlignCenter | Qt.AlignVCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: 900;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 3px 15px 10px 15px;
                background: transparent;
                border: none;
                line-height: 1.2;
            }
        """)
        title_layout.addWidget(main_title, 1)

        layout.addWidget(title_container)

        # إنشاء منطقة التمرير المحسنة للمعلومات - مطابقة تماماً للنموذج المرجعي
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(71, 85, 105, 0.4),
                    stop:1 rgba(100, 116, 139, 0.3));
                width: 16px;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.5),
                    stop:1 rgba(34, 197, 94, 0.4));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 7px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(34, 197, 94, 0.6));
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:0.5 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(21, 128, 61, 0.7));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق تماماً للنموذج المرجعي
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)  # زيادة المسافة بين الأقسام لاستغلال المساحة الإضافية

        # إضافة معلومات المورد
        self.add_supplier_info(info_layout)

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def add_supplier_info(self, layout):
        """إضافة معلومات المورد إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.supplier:
            return

        # قسم المعلومات الأساسية والشخصية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.supplier.id).zfill(8)}"),
            ("📧 البريد الإلكتروني", self.supplier.email or "غير محدد"),
            ("📍 العنوان الكامل", self.supplier.address or "غير محدد"),
            ("🏷️ حالة المورد", "نشط ✅" if self.supplier.balance is not None else "غير نشط ❌"),
            ("📊 مستوى البيانات", self.get_data_completeness())
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية والشخصية", basic_info)

        # قسم المعلومات المالية المحسن
        balance_color = self.get_balance_color()
        balance_text = f"{self.supplier.balance:.0f} جنيه" if self.supplier.balance else "0 جنيه"
        self.add_info_section(layout, "💰 المعلومات المالية", [
            ("💵 الرصيد الحالي", f"{balance_color} {balance_text}"),
            ("📊 حالة الحساب", self.get_account_status()),
            ("💳 تصنيف المورد", self.get_supplier_type()),
            ("📈 مستوى النشاط", self.get_activity_level()),
            ("⚖️ تقييم الائتمان", self.get_credit_rating())
        ])

        # قسم معلومات الاتصال المحسن
        self.add_info_section(layout, "📞 معلومات الاتصال", [
            ("📱 الهاتف الرئيسي", self.supplier.phone or "غير محدد"),
            ("📞 أرقام إضافية", self.get_additional_phones()),
            ("📧 حالة البريد", self.get_email_status()),
            ("📲 طرق التواصل", self.get_contact_methods())
        ])

        # قسم معلومات التاريخ والإحصائيات
        self.add_info_section(layout, "📅 معلومات التاريخ والإحصائيات", [
            ("📅 تاريخ الإضافة", self.get_creation_date()),
            ("⏰ آخر تحديث", self.get_last_update()),
            ("📈 مدة التعامل", self.get_membership_duration())
        ])

        # قسم الملاحظات والتفاصيل الإضافية
        self.add_info_section(layout, "📝 ملاحظات وتفاصيل إضافية", [
            ("📝 الملاحظات", self.supplier.notes or "لا توجد ملاحظات"),
            ("🔍 معلومات إضافية", self.get_additional_info()),
            ("📋 ملخص الحساب", self.get_account_summary())
        ])

    def add_info_section(self, layout, title, items):
        """إضافة قسم معلومات بدون إطار رئيسي"""
        # حاوي القسم بدون إطار
        section_frame = QWidget()
        section_frame.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
                margin: 5px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المبسط
        section_title = QLabel(title)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(139, 92, 246, 0.15),
                    stop:1 rgba(34, 197, 94, 0.1));
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                margin-bottom: 8px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(section_title)

        # عناصر القسم المحسنة
        for i, (label, value) in enumerate(items):
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(12, 8, 12, 8)
            item_layout.setSpacing(15)

            # خلفية مبسطة للعناصر
            item_widget.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.02 + (i % 2) * 0.01}),
                        stop:0.5 rgba(248, 250, 252, {0.03 + (i % 2) * 0.01}),
                        stop:1 rgba(241, 245, 249, {0.02 + (i % 2) * 0.01}));
                    border: none;
                    border-radius: 4px;
                    margin: 1px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.05),
                        stop:0.5 rgba(139, 92, 246, 0.04),
                        stop:1 rgba(34, 197, 94, 0.03));
                    border: none;
                }}
            """)

            # التسمية المحسنة - نصوص أوضح
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: none;
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة مع ألوان مخصصة
            value_widget = QLabel(str(value))

            # تحديد لون القيمة حسب المحتوى
            value_color = self.get_value_color(label, str(value))

            value_widget.setStyleSheet(f"""
                QLabel {{
                    color: {value_color};
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }}
                QLabel:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.5 rgba(139, 92, 246, 0.12),
                        stop:1 rgba(34, 197, 94, 0.10));
                    border: none;
                }}
            """)
            value_widget.setWordWrap(True)

            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget, 1)

            section_layout.addWidget(item_widget)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - ألوان أوضح وأكثر تباينًا"""
        # ألوان أساسية أوضح وأكثر تباينًا
        colors = {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'success': '#32CD32',       # أخضر ليموني للنجاح
            'error': '#FF4500',         # برتقالي أحمر للأخطاء
            'special': '#DA70D6',       # بنفسجي نيون للمميز
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

        # تحديد اللون حسب التسمية والقيمة
        if "رصيد" in label.lower() or "مالي" in label.lower():
            if "🟢" in value or "دائن" in value:
                return colors['positive']
            elif "🔴" in value or "مدين" in value:
                return colors['negative']
            else:
                return colors['neutral']
        elif "حالة" in label.lower():
            if "نشط" in value or "✅" in value:
                return colors['success']
            elif "غير نشط" in value or "❌" in value:
                return colors['error']
            else:
                return colors['warning']
        elif "بريد" in label.lower():
            if "صحيح" in value or "✅" in value:
                return colors['success']
            elif "خطأ" in value or "❌" in value:
                return colors['error']
            else:
                return colors['info']
        elif "تصنيف" in label.lower() or "مستوى" in label.lower():
            if "مميز" in value or "عالي" in value or "⭐" in value:
                return colors['special']
            elif "متوسط" in value:
                return colors['warning']
            else:
                return colors['neutral']
        elif "غير محدد" in value or "لا توجد" in value:
            return colors['neutral']
        else:
            return colors['default']

    def get_balance_color(self):
        """تحديد لون الرصيد"""
        if not self.supplier.balance or self.supplier.balance == 0:
            return "🟡"  # أصفر للصفر
        elif self.supplier.balance > 0:
            return "🟢"  # أخضر للموجب
        else:
            return "🔴"  # أحمر للسالب

    # دوال مساعدة مبسطة لعرض المعلومات - مطابقة للنموذج المرجعي
    def get_account_status(self):
        """حالة الحساب المبسطة"""
        if not self.supplier.balance or self.supplier.balance == 0:
            return "متوازن ⚖️"
        elif self.supplier.balance > 0:
            return "دائن 💚"
        else:
            return "مدين 🔴"

    def get_supplier_type(self):
        """تصنيف المورد"""
        balance = abs(self.supplier.balance) if self.supplier.balance else 0
        if balance > 50000:
            return "مورد رئيسي 🌟"
        elif balance > 10000:
            return "مورد مميز ⭐"
        else:
            return "مورد عادي 👤"

    def get_activity_level(self):
        """مستوى النشاط المبسط"""
        balance = abs(self.supplier.balance) if self.supplier.balance else 0
        if balance > 20000:
            return "نشاط عالي 🔥"
        elif balance > 5000:
            return "نشاط متوسط 📊"
        else:
            return "نشاط منخفض 📉"

    def get_credit_rating(self):
        """تقييم الائتمان"""
        balance = self.supplier.balance if self.supplier.balance else 0
        if balance > 0:
            return "ممتاز 🏆"
        elif balance == 0:
            return "جيد ✅"
        else:
            return "يحتاج متابعة ⚠️"

    def get_data_completeness(self):
        """مستوى اكتمال البيانات"""
        fields = [self.supplier.name, self.supplier.phone, self.supplier.email, self.supplier.address]
        filled = sum(1 for field in fields if field and field.strip())
        percentage = (filled / len(fields)) * 100
        if percentage == 100:
            return f"مكتمل {percentage:.0f}% ✅"
        elif percentage >= 75:
            return f"جيد {percentage:.0f}% 🟢"
        elif percentage >= 50:
            return f"متوسط {percentage:.0f}% 🟡"
        else:
            return f"ناقص {percentage:.0f}% 🔴"

    def get_additional_phones(self):
        """الأرقام الإضافية"""
        return "لا توجد أرقام إضافية"

    def get_email_status(self):
        """حالة البريد الإلكتروني"""
        if self.supplier.email:
            if "@" in self.supplier.email and "." in self.supplier.email:
                return "صحيح ✅"
            else:
                return "تنسيق خطأ ❌"
        else:
            return "غير محدد ⚪"

    def get_contact_methods(self):
        """طرق التواصل المتاحة"""
        methods = []
        if self.supplier.phone:
            methods.append("هاتف 📱")
        if self.supplier.email:
            methods.append("بريد 📧")
        if self.supplier.address:
            methods.append("عنوان 📍")
        return " | ".join(methods) if methods else "غير متاح"

    def get_creation_date(self):
        """تاريخ الإضافة"""
        if hasattr(self.supplier, 'date_added') and self.supplier.date_added:
            return self.supplier.date_added.strftime('%Y-%m-%d %H:%M')
        elif hasattr(self.supplier, 'created_at') and self.supplier.created_at:
            return self.supplier.created_at.strftime('%Y-%m-%d %H:%M')
        else:
            return "غير محدد"

    def get_last_update(self):
        """آخر تحديث"""
        return "غير متاح حالياً"

    def get_membership_duration(self):
        """مدة التعامل"""
        creation_date = None
        if hasattr(self.supplier, 'date_added') and self.supplier.date_added:
            creation_date = self.supplier.date_added
        elif hasattr(self.supplier, 'created_at') and self.supplier.created_at:
            creation_date = self.supplier.created_at

        if creation_date:
            days = (datetime.now() - creation_date).days
            if days < 30:
                return f"{days} يوم"
            elif days < 365:
                months = days // 30
                return f"{months} شهر"
            else:
                years = days // 365
                return f"{years} سنة"
        return "غير محدد"



    def get_additional_info(self):
        """معلومات إضافية"""
        info_parts = []
        creation_date = None
        if hasattr(self.supplier, 'date_added') and self.supplier.date_added:
            creation_date = self.supplier.date_added
        elif hasattr(self.supplier, 'created_at') and self.supplier.created_at:
            creation_date = self.supplier.created_at

        if creation_date:
            days_since_creation = (datetime.now() - creation_date).days
            info_parts.append(f"مضى {days_since_creation} يوم على الإضافة")

        if self.supplier.balance:
            balance_abs = abs(self.supplier.balance)
            if balance_abs > 1000:
                info_parts.append(f"رصيد كبير: {balance_abs:.0f} جنيه")

        return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

    def get_account_summary(self):
        """ملخص الحساب"""
        balance = self.supplier.balance if self.supplier.balance else 0
        status = "دائن" if balance > 0 else "مدين" if balance < 0 else "متوازن"
        return f"الحساب {status} بمبلغ {abs(balance):.0f} جنيه"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;  /* تقليل padding لصالح البيانات */
                margin: 5px 0;  /* تقليل margin لصالح البيانات */
                min-height: 65px;  /* تحديد ارتفاع أقل */
                max-height: 70px;  /* تحديد ارتفاع أقصى أقل */
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)  # تقليل المسافة بين الأزرار لاستغلال المساحة

        # زر الإغلاق - في المقدمة مكان التعديل
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        close_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة - عرض أكبر لاستغلال المساحة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        print_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF - عرض أكبر لاستغلال المساحة
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        export_pdf_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة - عرض أكبر لاستغلال المساحة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)  # زيادة العرض لاستغلال المساحة الفارغة
        note_btn.setMaximumHeight(45)  # تقليل الارتفاع لصالح البيانات
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار مع الإغلاق في المقدمة واستغلال كامل للمساحة
        buttons_layout.addWidget(close_btn)  # زر الإغلاق في المقدمة
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم متطور مطابق للنموذج المرجعي
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                color_pair = colors.get(button_type, ('#6B7280', '#9CA3AF'))

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[1]});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[0]});
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات المورد المحسنة"""
        try:

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخطوط
                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y = 100

                # العنوان الرئيسي
                painter.setFont(title_font)
                painter.drawText(100, y, f"تقرير تفصيلي للمورد: {self.supplier.name}")
                y += 80

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات الأساسية:")
                y += 40

                painter.setFont(normal_font)
                painter.drawText(120, y, f"المعرف: #{str(self.supplier.id).zfill(6)}")
                y += 30
                painter.drawText(120, y, f"الاسم: {self.supplier.name}")
                y += 30
                painter.drawText(120, y, f"البريد الإلكتروني: {self.supplier.email or 'غير محدد'}")
                y += 30
                painter.drawText(120, y, f"الهاتف: {self.supplier.phone or 'غير محدد'}")
                y += 30
                painter.drawText(120, y, f"العنوان: {self.supplier.address or 'غير محدد'}")
                y += 50

                # المعلومات المالية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات المالية:")
                y += 40

                painter.setFont(normal_font)
                balance = self.supplier.balance or 0
                painter.drawText(120, y, f"الرصيد الحالي: {balance:.0f} جنيه")
                y += 30
                painter.drawText(120, y, f"حالة الحساب: {self.get_account_status()}")
                y += 30
                painter.drawText(120, y, f"تصنيف المورد: {self.get_supplier_type()}")
                y += 50

                # معلومات التاريخ
                painter.setFont(header_font)
                painter.drawText(100, y, "معلومات التاريخ:")
                y += 40

                painter.setFont(normal_font)
                creation_date = self.get_creation_date()
                if creation_date != "غير محدد":
                    painter.drawText(120, y, f"تاريخ الإضافة: {creation_date}")
                    y += 30
                painter.drawText(120, y, f"مدة التعامل: {self.get_membership_duration()}")
                y += 30
                painter.drawText(120, y, f"تاريخ الطباعة: {format_datetime_for_export()}")
                y += 50

                # الملاحظات
                if self.supplier.notes:
                    painter.setFont(header_font)
                    painter.drawText(100, y, "الملاحظات:")
                    y += 40

                    painter.setFont(normal_font)
                    painter.drawText(120, y, self.supplier.notes[:100] + "..." if len(self.supplier.notes) > 100 else self.supplier.notes)

                painter.end()

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"فشل في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات المورد إلى PDF"""
        try:

            # اختيار مكان الحفظ
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير معلومات المورد إلى PDF",
                f"supplier_{self.supplier.name}_{format_datetime_for_filename()}.pdf",
                "PDF Files (*.pdf)"
            )

            if not filename:
                return

            # إعداد الطابعة للـ PDF
            printer = QPrinter()
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(filename)
            printer.setPageSize(QPrinter.A4)

            # إنشاء الرسام
            painter = QPainter()
            painter.begin(printer)

            # إعداد الخطوط
            title_font = QFont("Arial", 18, QFont.Bold)
            subtitle_font = QFont("Arial", 14, QFont.Bold)
            content_font = QFont("Arial", 12)
            info_font = QFont("Arial", 10)

            # الحصول على أبعاد الصفحة
            page_rect = printer.pageRect()
            margin = 50
            content_rect = QRect(margin, margin,
                               page_rect.width() - 2*margin,
                               page_rect.height() - 2*margin)

            y_pos = content_rect.top()

            # رسم العنوان الرئيسي
            painter.setFont(title_font)
            painter.setPen(QColor(0, 0, 0))
            title_text = f"🏢 معلومات المورد: {self.supplier.name}"
            painter.drawText(content_rect.left(), y_pos, title_text)
            y_pos += 70

            # رسم خط فاصل
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 50

            # المعلومات الأساسية
            painter.setFont(subtitle_font)
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(content_rect.left(), y_pos, "المعلومات الأساسية:")
            y_pos += 40

            painter.setFont(content_font)
            painter.setPen(QColor(50, 50, 50))

            basic_info = [
                f"معرف المورد: {self.supplier.id}",
                f"الاسم: {self.supplier.name}",
                f"البريد الإلكتروني: {self.supplier.email or 'غير محدد'}",
                f"الهاتف: {self.supplier.phone or 'غير محدد'}",
                f"العنوان: {self.supplier.address or 'غير محدد'}",
                f"الرصيد: {self.supplier.balance or 0} جنيه"
            ]

            for line in basic_info:
                painter.drawText(content_rect.left() + 20, y_pos, line)
                y_pos += 30

            y_pos += 20

            # الملاحظات
            if self.supplier.notes:
                painter.setFont(subtitle_font)
                painter.setPen(QColor(0, 0, 0))
                painter.drawText(content_rect.left(), y_pos, "الملاحظات:")
                y_pos += 40

                painter.setFont(content_font)
                painter.setPen(QColor(50, 50, 50))

                # تقسيم الملاحظات إلى أسطر
                notes_lines = self.supplier.notes.split('\n')
                for line in notes_lines:
                    if y_pos + 30 > content_rect.bottom():
                        printer.newPage()
                        y_pos = content_rect.top()

                    painter.drawText(content_rect.left() + 20, y_pos, line)
                    y_pos += 30

                y_pos += 20

            # معلومات التصدير
            y_pos += 30
            painter.setPen(QPen(QColor(100, 100, 100), 1))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 30

            painter.setFont(info_font)
            painter.setPen(QColor(100, 100, 100))
            export_info = f"تم التصدير في: {format_datetime_for_export()}"
            painter.drawText(content_rect.left(), y_pos, export_info)

            painter.end()

            show_supplier_advanced_info(self, "نجح", f"تم تصدير معلومات المورد إلى:\n{filename}")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"فشل في التصدير: {str(e)}")

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddSupplierNoteDialog(self, self.supplier, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث العرض في النافذة الحالية
                self.refresh_supplier_info()

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_supplier_info(self):
        """تحديث معلومات المورد"""
        try:
            # إعادة تحميل بيانات المورد من قاعدة البيانات
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                updated_supplier = self.parent_widget.session.query(Supplier).get(self.supplier.id)
                if updated_supplier:
                    self.supplier = updated_supplier
                    # إعادة إعداد الواجهة
                    self.setup_ui()
        except Exception as e:
            print(f"خطأ في تحديث معلومات المورد: {str(e)}")





    def create_shadow_effect(self):
        """إنشاء تأثير الظل للنافذة"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setXOffset(0)
        shadow.setYOffset(0)
        shadow.setColor(QColor(0, 0, 0, 100))
        return shadow

    def create_window_icon(self):
        """إنشاء أيقونة النافذة"""
        pixmap = QPixmap(48, 48)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        gradient = QRadialGradient(24, 24, 20)
        gradient.setColorAt(0, QColor(59, 130, 246))
        gradient.setColorAt(0.7, QColor(29, 78, 216))
        gradient.setColorAt(1, QColor(30, 64, 175))

        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
        painter.drawEllipse(4, 4, 40, 40)
        painter.setPen(QPen(QColor(255, 255, 255), 3))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(12, 30, "🏢")
        painter.end()

        return QIcon(pixmap)

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(59, 130, 246))
            gradient.setColorAt(0.7, QColor(29, 78, 216))
            gradient.setColorAt(1, QColor(30, 64, 175))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🏢")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")

    # ===============================
    # الدوال المرجعية للتصميم
    # ===============================

    @staticmethod
    def get_reference_colors():
        """الحصول على الألوان المرجعية للنظام"""
        return {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'vip': '#FFD700',           # ذهبي للمميزين
            'new': '#00FFFF',           # سماوي نيون للجدد
            'normal': '#DA70D6',        # بنفسجي نيون للعاديين
            'high': '#FF4500',          # برتقالي أحمر للعالي
            'medium': '#FFD700',        # ذهبي للمتوسط
            'low': '#C0C0C0',           # فضي للمنخفض
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي للنوافذ بدون إطار"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: none;
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """


class AddSupplierNoteDialog(QDialog):
    """نافذة ملاحظات المورد - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, supplier=None, parent_widget=None):
        super().__init__(parent)
        self.supplier = supplier
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً - مطابق للنموذج المرجعي"""
        self.setWindowTitle(f"📝 {self.supplier.name if self.supplier.name else 'مورد'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(SupplierInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)  # رفع النص 3 درجات (8-3=5 للأعلى، 8+3=11 للأسفل)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف - مكبر ومرفوع
        title_text = QLabel(f"📝 ملاحظة للمورد: {self.supplier.name}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        # وضع النص في المنتصف تماماً
        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # محرر النصوص المتطور مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(248, 250, 252, 0.1));
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', Arial, sans-serif;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                selection-background-color: rgba(59, 130, 246, 0.5);
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(255, 215, 0, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:1 rgba(248, 250, 252, 0.15));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن المورد، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        cancel_btn.setMinimumHeight(45)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        save_btn.setMinimumHeight(45)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور"""
        # استخدام نفس التصميم المتطور من النموذج المرجعي
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            # تصميم متطور مطابق للنموذج المرجعي
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'border': '#0ea5e9', 'shadow': 'rgba(14, 165, 233, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['bg_start']},
                        stop:0.15 {color_set['bg_mid']},
                        stop:0.85 {color_set['bg_end']},
                        stop:1 {color_set['bg_bottom']});
                    color: #ffffff;
                    border: 4px solid {color_set['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['hover_start']},
                        stop:0.15 {color_set['hover_mid']},
                        stop:0.85 {color_set['hover_end']},
                        stop:1 {color_set['hover_bottom']});
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3);
                }}
                QPushButton:pressed {{
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_set['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.2),
                               inset 0 -1px 0 rgba(255, 255, 255, 0.2);
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة الحالية"""
        if self.supplier and self.supplier.notes:
            self.text_editor.setPlainText(self.supplier.notes)

    def save_note(self):
        """حفظ الملاحظة - مطابق للنموذج المرجعي"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.supplier.notes = note if note else None

            if self.parent_widget and self.parent_widget.session:
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            show_supplier_advanced_info(self, "تم", "حُفظت الملاحظة")

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"فشل الحفظ: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليكون أسود"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للموردين: {e}")
            # المتابعة بدون تصميم متقدم لشريط العنوان


# ===============================
# نوافذ التحذير المتطورة للموردين
# ===============================

class SupplierWarningDialog(QDialog):
    """نافذة تحذير متطورة للموردين مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تحذير", message="", icon="⚠️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 200)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(251, 191, 36, 0.2),
                    stop:0.5 rgba(245, 158, 11, 0.3),
                    stop:1 rgba(217, 119, 6, 0.2));
                border: 2px solid rgba(251, 191, 36, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التحذير
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10B981',
                    'info': '#3B82F6',
                    'danger': '#EF4444'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(251, 191, 36))
            gradient.setColorAt(0.7, QColor(245, 158, 11))
            gradient.setColorAt(1, QColor(217, 119, 6))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon_text)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class SupplierErrorDialog(QDialog):
    """نافذة خطأ متطورة للموردين مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="خطأ", message="", icon="❌"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 200)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة الخطأ
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'danger')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10B981',
                    'info': '#3B82F6',
                    'danger': '#EF4444'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon_text)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class SupplierMessageDialog(QDialog):
    """نافذة رسائل متطورة للموردين مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="معلومات", message="", icon="ℹ️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 200)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.2),
                    stop:0.5 rgba(5, 150, 105, 0.3),
                    stop:1 rgba(4, 120, 87, 0.2));
                border: 2px solid rgba(16, 185, 129, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المعلومات
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10B981',
                    'info': '#3B82F6',
                    'danger': '#EF4444'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(16, 185, 129))
            gradient.setColorAt(0.7, QColor(5, 150, 105))
            gradient.setColorAt(1, QColor(4, 120, 87))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon_text)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class SupplierConfirmationDialog(QDialog):
    """نافذة تأكيد متطورة للموردين مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تأكيد", message="", icon="❓"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.result = False
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 220)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التأكيد
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        no_button = QPushButton("❌ لا")
        self.style_advanced_button(no_button, 'danger')
        no_button.clicked.connect(self.reject_action)

        yes_button = QPushButton("✅ نعم")
        self.style_advanced_button(yes_button, 'success')
        yes_button.clicked.connect(self.accept_action)

        buttons_layout.addWidget(no_button)
        buttons_layout.addWidget(yes_button)
        layout.addLayout(buttons_layout)

    def accept_action(self):
        """قبول التأكيد"""
        self.result = True
        self.accept()

    def reject_action(self):
        """رفض التأكيد"""
        self.result = False
        self.reject()

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10B981',
                    'info': '#3B82F6',
                    'danger': '#EF4444'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(59, 130, 246))
            gradient.setColorAt(0.7, QColor(37, 99, 235))
            gradient.setColorAt(1, QColor(29, 78, 216))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, self.icon_text)
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


# ===============================
# دوال مساعدة لعرض النوافذ المتطورة
# ===============================

def ensure_supplier_dialog_stays_visible(dialog):
    """التأكد من أن النافذة تبقى مرئية"""
    if dialog:
        dialog.setModal(True)
        dialog.raise_()
        dialog.activateWindow()
        dialog.show()
        return dialog

def show_supplier_advanced_warning(parent, title, message, icon="⚠️"):
    """عرض نافذة تحذير متطورة للموردين"""
    dialog = SupplierWarningDialog(parent, title, message, icon)
    return ensure_supplier_dialog_stays_visible(dialog).exec_()

def show_supplier_advanced_error(parent, title, message, icon="❌"):
    """عرض نافذة خطأ متطورة للموردين"""
    dialog = SupplierErrorDialog(parent, title, message, icon)
    return ensure_supplier_dialog_stays_visible(dialog).exec_()

def show_supplier_advanced_info(parent, title, message, icon="ℹ️"):
    """عرض نافذة معلومات متطورة للموردين"""
    dialog = SupplierMessageDialog(parent, title, message, icon)
    return ensure_supplier_dialog_stays_visible(dialog).exec_()

def show_supplier_advanced_confirmation(parent, title, message, icon="❓"):
    """عرض نافذة تأكيد متطورة للموردين"""
    dialog = SupplierConfirmationDialog(parent, title, message, icon)
    ensure_supplier_dialog_stays_visible(dialog).exec_()
    return dialog.result


class EditSupplierAmountDialog(QDialog):
    """نافذة بسيطة لتعديل مبلغ المورد - إضافة أو تقليل فقط"""

    def __init__(self, parent=None, supplier=None, session=None):
        super().__init__(parent)
        self.supplier = supplier
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة البسيطة"""
        # إعداد النافذة الأساسي
        self.setWindowTitle(f"💰 تعديل المبلغ - نظام إدارة الموردين المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 300)

        # تخصيص شريط العنوان ليتطابق مع باقي النوافذ
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel(f"💰 تعديل رصيد المورد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                padding: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6), stop:1 rgba(139, 92, 246, 0.6));
                border-radius: 12px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # معلومات المورد
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)

        # اسم المورد
        name_label = QLabel(f"🏢 المورد: {self.supplier.name}")
        name_label.setStyleSheet("color: #ffffff; font-size: 14px; font-weight: bold;")
        info_layout.addWidget(name_label)

        # الرصيد الحالي
        current_balance = self.supplier.balance or 0
        balance_label = QLabel(f"💳 الرصيد الحالي: {format_currency(current_balance)}")
        if current_balance > 0:
            balance_color = "#f87171"  # أحمر للدين
        elif current_balance < 0:
            balance_color = "#34d399"  # أخضر للرصيد الموجب
        else:
            balance_color = "#d1d5db"  # رمادي للصفر
        balance_label.setStyleSheet(f"color: {balance_color}; font-size: 14px; font-weight: bold;")
        info_layout.addWidget(balance_label)

        layout.addWidget(info_frame)

        # حقل تعديل المبلغ
        amount_frame = QFrame()
        amount_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        amount_layout = QFormLayout(amount_frame)

        # تسمية المبلغ
        amount_label = QLabel("المبلغ المراد إضافته أو تقليله:")
        amount_label.setStyleSheet("color: #ffffff; font-weight: bold; font-size: 13px;")

        # حقل المبلغ
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(-999999, 999999)
        self.amount_edit.setDecimals(0)
        self.amount_edit.setSingleStep(100)
        self.amount_edit.setValue(0)
        self.amount_edit.setPrefix("💰 ")
        self.amount_edit.setSuffix(" جنيه")
        self.amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                color: #1e293b;
            }
            QDoubleSpinBox:focus {
                border: 3px solid rgba(59, 130, 246, 0.8);
                background: rgba(219, 234, 254, 0.9);
            }
        """)

        amount_layout.addRow(amount_label, self.amount_edit)

        # ملاحظة توضيحية
        note_label = QLabel("💡 القيم الموجبة تزيد الرصيد، والقيم السالبة تقلل الرصيد")
        note_label.setStyleSheet("color: #fbbf24; font-size: 12px; font-style: italic;")
        amount_layout.addRow(note_label)

        layout.addWidget(amount_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر الحفظ - استخدام نفس تصميم الشريط الرئيسي
        save_button = QPushButton("💾 حفظ التعديل")
        save_button.clicked.connect(self.save_changes)
        save_button.setMinimumHeight(45)
        self.style_advanced_button(save_button, 'emerald')

        # زر الإلغاء - استخدام نفس تصميم الشريط الرئيسي
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setMinimumHeight(45)
        self.style_advanced_button(cancel_button, 'danger')

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        layout.addLayout(buttons_layout)

    def save_changes(self):
        """حفظ التعديل على رصيد المورد"""
        try:
            amount = self.amount_edit.value()

            # التحقق من صحة المبلغ
            if amount == 0:
                show_supplier_advanced_warning(self, "تحذير", "يجب إدخال مبلغ غير صفر")
                return

            # حفظ الرصيد القديم للمقارنة
            old_balance = self.supplier.balance or 0

            # تحديث رصيد المورد مع التحقق التلقائي من الأقساط
            operation = 'add' if amount >= 0 else 'subtract'
            amount_abs = abs(amount)
            success, new_balance = update_supplier_balance(
                self.session, self.supplier.id, amount_abs, operation, True
            )

            # إظهار رسالة نجاح
            if amount > 0:
                operation = "إضافة"
                icon = "➕"
            else:
                operation = "تقليل"
                icon = "➖"

            show_supplier_advanced_info(
                self,
                "تم بنجاح",
                f"{icon} تم {operation} {format_currency(abs(amount))} {operation} رصيد المورد {self.supplier.name}\n\n"
                f"📊 الرصيد السابق: {format_currency(old_balance)}\n"
                f"📊 الرصيد الجديد: {format_currency(new_balance)}"
            )

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            # التراجع عن التغييرات في حالة الخطأ
            self.session.rollback()
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ أثناء حفظ التعديل:\n{str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار - مطابق للشريط الرئيسي"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'emerald': {
                        'base': '#10B981',
                        'hover': '#059669',
                        'pressed': '#047857',
                        'shadow': 'rgba(16, 185, 129, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    },
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'orange': {
                        'base': '#F97316',
                        'hover': '#EA580C',
                        'pressed': '#C2410C',
                        'shadow': 'rgba(249, 115, 22, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                # تطبيق التصميم المتطور
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                    QPushButton:disabled {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #6B7280, stop:1 #4B5563);
                        color: #9CA3AF;
                        border: 2px solid rgba(255, 255, 255, 0.1);
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليتطابق مع باقي النوافذ"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            # تطبيق تصميم بسيط للشريط في حالة الفشل
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")


class SupplierStatisticsDialog(QDialog):
    """نافذة إحصائيات الموردين مطابقة للعملاء"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي مع شريط عنوان موحد
        self.setWindowTitle("📊 إحصائيات الموردين - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(480, 380)  # عرض وارتفاع مضغوط

        # تخصيص شريط العنوان الموحد
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)  # هوامش مضغوطة جداً
        layout.setSpacing(6)  # مسافات مضغوطة جداً

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 4px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(3)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات الموردين")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                padding: 4px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة الموردين والأرصدة المالية")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 13px;
                font-weight: normal;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                padding: 2px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(3)  # مسافات مضغوطة جداً
        stats_layout.setContentsMargins(8, 4, 8, 4)  # هوامش مضغوطة جداً

        # إنشاء قائمة الإحصائيات

        stats_items = [
            ("🏭", "إجمالي الموردين المسجلين في النظام", str(self.total_suppliers), "#3B82F6", "📊"),
            ("✅", "الموردين النشطين (لديهم أرصدة مستحقة)", str(self.active_suppliers), "#10B981", "💚"),
            ("⚪", "الموردين العاديين (رصيد متوازن صفر)", str(self.normal_suppliers), "#F59E0B", "🟡"),
            ("❌", "الموردين المدينين (عليهم مبالغ مستحقة)", str(self.debtor_suppliers), "#EF4444", "🔴"),
            ("💰", "إجمالي رصيد الدائنين", format_currency(self.creditor_balance), "#10B981", "⬆️"),
            ("💸", "إجمالي رصيد المدينين", format_currency(self.debtor_balance), "#EF4444", "⬇️"),
            ("🧮", "الرصيد الصافي", format_currency(self.net_balance), "#8B5CF6", "⚖️"),
            ("📊", "نسبة الموردين المدينين", f"{self.debtor_percentage:.1f}%", "#EF4444", "📈"),
            ("📅", "موردين جدد هذا الشهر", str(self.suppliers_this_month), "#8B5CF6", "🆕"),
            ("🗓️", "موردين جدد هذا العام", str(self.suppliers_this_year), "#06B6D4", "📆")
        ]

        for icon, title, value, color, secondary_icon in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 1px;
                    margin: 0px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 3px;
                }
            """)
            item_widget.setFixedHeight(28)  # ارتفاع مضغوط جداً

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(6)
            item_layout.setContentsMargins(6, 2, 6, 2)

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(20, 20)  # حجم أصغر جداً
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 13px;
                    font-weight: normal;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    padding: 1px 2px;
                }
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px 4px;
                    min-width: 50px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # قسم أعلى 5 موردين
        if hasattr(self, 'top_5_suppliers') and self.top_5_suppliers:
            top_suppliers_container = QWidget()
            top_suppliers_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 10px;
                    margin: 5px;
                }
            """)

            top_suppliers_layout = QVBoxLayout(top_suppliers_container)
            top_suppliers_layout.setSpacing(5)
            top_suppliers_layout.setContentsMargins(10, 5, 10, 5)

            # عنوان القسم
            top_title = QLabel("🏆 أعلى 5 موردين (حسب الرصيد)")
            top_title.setAlignment(Qt.AlignCenter)
            top_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    padding: 3px;
                }
            """)
            top_suppliers_layout.addWidget(top_title)

            # قائمة أعلى 5 موردين
            for i, supplier_data in enumerate(self.top_5_suppliers, 1):
                supplier_widget = QWidget()
                supplier_widget.setStyleSheet("""
                    QWidget {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.1);
                    }
                """)

                supplier_layout = QHBoxLayout(supplier_widget)
                supplier_layout.setContentsMargins(8, 4, 8, 4)

                # رقم الترتيب
                rank_label = QLabel(f"{i}")
                rank_label.setStyleSheet("""
                    QLabel {
                        color: #FFD700;
                        font-size: 13px;
                        font-weight: bold;
                        font-family: 'Segoe UI', 'Tahoma', sans-serif;
                        background: transparent;
                        min-width: 20px;
                    }
                """)
                rank_label.setAlignment(Qt.AlignCenter)
                supplier_layout.addWidget(rank_label)

                # اسم المورد
                name_label = QLabel(supplier_data['name'])
                name_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 13px;
                        font-family: 'Segoe UI', 'Tahoma', sans-serif;
                        background: transparent;
                    }
                """)
                supplier_layout.addWidget(name_label)

                # الرصيد
                balance_label = QLabel(format_currency(supplier_data['balance']))
                balance_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 13px;
                        font-weight: bold;
                        font-family: 'Segoe UI', 'Tahoma', sans-serif;
                        background: transparent;
                    }
                """)
                balance_label.setAlignment(Qt.AlignRight)
                supplier_layout.addWidget(balance_label)

                top_suppliers_layout.addWidget(supplier_widget)

            layout.addWidget(top_suppliers_container)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تصدير
        export_pdf_button = QPushButton("📄 تصدير")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        export_pdf_button.setMinimumHeight(45)
        self.style_advanced_button(export_pdf_button, 'info')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب الإحصائيات"""
        try:
            # استخدام database بدلاً من models
            suppliers = self.session.query(Supplier).all()

            self.total_suppliers = len(suppliers)
            self.active_suppliers = 0
            self.debtor_suppliers = 0
            self.normal_suppliers = 0

            # حساب الأرصدة المختلفة
            self.total_balance = 0
            self.creditor_balance = 0  # الدائنين (رصيد موجب)
            self.debtor_balance = 0    # المدينين (رصيد سالب)

            for supplier in suppliers:
                balance = supplier.balance or 0
                self.total_balance += balance

                if balance > 0:
                    self.active_suppliers += 1
                    self.creditor_balance += balance
                elif balance == 0:
                    self.normal_suppliers += 1
                else:
                    self.debtor_suppliers += 1
                    self.debtor_balance += abs(balance)  # قيمة مطلقة للمدينين

            # حساب الرصيد الصافي
            self.net_balance = self.creditor_balance - self.debtor_balance

            # حساب نسبة الموردين المدينين
            self.debtor_percentage = (self.debtor_suppliers / self.total_suppliers * 100) if self.total_suppliers > 0 else 0

            # أعلى 5 موردين حسب الرصيد
            sorted_suppliers = sorted(suppliers, key=lambda s: s.balance or 0, reverse=True)
            self.top_5_suppliers = [
                {
                    'name': supplier.name or 'غير محدد',
                    'balance': supplier.balance or 0
                }
                for supplier in sorted_suppliers[:5]
            ]

            # حساب الموردين المضافين هذا الشهر والسنة
            from datetime import datetime
            now = datetime.now()
            start_of_month = datetime(now.year, now.month, 1)
            start_of_year = datetime(now.year, 1, 1)

            self.suppliers_this_month = 0
            self.suppliers_this_year = 0

            for supplier in suppliers:
                if supplier.created_at:
                    if supplier.created_at >= start_of_month:
                        self.suppliers_this_month += 1
                    if supplier.created_at >= start_of_year:
                        self.suppliers_this_year += 1

        except Exception as e:
            print(f"خطأ في حساب الإحصائيات: {e}")
            self.total_suppliers = 0
            self.active_suppliers = 0
            self.debtor_suppliers = 0
            self.normal_suppliers = 0
            self.total_balance = 0
            self.creditor_balance = 0
            self.debtor_balance = 0
            self.net_balance = 0
            self.debtor_percentage = 0
            self.suppliers_this_month = 0
            self.suppliers_this_year = 0
            self.top_5_suppliers = []

    def export_statistics_to_pdf(self):
        """تصدير إحصائيات الموردين إلى ملف PDF باللغة العربية"""
        try:
            # التأكد من حساب الإحصائيات أولاً
            if not hasattr(self, 'total_suppliers'):
                self.calculate_statistics()

            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from PyQt5.QtWidgets import QFileDialog
            import os

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير إحصائيات الموردين",
                f"تقرير_إحصائيات_الموردين_{format_datetime_for_filename()}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return

            # إنشاء المستند مع حجم مخصص للمحتوى
            from reportlab.lib.pagesizes import letter

            doc = SimpleDocTemplate(
                file_path,
                pagesize=letter,  # حجم أصغر من A4
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=20,  # هامش سفلي أقل
                title="تقرير إحصائيات الموردين"
            )

            # قائمة العناصر
            story = []
            styles = getSampleStyleSheet()

            # دالة لإصلاح النص العربي
            def fix_arabic_text(text):
                """إصلاح النص العربي للعرض الصحيح في PDF"""
                try:
                    if isinstance(text, str):
                        # استخدام arabic-reshaper لإصلاح شكل الأحرف العربية
                        import arabic_reshaper
                        from bidi.algorithm import get_display

                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(text)
                        # إصلاح اتجاه النص
                        bidi_text = get_display(reshaped_text)

                        return bidi_text
                    return text
                except ImportError:
                    # إذا لم تكن المكتبات متاحة، استخدم النص كما هو
                    return text
                except Exception:
                    return text

            # تسجيل خط عربي (إذا كان متاحاً)
            try:
                # محاولة استخدام خط عربي
                arabic_font_path = "C:/Windows/Fonts/arial.ttf"  # خط Arial يدعم العربية
                if os.path.exists(arabic_font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'

            # العنوان الرئيسي
            title_style = ParagraphStyle(
                'ArabicTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=20,
                alignment=1,  # وسط
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("📊 تقرير إحصائيات الموردين"), title_style))
            story.append(Spacer(1, 8))

            # معلومات التقرير
            info_style = ParagraphStyle(
                'ArabicInfo',
                parent=styles['Normal'],
                fontSize=10,
                alignment=1,
                textColor=colors.grey,
                fontName=font_name,
                spaceAfter=5
            )

            story.append(Paragraph(fix_arabic_text(f"تاريخ التقرير: {format_datetime_for_export()}"), info_style))
            story.append(Spacer(1, 12))

            # عنوان قسم الإحصائيات العامة
            section_style = ParagraphStyle(
                'SectionTitle',
                parent=styles['Heading2'],
                fontSize=13,
                spaceAfter=8,
                alignment=2,  # يمين للعربية
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("📈 الإحصائيات الأساسية"), section_style))
            story.append(Spacer(1, 5))

            # إنشاء جدول الإحصائيات المختصر (عكس الترتيب)
            data = [
                # عكس ترتيب الأعمدة للعربية
                [fix_arabic_text('الوصف'), fix_arabic_text('القيمة'), fix_arabic_text('البيان')],
                [fix_arabic_text('العدد الكلي للموردين'), str(getattr(self, 'total_suppliers', 0)), fix_arabic_text('📊 إجمالي الموردين')],
                [fix_arabic_text('موردين نشطين'), str(getattr(self, 'active_suppliers', 0)), fix_arabic_text('✅ الموردين النشطين')],
                [fix_arabic_text('موردين عاديين'), str(getattr(self, 'normal_suppliers', 0)), fix_arabic_text('⚪ الموردين العاديين')],
                [fix_arabic_text('موردين مدينين'), str(getattr(self, 'debtor_suppliers', 0)), fix_arabic_text('❌ الموردين المدينين')],
                [fix_arabic_text('المبالغ المستحقة للموردين'), format_currency(getattr(self, 'creditor_balance', 0)), fix_arabic_text('💰 رصيد الدائنين')],
                [fix_arabic_text('المبالغ المستحقة على الموردين'), f"-{format_currency(getattr(self, 'debtor_balance', 0))}", fix_arabic_text('💸 رصيد المدينين')],
                [fix_arabic_text('الرصيد الصافي'), format_currency(getattr(self, 'net_balance', 0)), fix_arabic_text('🧮 الرصيد الصافي')]
            ]

            # إنشاء الجدول مع ترتيب معكوس للعربية
            table = Table(data, colWidths=[2.5*inch, 1.5*inch, 2*inch])
            table.setStyle(TableStyle([
                # تصميم الرأس
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),  # محاذاة يمين للعربية
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('TOPPADDING', (0, 0), (-1, 0), 12),

                # تصميم البيانات
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightblue, colors.lightcyan]),

                # تلوين خاص لرصيد المدينين (أحمر فاتح)
                ('BACKGROUND', (0, 6), (-1, 6), colors.mistyrose),
                ('TEXTCOLOR', (0, 6), (1, 6), colors.darkred),

                # تلوين خاص لرصيد الدائنين (أخضر فاتح)
                ('BACKGROUND', (0, 5), (-1, 5), colors.lightgreen),
                ('TEXTCOLOR', (0, 5), (1, 5), colors.darkgreen),

                ('GRID', (0, 0), (-1, -1), 1, colors.darkblue),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 1), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 1), (-1, -1), 6),
            ]))

            story.append(table)
            story.append(Spacer(1, 12))

            # قسم أعلى 3 موردين (مختصر)
            if hasattr(self, 'top_5_suppliers') and self.top_5_suppliers:
                story.append(Paragraph(fix_arabic_text("🏆 أعلى الموردين"), section_style))
                story.append(Spacer(1, 5))

                # عكس ترتيب الأعمدة للعربية
                top_data = [[fix_arabic_text('💰 الرصيد'), fix_arabic_text('👤 اسم المورد'), fix_arabic_text('🏅 الترتيب')]]

                # أخذ أعلى 3 موردين فقط
                for i, supplier_data in enumerate(self.top_5_suppliers[:3], 1):
                    balance = supplier_data['balance']
                    rank_icon = "🥇" if i == 1 else "🥈" if i == 2 else "🥉"

                    top_data.append([
                        format_currency(balance),
                        fix_arabic_text(supplier_data['name']),
                        f"{rank_icon} {i}"
                    ])

                top_table = Table(top_data, colWidths=[1.5*inch, 2.5*inch, 1*inch])
                top_table.setStyle(TableStyle([
                    # تصميم الرأس
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),  # محاذاة يمين للعربية
                    ('FONTNAME', (0, 0), (-1, -1), font_name),
                    ('FONTSIZE', (0, 0), (-1, 0), 11),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 10),
                    ('TOPPADDING', (0, 0), (-1, 0), 10),

                    # تصميم البيانات
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                    ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.lightgreen, colors.lightyellow]),
                    ('GRID', (0, 0), (-1, -1), 1, colors.darkgreen),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 5),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 5),
                    ('TOPPADDING', (0, 1), (-1, -1), 6),
                    ('BOTTOMPADDING', (0, 1), (-1, -1), 6),
                ]))

                story.append(top_table)
                story.append(Spacer(1, 12))

            # ملخص مختصر
            story.append(Paragraph(fix_arabic_text("📋 ملخص التقرير"), section_style))
            story.append(Spacer(1, 5))

            # نمط الملخص
            summary_style = ParagraphStyle(
                'SummaryStyle',
                parent=styles['Normal'],
                fontSize=9,
                alignment=2,  # محاذاة يمين للعربية
                textColor=colors.black,
                fontName=font_name,
                spaceAfter=4,
                rightIndent=15
            )

            # حساب مؤشرات أساسية
            total_suppliers = getattr(self, 'total_suppliers', 0)
            active_suppliers = getattr(self, 'active_suppliers', 0)
            debtor_suppliers = getattr(self, 'debtor_suppliers', 0)
            creditor_balance = getattr(self, 'creditor_balance', 0)
            debtor_balance = getattr(self, 'debtor_balance', 0)
            net_balance = getattr(self, 'net_balance', 0)

            # حساب النسب
            debtor_percentage = (debtor_suppliers / total_suppliers * 100) if total_suppliers > 0 else 0
            active_percentage = (active_suppliers / total_suppliers * 100) if total_suppliers > 0 else 0

            # ملخص مختصر
            summary_text = [
                fix_arabic_text(f"إجمالي الموردين: {total_suppliers} مورد"),
                fix_arabic_text(f"الموردين النشطين: {active_suppliers} مورد ({active_percentage:.1f}%)"),
                fix_arabic_text(f"الموردين المدينين: {debtor_suppliers} مورد ({debtor_percentage:.1f}%)"),
                fix_arabic_text(f"رصيد الدائنين: {format_currency(creditor_balance)}"),
                fix_arabic_text(f"رصيد المدينين: -{format_currency(debtor_balance)}"),
                fix_arabic_text(f"الرصيد الصافي: {format_currency(net_balance)}"),
            ]

            # تقييم سريع
            if net_balance > 0:
                summary_text.append(fix_arabic_text("الوضع المالي: إيجابي ✅"))
            elif net_balance < 0:
                summary_text.append(fix_arabic_text("الوضع المالي: يحتاج مراجعة ⚠️"))
            else:
                summary_text.append(fix_arabic_text("الوضع المالي: متوازن ⚖️"))

            for text in summary_text:
                story.append(Paragraph(text, summary_style))

            # بناء المستند
            doc.build(story)

            show_supplier_advanced_info(self, "تم بنجاح", f"تم تصدير التقرير الشامل بنجاح إلى:\n{file_path}")

        except ImportError:
            show_supplier_advanced_error(self, "خطأ", "مكتبة ReportLab غير مثبتة.\nيرجى تثبيتها باستخدام: pip install reportlab")
        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")


class SupplierWhatsAppDialog(QDialog):
    """نافذة واتساب للموردين - مطابقة للعملاء"""

    def __init__(self, supplier, parent=None):
        super().__init__(parent)
        self.supplier = supplier
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle(f"📱 واتساب - {self.supplier.name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(500, 600)

        # تطبيق تصميم شريط العنوان
        self.customize_title_bar()

        # تطبيق نفس خلفية نافذة العملاء
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # حاوي العنوان
        title_container = QWidget()
        title_container.setStyleSheet("background: transparent;")
        title_inner_layout = QVBoxLayout()
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(5)
        title_container.setLayout(title_inner_layout)

        # العنوان الرئيسي
        main_title = QLabel(f"📞 التواصل مع المورد")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                padding: 10px;
            }
        """)

        # العنوان الفرعي
        subtitle = QLabel(f"إرسال رسائل واتساب والاتصال مع: {self.supplier.name}")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # معلومات المورد
        info_layout = QVBoxLayout()
        info_layout.setSpacing(10)

        # عرض أرقام الهاتف
        if self.supplier.phone:
            phone_numbers = [phone.strip() for phone in self.supplier.phone.split(",") if phone.strip()]
            for i, phone in enumerate(phone_numbers):
                # إطار رقم الهاتف والأزرار
                phone_frame = QFrame()
                phone_frame.setStyleSheet("""
                    QFrame {
                        background: rgba(255, 255, 255, 0.1);
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 10px;
                        margin: 3px;
                    }
                    QFrame:hover {
                        background: rgba(255, 255, 255, 0.15);
                        border: 2px solid rgba(255, 255, 255, 0.3);
                    }
                """)

                phone_layout = QHBoxLayout()
                phone_layout.setSpacing(10)

                # رقم الهاتف
                phone_label = QLabel(f"📱 {phone}")
                phone_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
                phone_label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        background: transparent;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                        padding: 5px;
                    }
                """)
                phone_layout.addWidget(phone_label)

                # أزرار الإجراءات
                whatsapp_btn = QPushButton("💬 واتساب")
                self.style_advanced_button(whatsapp_btn, 'emerald')
                whatsapp_btn.clicked.connect(lambda checked, p=phone: self.open_whatsapp(p))

                call_btn = QPushButton("📞 اتصال")
                self.style_advanced_button(call_btn, 'info')
                call_btn.clicked.connect(lambda checked, p=phone: self.make_call(p))

                phone_layout.addWidget(whatsapp_btn)
                phone_layout.addWidget(call_btn)

                phone_frame.setLayout(phone_layout)
                info_layout.addWidget(phone_frame)
        else:
            no_phone_label = QLabel("❌ لا توجد أرقام هاتف مسجلة لهذا المورد")
            no_phone_label.setAlignment(Qt.AlignCenter)
            no_phone_label.setStyleSheet("""
                QLabel {
                    color: #ff6b6b;
                    background: transparent;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 20px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                }
            """)
            info_layout.addWidget(no_phone_label)

        layout.addLayout(info_layout)

        # إطار كتابة الرسالة
        message_frame = QFrame()
        message_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.08);
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }
            QFrame:hover {
                background: rgba(255, 255, 255, 0.12);
                border: 2px solid rgba(255, 255, 255, 0.35);
            }
        """)
        message_layout = QVBoxLayout()
        message_layout.setSpacing(10)

        # عنوان القسم
        message_title = QLabel("✉️ كتابة رسالة واتساب")
        message_title.setAlignment(Qt.AlignCenter)
        message_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)
        message_layout.addWidget(message_title)

        # حقل كتابة الرسالة
        self.message_text = QTextEdit()
        self.message_text.setPlainText(f"مرحباً {self.supplier.name}،\n\nنتواصل معك من شركة Smart Finish.\n\nشكراً لك.")
        self.message_text.setStyleSheet("""
            QTextEdit {
                background: rgba(255, 255, 255, 0.95);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                color: #2d3748;
                min-height: 100px;
                max-height: 120px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(34, 197, 94, 0.6);
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
            }
        """)
        message_layout.addWidget(self.message_text)

        # زر إرسال واتساب
        whatsapp_send_btn = QPushButton("📱 إرسال واتساب")
        whatsapp_send_btn.setMinimumHeight(40)
        self.style_advanced_button(whatsapp_send_btn, 'emerald')
        whatsapp_send_btn.clicked.connect(self.send_whatsapp_message)
        message_layout.addWidget(whatsapp_send_btn)

        message_frame.setLayout(message_layout)
        layout.addWidget(message_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(20, 10, 20, 10)

        # زر إغلاق كبير ومتوسط مع التصميم الأصلي
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setMinimumWidth(150)
        close_btn.setMinimumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # توسيط الزر
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def open_whatsapp(self, phone_number):
        """فتح واتساب مع رقم الهاتف"""
        try:
            # تنظيف رقم الهاتف من المسافات والرموز
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # معالجة محسنة للأرقام المصرية
            if clean_phone.startswith('0') and len(clean_phone) == 11:
                # الأرقام المصرية التي تبدأ بـ 0 (مثل 01017760368)
                clean_phone = '2' + clean_phone  # تصبح 201017760368
            elif clean_phone.startswith('1') and len(clean_phone) == 10:
                # الأرقام بدون الصفر (مثل 1017760368)
                clean_phone = '20' + clean_phone  # تصبح 201017760368
            elif not clean_phone.startswith('20') and len(clean_phone) >= 10:
                # أي رقم آخر طويل بما فيه الكفاية
                clean_phone = '20' + clean_phone.lstrip('0')

            # التحقق النهائي من صحة الرقم
            if len(clean_phone) < 12 or not clean_phone.startswith('20'):
                # إذا لم ينجح شيء، نضع 20 في البداية
                original_clean = ''.join(filter(str.isdigit, phone_number))
                clean_phone = '20' + original_clean.lstrip('0')

            # رابط واتساب
            whatsapp_url = f"https://wa.me/{clean_phone}"
            webbrowser.open(whatsapp_url)

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في فتح واتساب: {str(e)}")

    def make_call(self, phone_number):
        """فتح واتساب مع خيار الاتصال المباشر"""
        try:

            # تنظيف رقم الهاتف وإضافة كود مصر
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # معالجة الأرقام المصرية
            if clean_phone.startswith('0') and len(clean_phone) == 11:
                clean_phone = '2' + clean_phone
            elif clean_phone.startswith('1') and len(clean_phone) == 10:
                clean_phone = '20' + clean_phone
            elif not clean_phone.startswith('20') and len(clean_phone) >= 10:
                clean_phone = '20' + clean_phone.lstrip('0')

            # التحقق النهائي من صحة الرقم
            if len(clean_phone) < 12 or not clean_phone.startswith('20'):
                original_clean = ''.join(filter(str.isdigit, phone_number))
                clean_phone = '20' + original_clean.lstrip('0')

            # محاولة فتح واتساب مع رابط الاتصال المباشر
            system = platform.system().lower()

            try:
                if system == "windows":
                    whatsapp_call_url = f"whatsapp://call?phone={clean_phone}"
                    subprocess.run(f'start "" "{whatsapp_call_url}"', shell=True, check=True)
                elif system == "darwin":  # macOS
                    whatsapp_call_url = f"whatsapp://call?phone={clean_phone}"
                    subprocess.run(['open', whatsapp_call_url])
                else:
                    whatsapp_call_url = f"whatsapp://call?phone={clean_phone}"
                    subprocess.run(['xdg-open', whatsapp_call_url])

            except:
                # إذا فشل رابط التطبيق، استخدم واتساب ويب مع رسالة اتصال
                try:
                    whatsapp_web_call = f"https://web.whatsapp.com/send?phone={clean_phone}&text=📞%20طلب%20اتصال"
                    webbrowser.open(whatsapp_web_call)
                except:
                    # كحل أخير، فتح واتساب عادي
                    whatsapp_url = f"https://wa.me/{clean_phone}"
                    webbrowser.open(whatsapp_url)

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في فتح واتساب: {str(e)}")

    def send_whatsapp_message(self):
        """إرسال رسالة واتساب من حقل النص"""
        try:
            if not self.supplier.phone:
                show_supplier_advanced_error(self, "خطأ", "لا توجد أرقام هاتف مسجلة لهذا المورد")
                return

            # الحصول على أول رقم هاتف
            phone_numbers = [phone.strip() for phone in self.supplier.phone.split(",") if phone.strip()]
            if not phone_numbers:
                show_supplier_advanced_error(self, "خطأ", "لا توجد أرقام هاتف صحيحة")
                return

            phone_number = phone_numbers[0]

            # الحصول على نص الرسالة من حقل النص
            message = self.message_text.toPlainText().strip()
            if not message:
                show_supplier_advanced_warning(self, "تنبيه", "يرجى كتابة نص الرسالة أولاً")
                return

            # معالجة رقم الهاتف

            # تنظيف رقم الهاتف مع معالجة محسنة
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # معالجة محسنة للأرقام المصرية
            if clean_phone.startswith('0') and len(clean_phone) == 11:
                clean_phone = '2' + clean_phone
            elif clean_phone.startswith('1') and len(clean_phone) == 10:
                clean_phone = '20' + clean_phone
            elif not clean_phone.startswith('20') and len(clean_phone) >= 10:
                clean_phone = '20' + clean_phone.lstrip('0')

            # التحقق النهائي من صحة الرقم
            if len(clean_phone) < 12 or not clean_phone.startswith('20'):
                original_clean = ''.join(filter(str.isdigit, phone_number))
                clean_phone = '20' + original_clean.lstrip('0')

            # ترميز الرسالة
            encoded_message = urllib.parse.quote(message)

            # رابط واتساب مع الرسالة
            whatsapp_url = f"https://wa.me/{clean_phone}?text={encoded_message}"
            webbrowser.open(whatsapp_url)

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في إرسال الرسالة: {str(e)}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق تماماً للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم متطور مطابق للعملاء
                colors = {
                    'emerald': '#10b981',
                    'info': '#3b82f6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444',
                    'secondary': '#6b7280',
                    'cyan': '#06b6d4'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")


class SupplierDocumentsDialog(QDialog):
    """نافذة إدارة وثائق المورد - مطابقة لنافذة وثائق العملاء"""

    def __init__(self, parent=None, supplier=None, session=None):
        super().__init__(parent)
        self.supplier = supplier
        self.session = session
        self.parent_widget = parent
        self.documents = []  # قائمة الوثائق المحلية
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        supplier_name = self.supplier.name if self.supplier else "مورد"
        self.setWindowTitle(f"📁 إدارة وثائق المورد - {supplier_name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(750, 420)  # نفس حجم نافذة العملاء

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للنمط الموحد
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط النافذة (مضغوط جداً لتوفير المساحة)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # تقليل الهوامش أكثر
        layout.setSpacing(8)  # تقليل المسافات أكثر

        # العنوان الرئيسي المطور بدون إطار (مثل نافذة الإحصائيات)
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 5px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(5)  # تقليل المسافة

        # الأيقونة والعنوان الرئيسي (مضغوط)
        main_title = QLabel("📁 إدارة وثائق المورد")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 5px;
            }
        """)

        # العنوان الفرعي التوضيحي (مضغوط)
        subtitle = QLabel(f"إدارة شاملة لوثائق وملفات المورد: {supplier_name}")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 2px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # إطار معلومات المورد
        supplier_info_frame = QFrame()
        supplier_info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        supplier_info_layout = QVBoxLayout(supplier_info_frame)
        supplier_info_layout.setContentsMargins(10, 10, 10, 10)

        supplier_info = QLabel(f"""
📊 معلومات المورد:
• الاسم: {self.supplier.name}
• الهاتف: {self.supplier.phone or 'غير محدد'}
• البريد الإلكتروني: {self.supplier.email or 'غير محدد'}
• العنوان: {self.supplier.address or 'غير محدد'}
• الرصيد: {format_currency(self.supplier.balance)}
        """)
        supplier_info.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 15px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                line-height: 1.6;
                padding: 5px;
                background: transparent;
            }
        """)
        supplier_info_layout.addWidget(supplier_info)
        layout.addWidget(supplier_info_frame)

        documents_label = QLabel("📄 الوثائق والملفات:")
        documents_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                margin-bottom: 10px;
                padding: 10px;
                background: transparent;
            }
        """)
        layout.addWidget(documents_label)

        self.documents_list = QListWidget()
        self.documents_list.setMinimumHeight(150)  # تقليل الحد الأدنى للارتفاع أكثر
        self.documents_list.setStyleSheet("""
            QListWidget {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 8px;
                font-size: 14px;
                color: #ffffff;
                min-height: 150px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                margin: 3px;
                background: transparent;
                min-height: 25px;
            }
            QListWidget::item:selected {
                background: rgba(59, 130, 246, 0.3);
                border: 2px solid rgba(59, 130, 246, 0.6);
            }
            QListWidget::item:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        """)
        layout.addWidget(self.documents_list, 1)  # إعطاء أولوية التمدد للقائمة

        # إطار واحد لجميع الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                padding: 15px;
                margin: 5px;
            }
            QFrame:hover {
                background: rgba(255, 255, 255, 0.15);
                border: 2px solid rgba(255, 255, 255, 0.4);
            }
        """)
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # الصف الأول - أزرار الإضافة
        add_buttons_layout = QHBoxLayout()
        add_buttons_layout.setSpacing(10)

        # زر إضافة بطاقة هوية
        id_card_button = QPushButton("🆔 إضافة بطاقة هوية")
        id_card_button.clicked.connect(lambda: self.add_document("بطاقة هوية"))
        id_card_button.setMinimumHeight(45)
        self.style_advanced_button(id_card_button, 'emerald')

        # زر إضافة عقد
        contract_button = QPushButton("📋 إضافة عقد")
        contract_button.clicked.connect(lambda: self.add_document("عقد"))
        contract_button.setMinimumHeight(45)
        self.style_advanced_button(contract_button, 'info')

        # زر إضافة وثيقة أخرى
        other_doc_button = QPushButton("📄 إضافة وثيقة")
        other_doc_button.clicked.connect(lambda: self.add_document("وثيقة"))
        other_doc_button.setMinimumHeight(45)
        self.style_advanced_button(other_doc_button, 'emerald')

        add_buttons_layout.addWidget(id_card_button)
        add_buttons_layout.addWidget(contract_button)
        add_buttons_layout.addWidget(other_doc_button)

        # الصف الثاني - أزرار الإجراءات
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setSpacing(10)

        # زر عرض الوثيقة
        view_button = QPushButton("👁️ عرض الوثيقة")
        view_button.clicked.connect(self.view_document)
        view_button.setMinimumHeight(45)
        self.style_advanced_button(view_button, 'cyan')

        # زر حذف الوثيقة
        delete_button = QPushButton("🗑️ حذف الوثيقة")
        delete_button.clicked.connect(self.delete_document)
        delete_button.setMinimumHeight(45)
        self.style_advanced_button(delete_button, 'danger')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        action_buttons_layout.addWidget(view_button)
        action_buttons_layout.addWidget(delete_button)
        action_buttons_layout.addWidget(close_button)

        buttons_layout.addLayout(add_buttons_layout)
        buttons_layout.addLayout(action_buttons_layout)

        layout.addWidget(buttons_frame)

        # تحميل الوثائق الموجودة
        self.load_documents()

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق تماماً للعملاء والعمال"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم متطور مطابق للعملاء والعمال
                colors = {
                    'emerald': '#10b981',
                    'info': '#3b82f6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444',
                    'secondary': '#6b7280',
                    'cyan': '#06b6d4'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def load_documents(self):
        """تحميل الوثائق من قاعدة البيانات - مطابق للعملاء"""
        try:
            self.documents_list.clear()
            self.documents = []  # مسح القائمة المحلية

            # تحميل الوثائق من قاعدة البيانات
            db_documents = self.session.query(Document).filter(
                Document.supplier_id == self.supplier.id
            ).order_by(Document.upload_date.desc()).all()

            if not db_documents:
                item = QListWidgetItem("📝 لا توجد وثائق مضافة بعد")
                item.setData(Qt.UserRole, None)
                self.documents_list.addItem(item)
                return

            # إضافة الوثائق للقائمة
            for doc in db_documents:
                # تحويل الوثيقة لقاموس للتوافق
                document = {
                    'id': doc.id,
                    'title': doc.title,
                    'description': doc.description,
                    'file_path': doc.file_path,
                    'type': doc.file_type,
                    'supplier_id': doc.supplier_id,
                    'created_at': doc.upload_date
                }
                self.documents.append(document)

                # إنشاء عنصر القائمة
                display_text = f"📄 {doc.title} ({doc.file_type})"
                if doc.description:
                    display_text += f" - {doc.description[:50]}..."

                item = QListWidgetItem(display_text)
                item.setData(Qt.UserRole, document)
                self.documents_list.addItem(item)

        except Exception as e:
            print(f"خطأ في تحميل الوثائق: {e}")
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في تحميل الوثائق: {str(e)}")

    def add_document(self, doc_type):
        """إضافة وثيقة جديدة - مطابق للعملاء"""
        try:

            file_path, _ = QFileDialog.getOpenFileName(
                self, f"اختر {doc_type}", "",
                "جميع الملفات (*.*);;ملفات PDF (*.pdf);;ملفات الصور (*.jpg *.jpeg *.png);;ملفات Word (*.doc *.docx)"
            )

            if file_path:
                # إنشاء عنوان تلقائي من اسم الملف
                file_name = os.path.basename(file_path)
                title = f"{doc_type} - {file_name}"
                description = f"وثيقة {doc_type} للمورد {self.supplier.name}"

                # حفظ الوثيقة في قاعدة البيانات
                try:
                    # إنشاء وثيقة جديدة
                    new_document = Document(
                        title=title,
                        description=description,
                        file_path=file_path,
                        file_type=doc_type,
                        supplier_id=self.supplier.id
                    )

                    # حفظ في قاعدة البيانات
                    self.session.add(new_document)
                    self.session.commit()

                    show_supplier_advanced_info(self, "تم الحفظ", f"تم حفظ {doc_type} بنجاح")
                    self.load_documents()  # إعادة تحميل القائمة

                except Exception as db_error:
                    self.session.rollback()
                    show_supplier_advanced_error(self, "خطأ في الحفظ",
                        f"حدث خطأ في حفظ الوثيقة:\n{str(db_error)}")
                    return

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في إضافة الوثيقة: {str(e)}")

    def view_document(self):
        """عرض الوثيقة المحددة - مطابق للعملاء"""
        try:
            current_item = self.documents_list.currentItem()
            if not current_item:
                show_supplier_advanced_error(self, "خطأ", "الرجاء اختيار وثيقة من القائمة")
                return

            document = current_item.data(Qt.UserRole)
            if not document:
                show_supplier_advanced_error(self, "خطأ", "لا توجد وثيقة محددة")
                return

            # فتح الملف باستخدام البرنامج الافتراضي

            file_path = document['file_path']
            if not os.path.exists(file_path):
                show_supplier_advanced_error(self, "خطأ", f"الملف غير موجود:\n{file_path}")
                return

            # فتح الملف حسب نظام التشغيل
            if platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', file_path))
            elif platform.system() == 'Windows':  # Windows
                os.startfile(file_path)
            else:  # Linux
                subprocess.call(('xdg-open', file_path))

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في عرض الوثيقة: {str(e)}")

    def delete_document(self):
        """حذف الوثيقة المحددة - مطابق للعملاء"""
        try:
            current_item = self.documents_list.currentItem()
            if not current_item:
                show_supplier_advanced_error(self, "خطأ", "الرجاء اختيار وثيقة من القائمة")
                return

            document = current_item.data(Qt.UserRole)
            if not document:
                show_supplier_advanced_error(self, "خطأ", "لا توجد وثيقة محددة")
                return

            # تأكيد الحذف
            reply = show_supplier_advanced_confirmation(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الوثيقة:\n{document['title']}؟"
            )

            if reply:
                try:
                    # حذف من قاعدة البيانات
                    db_document = self.session.query(Document).filter(
                        Document.id == document['id']
                    ).first()

                    if db_document:
                        self.session.delete(db_document)
                        self.session.commit()

                    # حذف من القائمة المحلية
                    self.documents.remove(document)

                    show_supplier_advanced_info(self, "تم الحذف", "تم حذف الوثيقة بنجاح من قاعدة البيانات")
                    self.load_documents()  # إعادة تحميل القائمة

                except Exception as db_error:
                    self.session.rollback()
                    show_supplier_advanced_error(self, "خطأ في الحذف",
                        f"حدث خطأ في حذف الوثيقة من قاعدة البيانات:\n{str(db_error)}")
                    return

        except Exception as e:
            show_supplier_advanced_error(self, "خطأ", f"حدث خطأ في حذف الوثيقة: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")



