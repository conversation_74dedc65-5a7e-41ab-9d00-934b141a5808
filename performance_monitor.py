# -*- coding: utf-8 -*-
"""
مراقب الأداء
Performance Monitor Module
"""

import time
import psutil
import threading
from datetime import datetime, timedelta
from collections import deque
import json
from pathlib import Path

class PerformanceMonitor:
    """مراقب الأداء والموارد"""
    
    def __init__(self):
        self.data_dir = Path("data/performance")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.performance_file = self.data_dir / "performance.json"
        
        # قوائم لتخزين بيانات الأداء
        self.cpu_usage = deque(maxlen=100)
        self.memory_usage = deque(maxlen=100)
        self.disk_usage = deque(maxlen=100)
        self.response_times = deque(maxlen=100)
        
        # إعدادات المراقبة
        self.monitoring_active = False
        self.monitoring_interval = 5  # ثواني
        self.monitoring_thread = None
        
        # إحصائيات العمليات
        self.operation_stats = {}
        
        self.load_performance_data()
    
    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        try:
            if not self.monitoring_active:
                self.monitoring_active = True
                self.monitoring_thread = threading.Thread(target=self._monitor_loop, daemon=True)
                self.monitoring_thread.start()
                print("✅ تم بدء مراقبة الأداء")
                return True
        except Exception as e:
            print(f"خطأ في بدء مراقبة الأداء: {e}")
            return False
    
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        try:
            self.monitoring_active = False
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=2)
            print("⏹️ تم إيقاف مراقبة الأداء")
            return True
        except Exception as e:
            print(f"خطأ في إيقاف مراقبة الأداء: {e}")
            return False
    
    def _monitor_loop(self):
        """حلقة مراقبة الأداء"""
        while self.monitoring_active:
            try:
                # مراقبة استخدام المعالج
                cpu_percent = psutil.cpu_percent(interval=1)
                self.cpu_usage.append({
                    'timestamp': datetime.now().isoformat(),
                    'value': cpu_percent
                })
                
                # مراقبة استخدام الذاكرة
                memory = psutil.virtual_memory()
                self.memory_usage.append({
                    'timestamp': datetime.now().isoformat(),
                    'value': memory.percent,
                    'used': memory.used,
                    'available': memory.available
                })
                
                # مراقبة استخدام القرص
                disk = psutil.disk_usage('/')
                self.disk_usage.append({
                    'timestamp': datetime.now().isoformat(),
                    'value': (disk.used / disk.total) * 100,
                    'used': disk.used,
                    'free': disk.free,
                    'total': disk.total
                })
                
                # حفظ البيانات كل 10 دورات
                if len(self.cpu_usage) % 10 == 0:
                    self.save_performance_data()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                print(f"خطأ في مراقبة الأداء: {e}")
                time.sleep(self.monitoring_interval)
    
    def measure_operation_time(self, operation_name):
        """مُزخرف لقياس وقت العمليات"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    end_time = time.time()
                    duration = end_time - start_time
                    
                    self.record_operation(operation_name, duration, True)
                    return result
                except Exception as e:
                    end_time = time.time()
                    duration = end_time - start_time
                    self.record_operation(operation_name, duration, False)
                    raise
            return wrapper
        return decorator
    
    def record_operation(self, operation_name, duration, success=True):
        """تسجيل عملية وأداؤها"""
        try:
            if operation_name not in self.operation_stats:
                self.operation_stats[operation_name] = {
                    'total_calls': 0,
                    'successful_calls': 0,
                    'failed_calls': 0,
                    'total_time': 0,
                    'min_time': float('inf'),
                    'max_time': 0,
                    'avg_time': 0,
                    'recent_times': deque(maxlen=50)
                }
            
            stats = self.operation_stats[operation_name]
            stats['total_calls'] += 1
            stats['total_time'] += duration
            stats['recent_times'].append(duration)
            
            if success:
                stats['successful_calls'] += 1
            else:
                stats['failed_calls'] += 1
            
            # تحديث الإحصائيات
            stats['min_time'] = min(stats['min_time'], duration)
            stats['max_time'] = max(stats['max_time'], duration)
            stats['avg_time'] = stats['total_time'] / stats['total_calls']
            
            # تسجيل وقت الاستجابة
            self.response_times.append({
                'timestamp': datetime.now().isoformat(),
                'operation': operation_name,
                'duration': duration,
                'success': success
            })
            
        except Exception as e:
            print(f"خطأ في تسجيل العملية: {e}")
    
    def get_current_system_info(self):
        """الحصول على معلومات النظام الحالية"""
        try:
            return {
                'cpu': {
                    'percent': psutil.cpu_percent(interval=1),
                    'count': psutil.cpu_count(),
                    'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                'memory': psutil.virtual_memory()._asdict(),
                'disk': psutil.disk_usage('/')._asdict(),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                'processes': len(psutil.pids())
            }
        except Exception as e:
            print(f"خطأ في الحصول على معلومات النظام: {e}")
            return {}
    
    def get_performance_summary(self):
        """الحصول على ملخص الأداء"""
        try:
            summary = {
                'monitoring_active': self.monitoring_active,
                'data_points': {
                    'cpu': len(self.cpu_usage),
                    'memory': len(self.memory_usage),
                    'disk': len(self.disk_usage),
                    'response_times': len(self.response_times)
                },
                'operations': len(self.operation_stats),
                'system_info': self.get_current_system_info()
            }
            
            # إضافة إحصائيات العمليات
            if self.operation_stats:
                summary['top_operations'] = sorted(
                    self.operation_stats.items(),
                    key=lambda x: x[1]['total_calls'],
                    reverse=True
                )[:5]
            
            return summary
        except Exception as e:
            print(f"خطأ في الحصول على ملخص الأداء: {e}")
            return {}
    
    def get_operation_stats(self, operation_name=None):
        """الحصول على إحصائيات العمليات"""
        try:
            if operation_name:
                return self.operation_stats.get(operation_name, {})
            return self.operation_stats
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات العمليات: {e}")
            return {}
    
    def get_recent_performance_data(self, data_type='all', limit=50):
        """الحصول على بيانات الأداء الحديثة"""
        try:
            data = {}

            if data_type in ['all', 'cpu']:
                data['cpu'] = list(self.cpu_usage)[-limit:]

            if data_type in ['all', 'memory']:
                data['memory'] = list(self.memory_usage)[-limit:]

            if data_type in ['all', 'disk']:
                data['disk'] = list(self.disk_usage)[-limit:]

            if data_type in ['all', 'response_times']:
                data['response_times'] = list(self.response_times)[-limit:]

            return data
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الأداء: {e}")
            return {}

    def get_metrics(self):
        """الحصول على المقاييس الحالية - للتوافق مع الاختبار"""
        return self.get_current_system_info()

    def collect_metrics(self):
        """جمع المقاييس الحالية - للتوافق مع الاختبار"""
        return self.get_current_system_info()
    
    def clear_performance_data(self):
        """مسح بيانات الأداء"""
        try:
            self.cpu_usage.clear()
            self.memory_usage.clear()
            self.disk_usage.clear()
            self.response_times.clear()
            self.operation_stats.clear()
            return True
        except Exception as e:
            print(f"خطأ في مسح بيانات الأداء: {e}")
            return False
    
    def save_performance_data(self):
        """حفظ بيانات الأداء"""
        try:
            data = {
                'cpu_usage': list(self.cpu_usage),
                'memory_usage': list(self.memory_usage),
                'disk_usage': list(self.disk_usage),
                'response_times': list(self.response_times),
                'operation_stats': self.operation_stats,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.performance_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ بيانات الأداء: {e}")
            return False
    
    def load_performance_data(self):
        """تحميل بيانات الأداء"""
        try:
            if self.performance_file.exists():
                with open(self.performance_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # تحميل البيانات مع الحد الأقصى
                self.cpu_usage.extend(data.get('cpu_usage', [])[-100:])
                self.memory_usage.extend(data.get('memory_usage', [])[-100:])
                self.disk_usage.extend(data.get('disk_usage', [])[-100:])
                self.response_times.extend(data.get('response_times', [])[-100:])
                self.operation_stats = data.get('operation_stats', {})
                
                return True
        except Exception as e:
            print(f"خطأ في تحميل بيانات الأداء: {e}")
            return False

# إنشاء مثيل عام
performance_monitor = PerformanceMonitor()

# دوال مساعدة للاستخدام المباشر
def start_monitoring():
    """بدء مراقبة الأداء"""
    return performance_monitor.start_monitoring()

def stop_monitoring():
    """إيقاف مراقبة الأداء"""
    return performance_monitor.stop_monitoring()

def measure_operation_time(operation_name):
    """مُزخرف لقياس وقت العمليات"""
    return performance_monitor.measure_operation_time(operation_name)

def get_performance_summary():
    """الحصول على ملخص الأداء"""
    return performance_monitor.get_performance_summary()

def get_current_system_info():
    """الحصول على معلومات النظام الحالية"""
    return performance_monitor.get_current_system_info()

print("✅ تم تحميل مراقب الأداء بنجاح")
