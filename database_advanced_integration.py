# -*- coding: utf-8 -*-
"""
دمج النظام المتطور مع قاعدة البيانات
Advanced Database Integration
"""

import os
import time
from datetime import datetime, timedelta
from pathlib import Path
from sqlalchemy import create_engine, text, event
from sqlalchemy.orm import sessionmaker
from advanced_database_config import get_advanced_engine
from advanced_cache_system import get_cache_system, cached, db_cache_manager
from database import Base, get_session as get_old_session

class AdvancedDatabaseManager:
    """مدير قاعدة البيانات المتطور"""
    
    def __init__(self):
        self.engine = None
        self.Session = None
        self.cache = get_cache_system()
        self.performance_stats = {
            'query_count': 0,
            'cache_hits': 0,
            'avg_query_time': 0,
            'slow_queries': []
        }
        self.setup_advanced_engine()
    
    def setup_advanced_engine(self):
        """إعداد محرك قاعدة البيانات المتطور"""
        try:
            # استخدام المحرك المتطور
            self.engine = get_advanced_engine("sqlite_advanced")
            self.Session = sessionmaker(bind=self.engine)
            
            # إضافة مراقبة الأداء
            self.setup_performance_monitoring()
            
            print("✅ تم إعداد محرك قاعدة البيانات المتطور")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعداد المحرك المتطور: {e}")
            # العودة للمحرك القديم
            self.Session = sessionmaker(bind=get_old_session().bind)
            return False
    
    def setup_performance_monitoring(self):
        """إعداد مراقبة الأداء"""
        @event.listens_for(self.engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = time.time()
        
        @event.listens_for(self.engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            total_time = time.time() - context._query_start_time
            
            self.performance_stats['query_count'] += 1
            
            # تحديث متوسط وقت الاستعلام
            current_avg = self.performance_stats['avg_query_time']
            query_count = self.performance_stats['query_count']
            self.performance_stats['avg_query_time'] = (
                (current_avg * (query_count - 1) + total_time) / query_count
            )
            
            # تسجيل الاستعلامات البطيئة
            if total_time > 1.0:  # أكثر من ثانية
                self.performance_stats['slow_queries'].append({
                    'statement': statement[:200],  # أول 200 حرف
                    'time': total_time,
                    'timestamp': datetime.now().isoformat()
                })
                
                # الاحتفاظ بآخر 50 استعلام بطيء فقط
                if len(self.performance_stats['slow_queries']) > 50:
                    self.performance_stats['slow_queries'] = self.performance_stats['slow_queries'][-50:]
    
    def get_session(self):
        """الحصول على جلسة قاعدة بيانات محسنة"""
        session = self.Session()
        
        # إعدادات إضافية للجلسة
        try:
            session.execute(text("PRAGMA busy_timeout=30000"))
            session.execute(text("PRAGMA temp_store=MEMORY"))
        except:
            pass
        
        return session
    
    @cached(expiry=1800, key_prefix="dashboard")
    def get_dashboard_summary(self, user_id=None):
        """الحصول على ملخص لوحة المعلومات مع تخزين مؤقت"""
        session = self.get_session()
        try:
            # استعلامات محسنة للوحة المعلومات
            summary = {}
            
            # عدد العملاء
            result = session.execute(text("SELECT COUNT(*) FROM clients")).fetchone()
            summary['total_clients'] = result[0] if result else 0
            
            # عدد المشاريع
            result = session.execute(text("SELECT COUNT(*) FROM projects")).fetchone()
            summary['total_projects'] = result[0] if result else 0
            
            # إجمالي الإيرادات
            result = session.execute(text("SELECT COALESCE(SUM(amount), 0) FROM revenues")).fetchone()
            summary['total_revenues'] = result[0] if result else 0
            
            # إجمالي المصروفات
            result = session.execute(text("SELECT COALESCE(SUM(amount), 0) FROM expenses")).fetchone()
            summary['total_expenses'] = result[0] if result else 0
            
            # صافي الربح
            summary['net_profit'] = summary['total_revenues'] - summary['total_expenses']
            
            # المشاريع النشطة
            result = session.execute(text("SELECT COUNT(*) FROM projects WHERE status = 'active'")).fetchone()
            summary['active_projects'] = result[0] if result else 0
            
            # الفواتير المعلقة
            result = session.execute(text("SELECT COUNT(*) FROM invoices WHERE status = 'pending'")).fetchone()
            summary['pending_invoices'] = result[0] if result else 0
            
            summary['last_updated'] = datetime.now().isoformat()
            
            return summary
            
        except Exception as e:
            print(f"خطأ في الحصول على ملخص لوحة المعلومات: {e}")
            return {}
        finally:
            session.close()
    
    @cached(expiry=3600, key_prefix="financial_report")
    def get_financial_report(self, start_date, end_date):
        """تقرير مالي محسن مع تخزين مؤقت"""
        session = self.get_session()
        try:
            report = {
                'period': f"{start_date} to {end_date}",
                'generated_at': datetime.now().isoformat()
            }
            
            # الإيرادات حسب الفئة
            revenues_query = text("""
                SELECT category, COALESCE(SUM(amount), 0) as total
                FROM revenues 
                WHERE date BETWEEN :start_date AND :end_date
                GROUP BY category
                ORDER BY total DESC
            """)
            
            result = session.execute(revenues_query, {
                'start_date': start_date,
                'end_date': end_date
            }).fetchall()
            
            report['revenues_by_category'] = [
                {'category': row[0], 'amount': row[1]} for row in result
            ]
            
            # المصروفات حسب الفئة
            expenses_query = text("""
                SELECT category, COALESCE(SUM(amount), 0) as total
                FROM expenses 
                WHERE date BETWEEN :start_date AND :end_date
                GROUP BY category
                ORDER BY total DESC
            """)
            
            result = session.execute(expenses_query, {
                'start_date': start_date,
                'end_date': end_date
            }).fetchall()
            
            report['expenses_by_category'] = [
                {'category': row[0], 'amount': row[1]} for row in result
            ]
            
            # الإجماليات
            total_revenues = sum(item['amount'] for item in report['revenues_by_category'])
            total_expenses = sum(item['amount'] for item in report['expenses_by_category'])
            
            report['summary'] = {
                'total_revenues': total_revenues,
                'total_expenses': total_expenses,
                'net_profit': total_revenues - total_expenses,
                'profit_margin': (total_revenues - total_expenses) / total_revenues * 100 if total_revenues > 0 else 0
            }
            
            return report
            
        except Exception as e:
            print(f"خطأ في إنشاء التقرير المالي: {e}")
            return {}
        finally:
            session.close()
    
    @cached(expiry=900, key_prefix="project_stats")
    def get_project_statistics(self):
        """إحصائيات المشاريع مع تخزين مؤقت"""
        session = self.get_session()
        try:
            stats = {}
            
            # المشاريع حسب الحالة
            status_query = text("""
                SELECT status, COUNT(*) as count
                FROM projects
                GROUP BY status
            """)
            
            result = session.execute(status_query).fetchall()
            stats['by_status'] = {row[0]: row[1] for row in result}
            
            # متوسط قيمة المشاريع
            avg_query = text("SELECT AVG(budget) FROM projects WHERE budget > 0")
            result = session.execute(avg_query).fetchone()
            stats['average_budget'] = result[0] if result and result[0] else 0
            
            # أكبر المشاريع
            top_projects_query = text("""
                SELECT name, budget
                FROM projects
                WHERE budget > 0
                ORDER BY budget DESC
                LIMIT 5
            """)
            
            result = session.execute(top_projects_query).fetchall()
            stats['top_projects'] = [
                {'name': row[0], 'budget': row[1]} for row in result
            ]
            
            return stats
            
        except Exception as e:
            print(f"خطأ في إحصائيات المشاريع: {e}")
            return {}
        finally:
            session.close()
    
    def invalidate_cache(self, pattern=None):
        """إلغاء التخزين المؤقت"""
        if pattern:
            # إلغاء نمط معين
            if pattern == "dashboard":
                db_cache_manager.invalidate_dashboard_cache()
            elif pattern == "reports":
                db_cache_manager.invalidate_reports_cache()
        else:
            # إلغاء جميع التخزين المؤقت
            self.cache.clear_all()
    
    def get_performance_stats(self):
        """الحصول على إحصائيات الأداء"""
        cache_stats = self.cache.get_stats()
        
        return {
            'database': {
                'total_queries': self.performance_stats['query_count'],
                'avg_query_time': f"{self.performance_stats['avg_query_time']:.4f}s",
                'slow_queries_count': len(self.performance_stats['slow_queries']),
                'recent_slow_queries': self.performance_stats['slow_queries'][-5:]  # آخر 5
            },
            'cache': cache_stats
        }
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        session = self.get_session()
        try:
            optimization_commands = [
                "ANALYZE",
                "PRAGMA optimize",
                "PRAGMA incremental_vacuum"
            ]
            
            for command in optimization_commands:
                session.execute(text(command))
            
            session.commit()
            print("✅ تم تحسين قاعدة البيانات")
            
            # إلغاء التخزين المؤقت بعد التحسين
            self.invalidate_cache()
            
        except Exception as e:
            print(f"خطأ في تحسين قاعدة البيانات: {e}")
        finally:
            session.close()

# إنشاء مثيل مدير قاعدة البيانات المتطور
advanced_db_manager = AdvancedDatabaseManager()

def get_advanced_session():
    """الحصول على جلسة قاعدة بيانات متطورة"""
    return advanced_db_manager.get_session()

def get_dashboard_data(user_id=None):
    """الحصول على بيانات لوحة المعلومات"""
    return advanced_db_manager.get_dashboard_summary(user_id)

def get_financial_report_data(start_date, end_date):
    """الحصول على تقرير مالي"""
    return advanced_db_manager.get_financial_report(start_date, end_date)

def get_project_stats():
    """الحصول على إحصائيات المشاريع"""
    return advanced_db_manager.get_project_statistics()

def get_system_performance():
    """الحصول على أداء النظام"""
    return advanced_db_manager.get_performance_stats()

def optimize_system():
    """تحسين النظام"""
    return advanced_db_manager.optimize_database()

# تم إزالة دوال الاختبار
