# -*- coding: utf-8 -*-
"""
مدير الأمان
Security Manager Module
"""

import hashlib
import secrets
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
import bcrypt
from cryptography.fernet import Fernet

class SecurityManager:
    """مدير الأمان والتشفير"""
    
    def __init__(self):
        self.security_dir = Path("data/security")
        self.security_dir.mkdir(parents=True, exist_ok=True)
        self.key_file = self.security_dir / "encryption.key"
        self.sessions_file = self.security_dir / "sessions.json"
        self.audit_file = self.security_dir / "audit.log"
        self.load_or_create_key()
        self.sessions = self.load_sessions()
    
    def load_or_create_key(self):
        """تحميل أو إنشاء مفتاح التشفير"""
        try:
            if self.key_file.exists():
                with open(self.key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                self.encryption_key = Fernet.generate_key()
                with open(self.key_file, 'wb') as f:
                    f.write(self.encryption_key)
                # تأمين ملف المفتاح
                os.chmod(self.key_file, 0o600)
            
            self.cipher = Fernet(self.encryption_key)
        except Exception as e:
            print(f"خطأ في تحميل مفتاح التشفير: {e}")
            self.cipher = None
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        try:
            # إنشاء salt عشوائي
            salt = bcrypt.gensalt()
            # تشفير كلمة المرور
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            return hashed.decode('utf-8')
        except Exception as e:
            print(f"خطأ في تشفير كلمة المرور: {e}")
            return None
    
    def verify_password(self, password, hashed_password):
        """التحقق من كلمة المرور"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
        except Exception as e:
            print(f"خطأ في التحقق من كلمة المرور: {e}")
            return False
    
    def encrypt_data(self, data):
        """تشفير البيانات"""
        try:
            if self.cipher is None:
                return None
            
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self.cipher.encrypt(data)
            return encrypted_data
        except Exception as e:
            print(f"خطأ في تشفير البيانات: {e}")
            return None
    
    def decrypt_data(self, encrypted_data):
        """فك تشفير البيانات"""
        try:
            if self.cipher is None:
                return None
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            print(f"خطأ في فك تشفير البيانات: {e}")
            return None
    
    def generate_session_token(self):
        """إنشاء رمز جلسة آمن"""
        try:
            return secrets.token_urlsafe(32)
        except Exception as e:
            print(f"خطأ في إنشاء رمز الجلسة: {e}")
            return None
    
    def create_session(self, user_id, user_role="user", expires_in_hours=24):
        """إنشاء جلسة جديدة"""
        try:
            token = self.generate_session_token()
            if not token:
                return None
            
            session_data = {
                "user_id": user_id,
                "user_role": user_role,
                "created_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + timedelta(hours=expires_in_hours)).isoformat(),
                "is_active": True
            }
            
            self.sessions[token] = session_data
            self.save_sessions()
            self.log_security_event("session_created", f"User {user_id} session created")
            
            return token
        except Exception as e:
            print(f"خطأ في إنشاء الجلسة: {e}")
            return None
    
    def validate_session(self, token):
        """التحقق من صحة الجلسة"""
        try:
            if token not in self.sessions:
                return False, "الجلسة غير موجودة"
            
            session = self.sessions[token]
            
            if not session.get("is_active", False):
                return False, "الجلسة غير نشطة"
            
            expires_at = datetime.fromisoformat(session["expires_at"])
            if datetime.now() > expires_at:
                self.sessions[token]["is_active"] = False
                self.save_sessions()
                return False, "انتهت صلاحية الجلسة"
            
            return True, session
        except Exception as e:
            print(f"خطأ في التحقق من الجلسة: {e}")
            return False, "خطأ في التحقق من الجلسة"
    
    def invalidate_session(self, token):
        """إلغاء الجلسة"""
        try:
            if token in self.sessions:
                self.sessions[token]["is_active"] = False
                self.save_sessions()
                self.log_security_event("session_invalidated", f"Session {token[:8]}... invalidated")
                return True
            return False
        except Exception as e:
            print(f"خطأ في إلغاء الجلسة: {e}")
            return False
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        try:
            current_time = datetime.now()
            expired_sessions = []
            
            for token, session in self.sessions.items():
                expires_at = datetime.fromisoformat(session["expires_at"])
                if current_time > expires_at:
                    expired_sessions.append(token)
            
            for token in expired_sessions:
                del self.sessions[token]
            
            if expired_sessions:
                self.save_sessions()
                self.log_security_event("sessions_cleanup", f"Cleaned {len(expired_sessions)} expired sessions")
            
            return len(expired_sessions)
        except Exception as e:
            print(f"خطأ في تنظيف الجلسات: {e}")
            return 0
    
    def load_sessions(self):
        """تحميل الجلسات من الملف"""
        try:
            if self.sessions_file.exists():
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل الجلسات: {e}")
            return {}
    
    def save_sessions(self):
        """حفظ الجلسات في الملف"""
        try:
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump(self.sessions, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الجلسات: {e}")
            return False
    
    def log_security_event(self, event_type, description, user_id=None):
        """تسجيل حدث أمني"""
        try:
            event = {
                "timestamp": datetime.now().isoformat(),
                "event_type": event_type,
                "description": description,
                "user_id": user_id,
                "ip_address": "127.0.0.1"  # يمكن تحسينه لاحقاً
            }
            
            with open(self.audit_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(event, ensure_ascii=False) + '\n')
        except Exception as e:
            print(f"خطأ في تسجيل الحدث الأمني: {e}")
    
    def get_security_events(self, limit=100):
        """الحصول على الأحداث الأمنية"""
        try:
            events = []
            if self.audit_file.exists():
                with open(self.audit_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines[-limit:]:
                        try:
                            event = json.loads(line.strip())
                            events.append(event)
                        except json.JSONDecodeError:
                            continue
            return events
        except Exception as e:
            print(f"خطأ في قراءة الأحداث الأمنية: {e}")
            return []
    
    def generate_secure_backup_key(self):
        """إنشاء مفتاح آمن للنسخ الاحتياطية"""
        try:
            return Fernet.generate_key()
        except Exception as e:
            print(f"خطأ في إنشاء مفتاح النسخ الاحتياطية: {e}")
            return None
    
    def encrypt_backup(self, backup_data, backup_key=None):
        """تشفير النسخة الاحتياطية"""
        try:
            if backup_key is None:
                backup_key = self.generate_secure_backup_key()
            
            cipher = Fernet(backup_key)
            
            if isinstance(backup_data, str):
                backup_data = backup_data.encode('utf-8')
            
            encrypted_backup = cipher.encrypt(backup_data)
            return encrypted_backup, backup_key
        except Exception as e:
            print(f"خطأ في تشفير النسخة الاحتياطية: {e}")
            return None, None
    
    def decrypt_backup(self, encrypted_backup, backup_key):
        """فك تشفير النسخة الاحتياطية"""
        try:
            cipher = Fernet(backup_key)
            decrypted_backup = cipher.decrypt(encrypted_backup)
            return decrypted_backup
        except Exception as e:
            print(f"خطأ في فك تشفير النسخة الاحتياطية: {e}")
            return None

# إنشاء مثيل عام
security_manager = SecurityManager()

# دوال مساعدة للاستخدام المباشر
def hash_password(password):
    """تشفير كلمة المرور"""
    return security_manager.hash_password(password)

def verify_password(password, hashed_password):
    """التحقق من كلمة المرور"""
    return security_manager.verify_password(password, hashed_password)

def encrypt_data(data):
    """تشفير البيانات"""
    return security_manager.encrypt_data(data)

def decrypt_data(encrypted_data):
    """فك تشفير البيانات"""
    return security_manager.decrypt_data(encrypted_data)

def create_session(user_id, user_role="user", expires_in_hours=24):
    """إنشاء جلسة جديدة"""
    return security_manager.create_session(user_id, user_role, expires_in_hours)

def validate_session(token):
    """التحقق من صحة الجلسة"""
    return security_manager.validate_session(token)

def log_security_event(event_type, description, user_id=None):
    """تسجيل حدث أمني"""
    return security_manager.log_security_event(event_type, description, user_id)

print("✅ تم تحميل مدير الأمان بنجاح")
