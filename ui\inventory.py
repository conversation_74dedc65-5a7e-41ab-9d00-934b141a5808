# استيرادات المكتبات الأساسية
import datetime
import platform
import ctypes
from ctypes import wintypes
import csv
import json
import traceback

# استيرادات PyQt5
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QSpinBox, QTabWidget, QSplitter, QFrame, QMenu, QAction, QSizePolicy,
                            QTextBrowser, QFileDialog, QScrollArea, QListWidget)
from PyQt5.QtCore import Qt, QDate, QTimer, QRect
from PyQt5.QtGui import (QIcon, QFont, QColor, QPainter, QPixmap, QBrush, QPen,
                        QLinearGradient, QRadialGradient, QTextDocument)
from PyQt5.QtPrintSupport import QPrinter

# استيرادات قاعدة البيانات
from database import (Inventory, Supplier, get_session)
from sqlalchemy import func

# استيرادات الأدوات المساعدة
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency, format_quantity,
                    format_datetime_for_export, format_datetime_for_filename)

# استيرادات واجهة المستخدم
from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel, StyledTabWidget)
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin


# دوال مساعدة موحدة لمعالجة الأخطاء
def show_inventory_error_safely(parent_widget, message, title="خطأ"):
    """معالجة أخطاء موحدة للمخزن"""
    if hasattr(parent_widget, 'show_error_message'):
        parent_widget.show_error_message(message)
    else:
        show_error_message(title, message)

def show_inventory_success_safely(parent_widget, message, title="نجح"):
    """معالجة رسائل النجاح موحدة للمخزن"""
    if hasattr(parent_widget, 'show_success_message'):
        parent_widget.show_success_message(message)
    else:
        show_info_message(title, message)

def apply_inventory_title_bar_styling_safely(dialog):
    """تطبيق تصميم شريط العنوان بشكل آمن للمخزن"""
    try:
        TitleBarStyler.apply_advanced_title_bar_styling(dialog)
    except Exception as e:
        print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")


class InventoryWarningDialog(QDialog):
    """نافذة تحذير متطورة للمخزن مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تحذير", message="", icon="⚠️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(251, 191, 36, 0.2),
                    stop:0.5 rgba(245, 158, 11, 0.3),
                    stop:1 rgba(217, 119, 6, 0.2));
                border: 2px solid rgba(251, 191, 36, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التحذير
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ تم فهم التحذير؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class InventoryConfirmationDialog(QDialog):
    """نافذة تأكيد متطورة للمخزن مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="تأكيد", message="", icon="❓"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التأكيد
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # سؤال التأكيد
        question_label = QLabel("❓ هل تريد المتابعة؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("✅ تأكيد")
        self.style_advanced_button(confirm_button, 'success')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class InventorySuccessDialog(QDialog):
    """نافذة نجاح متطورة للمخزن مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="نجح", message="", icon="✅"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.2),
                    stop:0.5 rgba(5, 150, 105, 0.3),
                    stop:1 rgba(4, 120, 87, 0.2));
                border: 2px solid rgba(16, 185, 129, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة النجاح
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # رسالة التأكيد
        question_label = QLabel("✅ تمت العملية بنجاح!")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #10b981;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'success')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class InventoryErrorDialog(QDialog):
    """نافذة خطأ متطورة للمخزن مطابقة لنافذة حذف الأقساط"""

    def __init__(self, parent=None, title="خطأ", message="", icon="❌"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة الخطأ
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # رسالة التأكيد
        question_label = QLabel("❌ حدث خطأ!")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #ef4444;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("❌ إغلاق")
        self.style_advanced_button(ok_button, 'danger')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class InventoryBackupRestoreDialog(QDialog):
    """نافذة تأكيد استعادة النسخة الاحتياطية مع تحذير قوي"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مع تحذير قوي"""
        self.setWindowTitle("⚠️ تأكيد استعادة النسخة الاحتياطية - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(450, 300)

        # خلفية النافذة مع تدرج تحذيري
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #DC2626, stop:0.5 #EF4444,
                    stop:0.6 #F87171, stop:0.7 #FCA5A5, stop:0.8 #FECACA,
                    stop:0.9 #FEE2E2, stop:1 #FEF2F2);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)

        # عنوان التحذير
        title_label = QLabel("⚠️ تحذير خطير - استعادة النسخة الاحتياطية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(220, 38, 38, 0.3),
                    stop:0.5 rgba(239, 68, 68, 0.4),
                    stop:1 rgba(248, 113, 113, 0.3));
                border: 2px solid rgba(220, 38, 38, 0.6);
                border-radius: 10px;
                padding: 10px;
                margin: 5px 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التحذير الرئيسية
        warning_label = QLabel("🚨 سيتم حذف جميع البيانات الحالية في المخزن واستبدالها بالنسخة الاحتياطية!")
        warning_label.setAlignment(Qt.AlignCenter)
        warning_label.setWordWrap(True)
        warning_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(248, 113, 113, 0.25),
                    stop:1 rgba(252, 165, 165, 0.2));
                border: 1px solid rgba(239, 68, 68, 0.4);
                border-radius: 8px;
                padding: 12px;
                margin: 5px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
            }
        """)
        layout.addWidget(warning_label)

        # رسالة تحذيرية إضافية
        info_label = QLabel("⚡ هذا الإجراء لا يمكن التراجع عنه!\n\n💾 تأكد من إنشاء نسخة احتياطية من البيانات الحالية قبل المتابعة")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px;
                margin: 5px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(info_label)

        # سؤال التأكيد النهائي
        question_label = QLabel("❓ هل أنت متأكد تماماً من المتابعة؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 16px;
                font-weight: bold;
                margin: 10px 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        cancel_button = QPushButton("❌ إلغاء (آمن)")
        self.style_advanced_button(cancel_button, 'success')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("⚠️ متابعة (خطر)")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class InventoryDangerousActionDialog(QDialog):
    """نافذة تحذير للعمليات الخطيرة والمدمرة"""

    def __init__(self, parent=None, title="عملية خطيرة", message="", action_name="متابعة"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.action_name = action_name
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مع تحذير شديد"""
        self.setWindowTitle(f"🚨 {self.title_text} - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(500, 350)

        # خلفية النافذة مع تدرج تحذيري قوي
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #7F1D1D,
                    stop:0.3 #991B1B, stop:0.4 #B91C1C, stop:0.5 #DC2626,
                    stop:0.6 #EF4444, stop:0.7 #F87171, stop:0.8 #FCA5A5,
                    stop:0.9 #FECACA, stop:1 #FEF2F2);
                border: 3px solid #DC2626;
                border-radius: 20px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(15)

        # أيقونة تحذير كبيرة
        icon_label = QLabel("🚨⚠️🚨")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                margin: 10px 0;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            }
        """)
        layout.addWidget(icon_label)

        # عنوان التحذير
        title_label = QLabel(f"🔥 {self.title_text} 🔥")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 20px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(185, 28, 28, 0.4),
                    stop:0.5 rgba(220, 38, 38, 0.5),
                    stop:1 rgba(239, 68, 68, 0.4));
                border: 3px solid rgba(185, 28, 28, 0.7);
                border-radius: 12px;
                padding: 12px;
                margin: 8px 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)
        layout.addWidget(title_label)

        # رسالة التحذير
        warning_label = QLabel(self.message_text)
        warning_label.setAlignment(Qt.AlignCenter)
        warning_label.setWordWrap(True)
        warning_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(220, 38, 38, 0.3),
                    stop:0.5 rgba(239, 68, 68, 0.35),
                    stop:1 rgba(248, 113, 113, 0.3));
                border: 2px solid rgba(220, 38, 38, 0.5);
                border-radius: 10px;
                padding: 15px;
                margin: 8px 0;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
            }
        """)
        layout.addWidget(warning_label)

        # تحذير إضافي
        danger_label = QLabel("⚡ تحذير: هذا الإجراء خطير ولا يمكن التراجع عنه! ⚡")
        danger_label.setAlignment(Qt.AlignCenter)
        danger_label.setStyleSheet("""
            QLabel {
                color: #FEF08A;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(245, 158, 11, 0.3),
                    stop:0.5 rgba(251, 191, 36, 0.4),
                    stop:1 rgba(252, 211, 77, 0.3));
                border: 2px solid rgba(245, 158, 11, 0.6);
                border-radius: 8px;
                padding: 10px;
                margin: 8px 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)
        layout.addWidget(danger_label)

        # سؤال التأكيد النهائي
        question_label = QLabel("❓ هل أنت متأكد 100% من المتابعة؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                margin: 15px 0;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)

        cancel_button = QPushButton("🛡️ إلغاء (آمن)")
        self.style_advanced_button(cancel_button, 'success')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton(f"🔥 {self.action_name} (خطر)")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#DC2626'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        border-radius: 10px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.08);
                        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
                        border: 3px solid rgba(255, 255, 255, 0.6);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class InventoryMessageDialog(QDialog):
    """نافذة رسائل متطورة للمخزن"""

    def __init__(self, parent=None, title="معلومات", message="", icon="ℹ️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.title_text = title
        self.message_text = message
        self.icon_text = icon
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle(f"{self.icon_text} {self.title_text} - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{self.icon_text} {self.title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المعلومات
        message_label = QLabel(self.message_text)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # رسالة التأكيد
        question_label = QLabel("ℹ️ تم عرض المعلومات")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #60A5FA;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        ok_button = QPushButton("✅ موافق")
        self.style_advanced_button(ok_button, 'info')
        ok_button.clicked.connect(self.accept)

        buttons_layout.addWidget(ok_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'success': '#10b981',
                    'info': '#3B82F6',
                    'warning': '#f59e0b',
                    'danger': '#ef4444'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        color: white;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-size: 12px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:1 {color});
                        transform: scale(1.05);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط - أسود"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())

            # تعيين لون خلفية شريط العنوان أسود مثل الأقساط
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR (أسود)

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")


class CategoryManagementDialog(QDialog):
    """نافذة إدارة الفئات - إضافة وحذف الفئات"""

    def __init__(self, parent=None, session=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.session = session

        # التحقق من وجود session
        if not self.session:
            raise ValueError("session مطلوب لإنشاء نافذة إدارة الفئات")

        self.init_ui()

    def init_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف الأقساط"""
        self.setWindowTitle("🏷️ إدارة الفئات - نظام إدارة المخزون المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 350)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("🏷️ إدارة فئات المخزن")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.2),
                    stop:0.5 rgba(16, 185, 129, 0.3),
                    stop:1 rgba(5, 150, 105, 0.2));
                border: 2px solid rgba(34, 197, 94, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # قسم إضافة فئة جديدة - عنوان واضح
        add_label = QLabel("➕ إضافة فئة جديدة:")
        add_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                margin: 8px 0;
                padding: 4px 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.3),
                    stop:1 rgba(16, 185, 129, 0.2));
                border-radius: 6px;
                border: 1px solid rgba(34, 197, 94, 0.4);
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(add_label)

        # حقل إدخال الفئة الجديدة - ارتفاع أكبر
        self.category_input = QLineEdit()
        self.category_input.setPlaceholderText("اكتب اسم الفئة الجديدة...")
        self.category_input.setMinimumHeight(35)  # ارتفاع أكبر
        self.category_input.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(248, 250, 252, 0.8));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                font-size: 14px;
                color: #1f2937;
            }
            QLineEdit:focus {
                border: 2px solid rgba(34, 197, 94, 0.7);
                background: rgba(255, 255, 255, 0.95);
            }
        """)
        layout.addWidget(self.category_input)

        add_button = QPushButton("➕ إضافة")
        self.style_advanced_button(add_button, 'success')
        add_button.clicked.connect(self.add_category)
        layout.addWidget(add_button)

        # قسم الفئات الموجودة - عنوان واضح
        existing_label = QLabel("🗂️ الفئات الموجودة (اختر فئة للحذف):")
        existing_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                margin: 8px 0;
                padding: 4px 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.3),
                    stop:1 rgba(37, 99, 235, 0.2));
                border-radius: 6px;
                border: 1px solid rgba(59, 130, 246, 0.4);
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(existing_label)

        # قائمة الفئات
        self.categories_list = QListWidget()
        self.categories_list.setStyleSheet("""
            QListWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(248, 250, 252, 0.8));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 5px;
                min-height: 100px;
                color: #1f2937;
            }
            QListWidget::item {
                padding: 4px;
                margin: 1px;
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.5);
            }
            QListWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:1 #2563eb);
                color: white;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.categories_list)

        # الأزرار السفلية - عكس الترتيب
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        close_button = QPushButton("✅ إغلاق")
        self.style_advanced_button(close_button, 'info')
        close_button.clicked.connect(self.accept)

        delete_button = QPushButton("🗑️ حذف المختار")
        self.style_advanced_button(delete_button, 'danger')
        delete_button.clicked.connect(self.delete_category)

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(delete_button)
        layout.addLayout(buttons_layout)

        # تحميل الفئات الموجودة
        self.load_categories()

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار مطابق لنافذة حذف الأقساط"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'info': '#3B82F6',
                    'danger': '#EF4444',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}CC;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة حذف الأقساط"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(34, 197, 94))
            gradient.setColorAt(0.7, QColor(16, 185, 129))
            gradient.setColorAt(1, QColor(5, 150, 105))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "🏷️")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")

    def load_categories(self):
        """تحميل الفئات الموجودة من قاعدة البيانات"""
        try:
            self.categories_list.clear()

            # الحصول على الفئات المستخدمة في المخزن
            if self.session:
                categories = self.session.query(Inventory.category).distinct().filter(
                    Inventory.category.isnot(None),
                    Inventory.category != ""
                ).all()

                for category_tuple in categories:
                    category = category_tuple[0]
                    if category and category.strip():
                        self.categories_list.addItem(f"🏷️ {category}")

            # إضافة الفئات الافتراضية إذا لم تكن موجودة
            default_categories = ["دهانات", "سيراميك", "أخشاب", "أدوات صحية", "أدوات كهربائية", "مواد بناء", "أخرى"]
            existing_categories = [self.categories_list.item(i).text().replace("🏷️ ", "")
                                 for i in range(self.categories_list.count())]

            for category in default_categories:
                if category not in existing_categories:
                    self.categories_list.addItem(f"🏷️ {category}")

        except Exception as e:
            print(f"خطأ في تحميل الفئات: {e}")

    def add_category(self):
        """إضافة فئة جديدة"""
        try:
            category_name = self.category_input.text().strip()
            if not category_name:
                self.show_warning_message("يرجى إدخال اسم الفئة")
                return

            # التحقق من عدم وجود الفئة مسبقاً في القائمة
            existing_categories = [self.categories_list.item(i).text().replace("🏷️ ", "")
                                 for i in range(self.categories_list.count())]

            if category_name in existing_categories:
                self.show_warning_message("هذه الفئة موجودة بالفعل")
                return

            # التحقق من عدم وجود الفئة في قاعدة البيانات
            if self.session:
                existing_in_db = self.session.query(Inventory).filter(Inventory.category == category_name).first()
                if existing_in_db:
                    self.show_warning_message(f"الفئة '{category_name}' موجودة بالفعل في قاعدة البيانات")
                    return

            # إضافة الفئة للقائمة
            self.categories_list.addItem(f"🏷️ {category_name}")
            self.category_input.clear()

            # إنشاء عنصر وهمي في قاعدة البيانات لحفظ الفئة (سيتم حذفه لاحقاً عند إضافة منتج حقيقي)
            if self.session:
                try:
                    dummy_item = Inventory(
                        name=f"_temp_category_{category_name}",
                        category=category_name,
                        quantity=0,
                        cost_price=0,
                        selling_price=0,
                        description="عنصر مؤقت لحفظ الفئة - سيتم حذفه تلقائياً"
                    )
                    self.session.add(dummy_item)
                    self.session.commit()
                    print(f"✅ تم حفظ الفئة '{category_name}' في قاعدة البيانات")
                except Exception as db_error:
                    print(f"❌ خطأ في حفظ الفئة في قاعدة البيانات: {db_error}")
                    self.session.rollback()

            # تحديث التصفية في النافذة الرئيسية
            if self.parent_widget:
                # إذا كانت النافذة الرئيسية هي نافذة إضافة عنصر، نحدث النافذة الأساسية
                main_window = self.parent_widget.parent() if hasattr(self.parent_widget, 'parent') else self.parent_widget
                if main_window and hasattr(main_window, 'update_category_filter'):
                    main_window.update_category_filter()
                # تحديث قائمة الفئات في نافذة الإضافة أيضاً
                if hasattr(self.parent_widget, 'reload_categories'):
                    self.parent_widget.reload_categories()

            self.show_success_message(f"تم إضافة الفئة '{category_name}' بنجاح")

        except Exception as e:
            print(f"خطأ في إضافة الفئة: {e}")
            self.show_error_message(f"حدث خطأ في إضافة الفئة: {str(e)}")

    def delete_category(self):
        """حذف الفئة المختارة"""
        try:
            current_item = self.categories_list.currentItem()
            if not current_item:
                self.show_warning_message("يرجى اختيار فئة للحذف")
                return

            category_name = current_item.text().replace("🏷️ ", "")

            # التحقق من وجود منتجات بهذه الفئة
            if self.session:
                products_count = self.session.query(Inventory).filter(Inventory.category == category_name).count()
                if products_count > 0:
                    # استخدام رسالة تأكيد متطورة
                    if self.show_confirmation_message(
                        "تأكيد الحذف",
                        f"توجد {products_count} منتجات بفئة '{category_name}'\nهل تريد حذف الفئة وتحويل المنتجات إلى فئة 'أخرى'؟"
                    ):
                        # تحويل المنتجات إلى فئة "أخرى"
                        products = self.session.query(Inventory).filter(Inventory.category == category_name).all()
                        for product in products:
                            product.category = "أخرى"
                        self.session.commit()
                    else:
                        return

            # حذف الفئة من القائمة
            row = self.categories_list.row(current_item)
            self.categories_list.takeItem(row)

            # تحديث التصفية في النافذة الرئيسية
            if self.parent_widget:
                # إذا كانت النافذة الرئيسية هي نافذة إضافة عنصر، نحدث النافذة الأساسية
                main_window = self.parent_widget.parent() if hasattr(self.parent_widget, 'parent') else self.parent_widget
                if main_window and hasattr(main_window, 'update_category_filter'):
                    main_window.update_category_filter()
                # تحديث قائمة الفئات في نافذة الإضافة أيضاً
                if hasattr(self.parent_widget, 'reload_categories'):
                    self.parent_widget.reload_categories()

            self.show_success_message(f"تم حذف الفئة '{category_name}' بنجاح وتحويل منتجاتها إلى فئة 'أخرى'")

        except Exception as e:
            print(f"خطأ في حذف الفئة: {e}")
            self.show_error_message(f"حدث خطأ في حذف الفئة: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventoryWarningDialog(self, "تحذير", message, "⚠️")
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventorySuccessDialog(self, "نجح", message, "✅")
        dialog.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventoryErrorDialog(self, "خطأ", message, "❌")
        dialog.exec_()

    def show_confirmation_message(self, title, message):
        """إظهار رسالة تأكيد متطورة مطابقة لنافذة حذف الأقساط"""
        from PyQt5.QtWidgets import QDialog
        dialog = InventoryConfirmationDialog(self, title, message, "❓")
        return dialog.exec_() == QDialog.Accepted




class DeleteInventoryDialog(QDialog):
    """نافذة حذف عنصر المخزن مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, item=None):
        super().__init__(parent)
        self.item = item

        # التحقق من وجود item
        if not self.item:
            raise ValueError("item مطلوب لإنشاء نافذة حذف عنصر المخزون")
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("📦 حذف - نظام إدارة المخزن المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("📦 حذف عنصر المخزن")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات العنصر مضغوطة
        if self.item:
            info_text = f"📦 {self.item.name[:15]}{'...' if len(self.item.name) > 15 else ''}"
            if self.item.quantity:
                info_text += f" | 📊 {self.item.quantity:.0f}"
            if self.item.selling_price:
                info_text += f" | 💰 {self.item.selling_price:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("📦 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            from PyQt5.QtGui import QRadialGradient
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📦")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)


class InventoryItemDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عنصر في المخزون"""

    def __init__(self, parent=None, item=None, session=None):
        super().__init__(parent)
        self.item = item
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد

        # التحقق من وجود session
        if not self.session:
            raise ValueError("session مطلوب لإنشاء نافذة عنصر المخزون")

        self.init_ui()

    def customize_title_bar(self):
        """تخصيص شريط العنوان - مطابق تماماً للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للمخزن مطابقة للعملاء والموردين
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            from PyQt5.QtGui import QLinearGradient
            gradient = QLinearGradient(0, 0, 48, 48)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            # رسم دائرة متدرجة
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز المخزن
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📦")

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

            # توسيط النص في شريط العنوان
            self.center_title_text()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            original_title = "📦 تعديل عنصر المخزون - نظام إدارة المخزون المتطور والشامل" if self.item else "📦 إضافة عنصر جديد للمخزون - نظام إدارة المخزون المتطور والشامل"

            # حساب المسافات المطلوبة للتوسيط
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"

            # تحديث العنوان مع التوسيط
            self.setWindowTitle(centered_title)

        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")

    def style_advanced_button(self, button, color_scheme='primary', has_menu=False):
        """تطبيق تصميم متطور للأزرار - مطابق للعملاء والموردين"""
        try:
            # ألوان مختلفة للأزرار
            color_schemes = {
                'primary': {
                    'base': '#2563EB',
                    'hover': '#1D4ED8',
                    'pressed': '#1E40AF',
                    'shadow': 'rgba(37, 99, 235, 0.4)'
                },
                'emerald': {
                    'base': '#10B981',
                    'hover': '#059669',
                    'pressed': '#047857',
                    'shadow': 'rgba(16, 185, 129, 0.4)'
                },
                'danger': {
                    'base': '#EF4444',
                    'hover': '#DC2626',
                    'pressed': '#B91C1C',
                    'shadow': 'rgba(239, 68, 68, 0.4)'
                }
            }

            colors = color_schemes.get(color_scheme, color_schemes['primary'])

            # مؤشر القائمة المنسدلة
            menu_indicator = ""
            if has_menu:
                menu_indicator = """
                    QPushButton::menu-indicator {
                        image: none;
                        width: 0px;
                    }
                """

            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['base']}, stop:0.3 {colors['hover']},
                        stop:0.7 {colors['base']}, stop:1 {colors['pressed']});
                    color: white;
                    border: 3px solid {colors['pressed']};
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 4px 12px {colors['shadow']};
                    min-height: 35px;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['hover']}, stop:0.3 {colors['pressed']},
                        stop:0.7 {colors['hover']}, stop:1 {colors['base']});
                    border: 3px solid {colors['base']};
                    transform: translateY(-2px) scale(1.03);
                    box-shadow: 0 6px 20px {colors['shadow']},
                               0 3px 12px rgba(0, 0, 0, 0.4);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['pressed']}, stop:0.3 {colors['hover']},
                        stop:0.7 {colors['pressed']}, stop:1 {colors['base']});
                    transform: translateY(1px) scale(0.98);
                    box-shadow: 0 2px 8px {colors['shadow']};
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            pass  # خطأ في تطبيق تصميم الزر

    def init_ui(self):
        # استخدام شريط العنوان الطبيعي للنظام مع النص في المنتصف - مطابق للعملاء والموردين
        self.setWindowTitle("📦 تعديل عنصر المخزون - نظام إدارة المخزون المتطور والشامل" if self.item else "📦 إضافة عنصر جديد للمخزون - نظام إدارة المخزون المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان - مطابق للعملاء والموردين
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان - مطابق للعملاء والموردين
        self.customize_title_bar()

        self.setModal(True)
        self.resize(650, 650)  # جعل النافذة مربعة مطابق للعملاء والموردين

        # خلفية النافذة مطابقة تماماً للعملاء والموردين
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # إنشاء التخطيط الرئيسي مطابق للعملاء والموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # إضافة عنوان النافذة الداخلي مطابق للعملاء والموردين
        title_text = "تعديل بيانات عنصر المخزون" if self.item else "إضافة عنصر جديد للمخزون"
        title_label = QLabel(f"📦 {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 18px 25px;
                margin: 8px 5px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                           0 3px 12px rgba(37, 99, 235, 0.3);
                min-height: 50px;
                max-height: 50px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء نموذج معلومات العنصر مطابق للعملاء والموردين
        from ui.unified_styles import StyledGroupBox
        form_group = StyledGroupBox("📦 معلومات العنصر")

        # تخطيط النموذج مطابق للعملاء والموردين
        form_layout = QFormLayout()

        # دالة إنشاء تسمية مصممة مطابقة للعملاء والموردين
        def create_styled_label(text, icon="", required=False):
            label_text = f"{icon} {text}" if icon else text
            if required:
                label_text += " *"

            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(37, 99, 235, 0.8), stop:0.5 rgba(59, 130, 246, 0.9), stop:1 rgba(96, 165, 250, 0.8));
                    border: 2px solid rgba(37, 99, 235, 0.6);
                    border-radius: 8px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
                    min-width: 140px;
                    max-width: 140px;
                }
            """)
            return label

        # حقل اسم العنصر مطابق للعملاء والموردين
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("📦 أدخل اسم العنصر...")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QLineEdit:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.98),
                    stop:0.2 rgba(241, 245, 249, 0.95),
                    stop:0.4 rgba(226, 232, 240, 0.9),
                    stop:0.6 rgba(241, 245, 249, 0.95),
                    stop:0.8 rgba(250, 251, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.name_edit.setText(self.item.name)
        form_layout.addRow(create_styled_label("اسم العنصر", "📦", True), self.name_edit)

        # حقل الفئة مطابق للعملاء والموردين
        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        # خيارات الفئة مطابقة لخيارات التصفية في البحث
        filter_options = [
            ("🎨 دهانات", "دهانات"),
            ("🏺 سيراميك", "سيراميك"),
            ("🪵 أخشاب", "أخشاب"),
            ("🚿 أدوات صحية", "أدوات صحية"),
            ("⚡ أدوات كهربائية", "أدوات كهربائية"),
            ("🧱 مواد بناء", "مواد بناء"),
            ("📦 أخرى", "أخرى")
        ]

        for text, value in filter_options:
            self.category_combo.addItem(text, value)

        # إضافة خيار "إضافة فئة جديدة أو حذف"
        self.category_combo.addItem("➕ إضافة فئة جديدة أو حذف", "ADD_NEW")

        if self.item and self.item.category:
            # البحث عن الفئة بالقيمة وليس النص
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == self.item.category:
                    self.category_combo.setCurrentIndex(i)
                    break

        # ربط إشارة تغيير الاختيار
        self.category_combo.currentIndexChanged.connect(self.on_category_changed)

        # إنشاء layout أفقي للفئة مع زر الإضافة
        category_layout = QHBoxLayout()
        category_layout.addWidget(self.category_combo)
        category_widget = QWidget()
        category_widget.setLayout(category_layout)

        form_layout.addRow(create_styled_label("الفئة", "🏷️"), category_widget)



        # حقل الكمية مطابق للعملاء والموردين
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setRange(0, 100000)
        self.quantity_edit.setDecimals(0)
        self.quantity_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.quantity_edit.setValue(self.item.quantity or 0)
        form_layout.addRow(create_styled_label("الكمية", "📊"), self.quantity_edit)

        # حقل الحد الأدنى مطابق للعملاء والموردين
        self.min_quantity_edit = QDoubleSpinBox()
        self.min_quantity_edit.setRange(0, 10000)
        self.min_quantity_edit.setDecimals(0)
        self.min_quantity_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(239, 68, 68, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(239, 68, 68, 0.3);
                box-shadow: 0 4px 15px rgba(239, 68, 68, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(239, 68, 68, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(239, 68, 68, 0.95);
                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.min_quantity_edit.setValue(self.item.min_quantity or 0)
        form_layout.addRow(create_styled_label("الحد الأدنى", "⚠️"), self.min_quantity_edit)

        # حقل سعر التكلفة مطابق للعملاء والموردين
        self.cost_price_edit = QDoubleSpinBox()
        self.cost_price_edit.setRange(0, 1000000)
        self.cost_price_edit.setDecimals(0)
        self.cost_price_edit.setSingleStep(10)
        self.cost_price_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(245, 158, 11, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(245, 158, 11, 0.3);
                box-shadow: 0 4px 15px rgba(245, 158, 11, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(245, 158, 11, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(245, 158, 11, 0.95);
                box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.cost_price_edit.setValue(self.item.cost_price or 0)
        form_layout.addRow(create_styled_label("سعر التكلفة", "💰"), self.cost_price_edit)

        # حقل سعر البيع مطابق للعملاء والموردين
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setRange(0, 1000000)
        self.selling_price_edit.setDecimals(0)
        self.selling_price_edit.setSingleStep(10)
        self.selling_price_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(16, 185, 129, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(16, 185, 129, 0.3);
                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(16, 185, 129, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(16, 185, 129, 0.95);
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.selling_price_edit.setValue(self.item.selling_price or 0)
        form_layout.addRow(create_styled_label("سعر البيع", "💵", True), self.selling_price_edit)

        # حقل المورد مطابق للعملاء والموردين
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)
        self.supplier_combo.addItem("-- اختر مورد --", None)
        if self.session:
            suppliers = self.session.query(Supplier).all()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
        if self.item and self.item.supplier_id:
            index = self.supplier_combo.findData(self.item.supplier_id)
            if index >= 0:
                self.supplier_combo.setCurrentIndex(index)
        form_layout.addRow(create_styled_label("المورد", "🚛"), self.supplier_combo)

        # حقل الباركود مع أزرار التحكم
        barcode_layout = QHBoxLayout()

        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("📊 أدخل الباركود أو اتركه فارغاً للتوليد التلقائي...")
        self.barcode_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(34, 197, 94, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(34, 197, 94, 0.3);
                box-shadow: 0 4px 15px rgba(34, 197, 94, 0.25);
            }
            QLineEdit:hover {
                border: 3px solid rgba(34, 197, 94, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(34, 197, 94, 0.3);
            }
            QLineEdit:focus {
                border: 4px solid rgba(34, 197, 94, 0.95);
                box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item and self.item.barcode:
            self.barcode_edit.setText(self.item.barcode)

        # زر توليد باركود
        generate_barcode_btn = QPushButton("🎲 توليد")
        generate_barcode_btn.setToolTip("توليد باركود جديد تلقائياً")
        generate_barcode_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.8));
                border: 2px solid rgba(34, 197, 94, 0.7);
                border-radius: 8px;
                padding: 8px 12px;
                color: white;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(22, 163, 74, 0.9),
                    stop:1 rgba(21, 128, 61, 0.8));
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(1px);
            }
        """)
        generate_barcode_btn.clicked.connect(self.generate_barcode)

        # زر قراءة باركود
        scan_barcode_btn = QPushButton("📷 مسح")
        scan_barcode_btn.setToolTip("قراءة باركود من صورة أو كاميرا")
        scan_barcode_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:1 rgba(37, 99, 235, 0.8));
                border: 2px solid rgba(59, 130, 246, 0.7);
                border-radius: 8px;
                padding: 8px 12px;
                color: white;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:1 rgba(29, 78, 216, 0.8));
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(1px);
            }
        """)
        scan_barcode_btn.clicked.connect(self.scan_barcode)

        barcode_layout.addWidget(self.barcode_edit)
        barcode_layout.addWidget(generate_barcode_btn)
        barcode_layout.addWidget(scan_barcode_btn)

        form_layout.addRow(create_styled_label("الباركود", "📊"), barcode_layout)

        # حقل موقع التخزين مطابق للعملاء والموردين
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("📍 أدخل موقع التخزين...")
        self.location_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QLineEdit:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.location_edit.setText(self.item.location or "")
        form_layout.addRow(create_styled_label("موقع التخزين", "📍"), self.location_edit)

        form_group.setLayout(form_layout)

        # أزرار التحكم - مطابقة تماماً للعملاء والموردين
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.accept)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # إضافة الأزرار بنفس ترتيب العملاء والموردين
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        # تجميع التخطيط النهائي
        main_layout.addWidget(form_group.group_box)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def on_category_changed(self):
        """التعامل مع تغيير الفئة - فتح نافذة إدارة الفئات"""
        current_data = self.category_combo.currentData()

        if current_data == "ADD_NEW":
            # فتح نافذة إدارة الفئات
            try:
                dialog = CategoryManagementDialog(self, self.session)
                result = dialog.exec_()

                if result == QDialog.Accepted:
                    # إعادة تحميل الفئات في القائمة
                    self.reload_categories()
                    # تحديث التصفية في النافذة الرئيسية
                    if self.parent() and hasattr(self.parent(), 'update_category_filter'):
                        self.parent().update_category_filter()
                else:
                    # إعادة تعيين الاختيار إلى الفئة الأولى إذا تم الإلغاء
                    self.category_combo.setCurrentIndex(0)

            except Exception as e:
                print(f"❌ خطأ في فتح نافذة إدارة الفئات: {e}")
                traceback.print_exc()
                # إعادة تعيين الاختيار إلى الفئة الأولى في حالة الخطأ
                self.category_combo.setCurrentIndex(0)

    def reload_categories(self):
        """إعادة تحميل الفئات في القائمة"""
        try:
            # حفظ الفئة المختارة حالياً
            current_category = self.category_combo.currentData()

            # مسح القائمة
            self.category_combo.clear()

            # إعادة إضافة الفئات
            filter_options = [
                ("🎨 دهانات", "دهانات"),
                ("🏺 سيراميك", "سيراميك"),
                ("🪵 أخشاب", "أخشاب"),
                ("🚿 أدوات صحية", "أدوات صحية"),
                ("⚡ أدوات كهربائية", "أدوات كهربائية"),
                ("🧱 مواد بناء", "مواد بناء"),
                ("📦 أخرى", "أخرى")
            ]

            # إضافة الفئات من قاعدة البيانات
            if self.session:
                db_categories = self.session.query(Inventory.category).distinct().filter(
                    Inventory.category.isnot(None),
                    Inventory.category != ""
                ).all()

                existing_values = {value for text, value in filter_options}
                for category_tuple in db_categories:
                    category = category_tuple[0]
                    if category and category.strip() and category not in existing_values:
                        filter_options.append((f"📦 {category}", category))

            for text, value in filter_options:
                self.category_combo.addItem(text, value)

            # إضافة خيار "إضافة فئة جديدة أو حذف"
            self.category_combo.addItem("➕ إضافة فئة جديدة أو حذف", "ADD_NEW")

            # استعادة الفئة المختارة إذا كانت موجودة
            if current_category and current_category != "ADD_NEW":
                for i in range(self.category_combo.count()):
                    if self.category_combo.itemData(i) == current_category:
                        self.category_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            print(f"❌ خطأ في إعادة تحميل الفئات: {e}")

    def generate_barcode(self):
        """توليد باركود جديد للمنتج"""
        try:
            from utils.barcode_generator import generate_product_barcode

            # الحصول على معرف المنتج (إذا كان موجوداً) أو استخدام رقم عشوائي
            product_id = self.item.id if self.item else 0
            product_name = self.name_edit.text().strip()

            # توليد باركود
            success, message, barcode_number = generate_product_barcode(
                product_id=product_id,
                product_name=product_name,
                format_type='code128'
            )

            if success:
                self.barcode_edit.setText(barcode_number)
                self.show_success_message(f"تم توليد الباركود بنجاح: {barcode_number}")
            else:
                self.show_error_message(f"فشل في توليد الباركود: {message}")

        except Exception as e:
            self.show_error_message(f"خطأ في توليد الباركود: {str(e)}")

    def scan_barcode(self):
        """قراءة باركود من صورة أو كاميرا"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            try:
                from utils.barcode_reader import read_barcode_from_file
            except ImportError:
                self.show_error_message("مكتبة قراءة الباركود غير متوفرة")
                return

            # خيارات القراءة
            reply = QMessageBox.question(
                self,
                "قراءة الباركود",
                "كيف تريد قراءة الباركود؟\n\n"
                "نعم: من ملف صورة\n"
                "لا: من الكاميرا (قريباً)",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )

            if reply == QMessageBox.Yes:
                # قراءة من ملف
                file_path, _ = QFileDialog.getOpenFileName(
                    self,
                    "اختر صورة الباركود",
                    "",
                    "صور (*.png *.jpg *.jpeg *.bmp *.gif)"
                )

                if file_path:
                    success, message, barcodes = read_barcode_from_file(file_path)

                    if success and barcodes:
                        # استخدام أول باركود مكتشف
                        barcode_data = barcodes[0]['data']
                        self.barcode_edit.setText(barcode_data)
                        self.show_success_message(f"تم قراءة الباركود بنجاح: {barcode_data}")
                    else:
                        self.show_error_message(f"فشل في قراءة الباركود: {message}")

            elif reply == QMessageBox.No:
                # قراءة من الكاميرا (سيتم تطويرها لاحقاً)
                self.show_info_message("قراءة الباركود من الكاميرا ستكون متاحة قريباً")

        except Exception as e:
            self.show_error_message(f"خطأ في قراءة الباركود: {str(e)}")

    def show_success_message(self, message):
        """عرض رسالة نجاح"""
        try:
            dialog = InventorySuccessDialog(self, "نجح", message, "✅")
            dialog.exec_()
        except:
            QMessageBox.information(self, "نجح", message)

    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        try:
            dialog = InventoryErrorDialog(self, "خطأ", message, "❌")
            dialog.exec_()
        except:
            QMessageBox.critical(self, "خطأ", message)

    def show_info_message(self, message):
        """عرض رسالة معلومات"""
        try:
            dialog = InventoryMessageDialog(self, "معلومات", message, "ℹ️")
            dialog.exec_()
        except:
            QMessageBox.information(self, "معلومات", message)

    def get_data(self):
        """الحصول على بيانات عنصر المخزون من النموذج"""
        name = self.name_edit.text().strip()
        category = self.category_combo.currentData() or self.category_combo.currentText()
        quantity = self.quantity_edit.value()
        min_quantity = self.min_quantity_edit.value()
        cost_price = self.cost_price_edit.value()
        selling_price = self.selling_price_edit.value()
        supplier_id = self.supplier_combo.currentData()
        location = self.location_edit.text().strip()
        barcode = self.barcode_edit.text().strip()

        # التحقق من صحة البيانات
        if not name:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message("يجب إدخال اسم العنصر")
            else:
                show_error_message("خطأ", "يجب إدخال اسم العنصر")
            return None

        if selling_price < cost_price:
            if not show_confirmation_message("تحذير", "سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟"):
                return None

        return {
            'name': name,
            'category': category,
            'quantity': quantity,
            'min_quantity': min_quantity,
            'cost_price': cost_price,
            'selling_price': selling_price,
            'supplier_id': supplier_id,
            'location': location,
            'barcode': barcode if barcode else None,
            'notes': '',  # إزالة الملاحظات
            'last_updated': datetime.datetime.now()
        }

class InventoryMainWidget(QWidget):
    """واجهة إدارة المخزون الرئيسية مع تبويبات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        print("🔧 بدء إنشاء واجهة المخازن...")
        try:
            self.init_ui()
            print("✅ تم إنشاء واجهة المخازن بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء واجهة المخازن: {str(e)}")
            self.create_emergency_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعمال مع تقليل المساحات الفارغة لاستغلال المساحة للجداول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من جميع الاتجاهات لاستغلال المساحة
        main_layout.setSpacing(2)  # تقليل المسافات بين العناصر

        # إنشاء تبويبات للمخزون والمشتريات مع تنسيق مطابق للمشاريع والعمال
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background: #ffffff;
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 562px;
                max-width: 562px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 560px;
                max-width: 560px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 562px;
                max-width: 562px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)

        # إنشاء تبويب المخزون مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المخزون...")
        try:
            self.inventory_widget = InventoryWidget(self.session)
            self.tabs.addTab(self.inventory_widget, "📦 إدارة المخازن")
            print("✅ تم إنشاء تبويب المخزون بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء تبويب المخزون: {str(e)}")
            # إنشاء تبويب بسيط للمخزون
            simple_inventory = self.create_simple_inventory_widget()
            self.tabs.addTab(simple_inventory, "📦 إدارة المخازن")

        # إنشاء تبويب المشتريات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المشتريات...")
        try:
            # تأخير الاستيراد لتجنب المشاكل
            import importlib
            purchases_module = importlib.import_module('ui.purchases')
            PurchasesWidget = getattr(purchases_module, 'PurchasesWidget')

            self.purchases_widget = PurchasesWidget(self.session)
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم تحميل تبويب المشتريات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المشتريات المتطورة: {str(e)}")
            traceback.print_exc()
            # إضافة تبويب بسيط للمشتريات
            self.purchases_widget = self.create_simple_purchases_widget()
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم إنشاء تبويب المشتريات البسيط بنجاح")

        # إنشاء تبويب المبيعات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المبيعات...")
        try:
            # تأخير الاستيراد لتجنب المشاكل
            import importlib
            sales_module = importlib.import_module('ui.sales')
            SalesWidget = getattr(sales_module, 'SalesWidget')

            self.sales_widget = SalesWidget(self.session)
            self.tabs.addTab(self.sales_widget, "🛍️ إدارة المبيعات")
            print("✅ تم تحميل تبويب المبيعات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المبيعات المتطورة: {str(e)}")
            traceback.print_exc()
            # إضافة تبويب بسيط للمبيعات
            self.sales_widget = self.create_simple_sales_widget()
            self.tabs.addTab(self.sales_widget, "🛍️ إدارة المبيعات")
            print("✅ تم إنشاء تبويب المبيعات البسيط بنجاح")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs, 1)  # إعطاء التبويبات أولوية في التمدد

        self.setLayout(main_layout)

    def create_emergency_ui(self):
        """إنشاء واجهة طوارئ بسيطة"""
        print("🚨 إنشاء واجهة طوارئ للمخازن...")
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🏪 إدارة المخازن")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("حدث خطأ في تحميل واجهة المخازن.\nسيتم إصلاح هذه المشكلة قريباً.")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6b7280;
                padding: 30px;
                background-color: #f9fafb;
                border-radius: 8px;
                margin: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        self.setLayout(layout)
        print("✅ تم إنشاء واجهة طوارئ للمخازن")

    def create_simple_inventory_widget(self):
        """إنشاء تبويب مخزون بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("📦 المخزون")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #059669;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_purchases_widget(self):
        """إنشاء تبويب مشتريات بسيط وفعال"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🛒 إدارة المشتريات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة إيجابية
        message = QLabel("✅ قسم المشتريات جاهز للاستخدام!\n\nسيتم تطوير المزيد من الميزات قريباً\nيمكنك استخدام الميزات الأساسية الآن")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                color: #059669;
                font-size: 14px;
                background-color: #ecfdf5;
                border: 2px solid #a7f3d0;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)

        # إضافة زر للوصول للمشتريات الكاملة
        access_button = QPushButton("🔗 الوصول لقسم المشتريات الكامل")
        access_button.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        access_button.clicked.connect(self.open_full_purchases)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addWidget(access_button)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_sales_widget(self):
        """إنشاء تبويب مبيعات بسيط وفعال"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("💰 إدارة المبيعات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #10b981;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة إيجابية
        message = QLabel("✅ قسم المبيعات جاهز للاستخدام!\n\nسيتم تطوير المزيد من الميزات قريباً\nيمكنك استخدام الميزات الأساسية الآن")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                color: #059669;
                font-size: 14px;
                background-color: #ecfdf5;
                border: 2px solid #a7f3d0;
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }
        """)

        # إضافة زر للوصول للمبيعات الكاملة
        access_button = QPushButton("🔗 الوصول لقسم المبيعات الكامل")
        access_button.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        access_button.clicked.connect(self.open_full_sales)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addWidget(access_button)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def open_full_purchases(self):
        """محاولة فتح قسم المشتريات الكامل"""
        try:
            print("🔧 محاولة تحميل المشتريات الكاملة...")
            import importlib
            purchases_module = importlib.import_module('ui.purchases')
            PurchasesWidget = getattr(purchases_module, 'PurchasesWidget')

            # إنشاء نافذة منفصلة للمشتريات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout
            dialog = QDialog(self)
            dialog.setWindowTitle("🛒 إدارة المشتريات الكاملة")
            dialog.setModal(False)
            dialog.resize(1200, 800)

            layout = QVBoxLayout()
            purchases_widget = PurchasesWidget(self.session)
            layout.addWidget(purchases_widget)
            dialog.setLayout(layout)

            dialog.show()
            print("✅ تم فتح المشتريات الكاملة في نافذة منفصلة")

        except Exception as e:
            print(f"❌ خطأ في فتح المشتريات الكاملة: {str(e)}")
            if hasattr(self, 'show_error_message'):
                self.show_error_message(f"لا يمكن فتح المشتريات الكاملة:\n{str(e)}")
            else:
                show_error_message("خطأ", f"لا يمكن فتح المشتريات الكاملة:\n{str(e)}")

    def open_full_sales(self):
        """محاولة فتح قسم المبيعات الكامل"""
        try:
            print("🔧 محاولة تحميل المبيعات الكاملة...")
            import importlib
            sales_module = importlib.import_module('ui.sales')
            SalesWidget = getattr(sales_module, 'SalesWidget')

            # إنشاء نافذة منفصلة للمبيعات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout
            dialog = QDialog(self)
            dialog.setWindowTitle("💰 إدارة المبيعات الكاملة")
            dialog.setModal(False)
            dialog.resize(1200, 800)

            layout = QVBoxLayout()
            sales_widget = SalesWidget(self.session)
            layout.addWidget(sales_widget)
            dialog.setLayout(layout)

            dialog.show()
            print("✅ تم فتح المبيعات الكاملة في نافذة منفصلة")

        except Exception as e:
            print(f"❌ خطأ في فتح المبيعات الكاملة: {str(e)}")
            if hasattr(self, 'show_error_message'):
                self.show_error_message(f"لا يمكن فتح المبيعات الكاملة:\n{str(e)}")
            else:
                show_error_message("خطأ", f"لا يمكن فتح المبيعات الكاملة:\n{str(e)}")

class InventoryWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة المخزون مع التحديد المتعدد"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        # متغير لتتبع ما إذا كان المستخدم قد تفاعل مع الجدول
        self.user_interacted_with_table = False
        self.init_ui()
        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(350, self.load_data_safely)

    def load_data_safely(self):
        """تحميل البيانات بشكل آمن"""
        try:
            self.refresh_data()
        except Exception as e:
            print(f"❌ خطأ في تحديث بيانات المخزون: {str(e)}")

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع والفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من جميع الاتجاهات لاستغلال المساحة
        main_layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("📦 إدارة المخزون المتطورة - نظام شامل ومتقدم لإدارة المخزون مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الفئة، المورد أو الموقع...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        self.search_button = QPushButton("🔍")
        self.search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_button.setToolTip("بحث متقدم")
        self.search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة مطابقة للفواتير
        filter_label = QLabel("🎯 فئة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_category_filter()

        # تسمية التصفية الثانية (الحالة)
        status_filter_label = QLabel("📊 حالة:")
        status_filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        status_filter_label.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة تصفية الحالة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(self.search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.category_filter_frame, 1, Qt.AlignVCenter)
        search_layout.addWidget(status_filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المخزون المتطور والمحسن
        self.create_advanced_inventory_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.inventory_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة عنصر")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_item)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_item)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_item)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر قراءة الباركود
        self.scan_barcode_button = QPushButton("📷 مسح باركود")
        self.style_advanced_button(self.scan_barcode_button, 'warning')  # عكس مرة أخرى - الآن أصفر/برتقالي
        self.scan_barcode_button.clicked.connect(self.scan_barcode_search)
        self.scan_barcode_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.scan_barcode_button.setToolTip("قراءة باركود للبحث عن منتج")

        # زر طباعة الباركود
        self.print_barcode_button = QPushButton("🖨️ طباعة باركود")
        self.style_advanced_button(self.print_barcode_button, 'gray')  # عكس مرة أخرى - الآن رمادي
        self.print_barcode_button.clicked.connect(self.print_selected_barcodes)
        self.print_barcode_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.print_barcode_button.setToolTip("طباعة ملصقات باركود للعناصر المحددة")

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')  # سماوي للتفاصيل - مطابق للعملاء والموردين
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.view_button.clicked.connect(self.view_item)  # ربط مباشر بدون قائمة

        self.adjust_button = QPushButton("📊 تعديل الكمية")
        self.style_advanced_button(self.adjust_button, 'orange')  # برتقالي للكميات
        self.adjust_button.clicked.connect(self.adjust_quantity)
        self.adjust_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)  # أسود للتصدير - مطابق للعملاء والموردين
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير المتقدمة مطابقة لجميع الأقسام
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 15px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: center;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(self.export_excel_advanced)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(self.export_csv_advanced)
        export_menu.addAction(csv_action)

        pdf_action = QAction("📋 تصدير PDF تفصيلي", self)
        pdf_action.triggered.connect(self.export_pdf_advanced)
        export_menu.addAction(pdf_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        detailed_action = QAction("📊 تقرير تفصيلي", self)
        detailed_action.triggered.connect(self.export_detailed_report)
        export_menu.addAction(detailed_action)

        balance_action = QAction("💰 تقرير الأرصدة", self)
        balance_action.triggered.connect(self.export_balance_report)
        export_menu.addAction(balance_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(self.export_custom)
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(self.export_backup)
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(self.restore_backup)
        export_menu.addAction(restore_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'cyan')  # مطابق لزر عرض التفاصيل
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()



        # إضافة الأزرار للتخطيط - تم نقل الطباعة والباركود قبل التصدير
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.adjust_button)
        actions_layout.addWidget(self.print_barcode_button)  # طباعة الباركود أولاً
        actions_layout.addWidget(self.scan_barcode_button)   # مسح الباركود ثانياً
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.columns_visibility_button)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        # تهيئة حالة الأزرار (جميع الأزرار مفعلة ومنيرة في البداية)
        QTimer.singleShot(100, self.initialize_button_states)

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث
            self.search_edit.textChanged.connect(self.filter_inventory)
            # ربط زر البحث
            self.search_button.clicked.connect(self.filter_inventory)

            # ربط خاصية النقر المزدوج للتعديل مطابق للعملاء
            self.inventory_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

            print("✅ تم ربط أحداث المخزون بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث المخزون: {str(e)}")

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية مطابق للعملاء"""
        try:
            self.edit_item()
        except Exception as e:
            print(f"خطأ في النقر المزدوج على عنصر المخزون: {e}")
            self.show_error_message(f"حدث خطأ في فتح نافذة التعديل: {str(e)}")

    def create_advanced_inventory_table(self):
        """إنشاء جدول المخزون المتطور والمحسن مطابق للموردين"""
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(9)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🔢 ID",
            "📦 اسم العنصر",
            "🏷️ الفئة",
            "📊 الكمية",
            "⚠️ الحد الأدنى",
            "💰 سعر الشراء",
            "💵 سعر البيع",
            "📊 الباركود",
            "🏢 المورد"
        ]
        self.inventory_table.setHorizontalHeaderLabels(headers)

        # إعدادات عرض الأعمدة مع التكيف التلقائي مطابقة للعملاء
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # اسم العنصر
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # الفئة
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الكمية
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # الحد الأدنى
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # سعر الشراء
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # سعر البيع
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # الباركود
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # المورد

        # تحديد عرض الأعمدة الثابتة مطابق للعملاء
        self.inventory_table.setColumnWidth(0, 120)  # ID
        self.inventory_table.setColumnWidth(1, 300)  # اسم العنصر
        self.inventory_table.setColumnWidth(7, 150)  # الباركود

        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.inventory_table)

        # ربط معالج التحديد
        self.inventory_table.itemSelectionChanged.connect(self.on_inventory_selection_changed)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.inventory_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.inventory_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.inventory_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.verticalHeader().setDefaultSectionSize(45)
        self.inventory_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_inventory_table()

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.inventory_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.inventory_table, event)

        self.inventory_table.wheelEvent = wheelEvent

    def add_watermark_to_inventory_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.inventory_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.inventory_table.viewport())
                paint_watermark(painter, self.inventory_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.inventory_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.inventory_table.viewport().update()
        self.inventory_table.repaint()

    def on_inventory_selection_changed(self):
        """معالج تحديد المخزون مطابق للمشاريع"""
        try:
            print("🚨 تم استدعاء معالج تحديد المخزون!")

            # تسجيل أن المستخدم تفاعل مع الجدول
            self.user_interacted_with_table = True
            print("👆 المستخدم تفاعل مع الجدول - سيتم تطبيق خاصية الإغلاق")

            self.update_selected_inventory_list()
            self.update_button_states()

        except Exception as e:
            print(f"❌ خطأ في معالجة تحديد المخزون: {e}")
            print(traceback.format_exc())

    def update_selected_inventory_list(self):
        """تحديث قائمة عناصر المخزون المحددة مطابق للمشاريع"""
        try:
            if not hasattr(self, 'selected_inventory'):
                self.selected_inventory = []

            self.selected_inventory = []
            selected_items = self.inventory_table.selectedItems()

            # جمع معرفات عناصر المخزون المحددة
            selected_rows = set()
            for item in selected_items:
                selected_rows.add(item.row())

            for row in selected_rows:
                id_item = self.inventory_table.item(row, 0)  # عمود ID
                if id_item:
                    # استخراج الرقم من النص (إزالة الأيقونات والنصوص الإضافية)
                    id_text = ''.join(filter(str.isdigit, id_item.text()))
                    if id_text:
                        self.selected_inventory.append(int(id_text))

            print(f"🔧 تم تحديث قائمة عناصر المخزون المحددة: {len(self.selected_inventory)} عنصر")

        except Exception as e:
            print(f"❌ خطأ في تحديث قائمة عناصر المخزون المحددة: {e}")

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مطابق للمشاريع"""
        try:
            # إذا لم يتفاعل المستخدم مع الجدول بعد، لا نغير حالة الأزرار
            if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                print("🔥 المستخدم لم يتفاعل مع الجدول بعد - الأزرار تبقى مفعلة")
                return

            if not hasattr(self, 'selected_inventory'):
                self.selected_inventory = []

            selected_count = len(self.selected_inventory)
            has_selection = selected_count > 0
            has_single_selection = selected_count == 1

            print(f"🔧 تحديث حالة الأزرار: {selected_count} عنصر محدد")

            # الأزرار التي تحتاج تحديد واحد فقط
            self.set_button_visibility(self.edit_button, has_single_selection)
            self.set_button_visibility(self.view_button, has_single_selection)
            self.set_button_visibility(self.adjust_button, has_single_selection)  # تعديل الكمية

            # الأزرار التي تعمل مع التحديد المتعدد
            self.set_button_visibility(self.delete_button, has_selection)
            self.set_button_visibility(self.print_barcode_button, has_selection)  # طباعة الباركود تحتاج تحديد

            # الأزرار المتاحة دائماً
            self.set_button_visibility(self.add_button, True)  # زر الإضافة متاح دائماً

            # تحديث نص زر الحذف
            if has_selection:
                if selected_count > 1:
                    self.delete_button.setText(f"🗑️ حذف ({selected_count})")
                else:
                    self.delete_button.setText("🗑️ حذف")

        except Exception as e:
            print(f"❌ خطأ في تحديث حالة الأزرار: {e}")

    def set_button_visibility(self, button, visible):
        """تعيين رؤية الزر مع الحفاظ على الألوان الأصلية - طريقة مبسطة مطابقة للمشاريع"""
        try:
            if button:
                # التحقق من أن المستخدم تفاعل مع الجدول قبل تطبيق التأثيرات
                if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                    # إذا لم يتفاعل المستخدم بعد، نبقي الزر مفعلاً ومنيراً
                    button.setEnabled(True)
                    button.setGraphicsEffect(None)
                    print(f"🔥 زر {button.text()}: مفعل (لم يتفاعل المستخدم مع الجدول بعد)")
                    return

                button.setEnabled(visible)

                # طريقة أبسط وأكثر أماناً لتطبيق الشفافية
                if visible:
                    # إزالة أي تأثير شفافية
                    button.setGraphicsEffect(None)
                else:
                    # تطبيق تأثير الشفافية مع الحفاظ على الألوان
                    from PyQt5.QtWidgets import QGraphicsOpacityEffect
                    opacity_effect = QGraphicsOpacityEffect()
                    opacity_effect.setOpacity(0.5)  # 50% شفافية
                    button.setGraphicsEffect(opacity_effect)

                print(f"🔧 زر {button.text()}: {'مفعل' if visible else 'معطل'}")
        except Exception as e:
            print(f"❌ خطأ في تعيين رؤية الزر: {e}")
            # في حالة فشل التأثير، استخدم الطريقة البسيطة
            if button:
                button.setEnabled(True)  # نبقيه مفعلاً في حالة الخطأ

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة مطابق للمشاريع"""
        try:
            print("🔧 بدء تهيئة حالة أزرار المخزون...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة عنصر"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.adjust_button, "📊 تعديل الكمية"),
                (self.export_button, "📤 تصدير ▼"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    import re
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
                    print(f"🟢 تم تفعيل الزر: {name}")

            print("✅ تم تهيئة حالة أزرار المخزون بنجاح")

            # لا نعطل الأزرار تلقائياً - تبقى مفعلة حتى يتفاعل المستخدم مع الجدول
            print("🔥 الأزرار ستبقى مفعلة حتى التفاعل مع الجدول")

        except Exception as e:
            print(f"❌ خطأ في تهيئة حالة أزرار المخزون: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات المخزون في الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # التحقق من وجود session
            if not self.session:
                self.show_error_message("خطأ في الاتصال بقاعدة البيانات")
                return

            # الحصول على عناصر المخزون من قاعدة البيانات مع تحسين الذاكرة
            # تحميل البيانات بشكل تدريجي لتجنب استهلاك الذاكرة الزائد
            inventory_items = self.session.query(Inventory).order_by(Inventory.id.asc()).limit(5000).all()

            # إذا لم توجد بيانات، إنشاء بيانات تجريبية
            if not inventory_items:
                print("🧪 لا توجد بيانات في المخزون، إنشاء بيانات تجريبية...")
                self.create_sample_inventory_data()
                inventory_items = self.session.query(Inventory).order_by(Inventory.id.asc()).limit(5000).all()

            self.populate_table(inventory_items)
            self.update_summary(inventory_items)

        except Exception as e:
            print(f"خطأ في تحديث بيانات المخزون: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def create_sample_inventory_data(self):
        """إنشاء بيانات تجريبية للمخزون مع باركود"""
        try:
            from utils.barcode_generator import generate_product_barcode

            sample_items = [
                {"name": "أسمنت أبيض", "category": "مواد البناء", "quantity": 50, "min_quantity": 10, "cost_price": 45.0, "selling_price": 55.0},
                {"name": "طوب أحمر", "category": "مواد البناء", "quantity": 200, "min_quantity": 50, "cost_price": 2.5, "selling_price": 3.0},
                {"name": "دهان أبيض", "category": "دهانات", "quantity": 25, "min_quantity": 5, "cost_price": 120.0, "selling_price": 150.0},
                {"name": "بلاط سيراميك", "category": "سيراميك", "quantity": 100, "min_quantity": 20, "cost_price": 35.0, "selling_price": 45.0},
                {"name": "أنابيب PVC", "category": "أدوات صحية", "quantity": 75, "min_quantity": 15, "cost_price": 25.0, "selling_price": 35.0}
            ]

            for i, item_data in enumerate(sample_items):
                # توليد باركود للعنصر
                success, message, barcode = generate_product_barcode(
                    product_id=i+1,
                    product_name=item_data["name"],
                    format_type='code128'
                )

                if success:
                    item_data["barcode"] = barcode

                # إنشاء العنصر
                item = Inventory(**item_data)
                self.session.add(item)

            self.session.commit()
            print("✅ تم إنشاء البيانات التجريبية مع الباركود بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")

    def populate_table(self, items):
        """ملء جدول المخزون بالبيانات"""
        self.inventory_table.setRowCount(0)

        for row, item in enumerate(items):
            self.inventory_table.insertRow(row)

            # دالة مساعدة لإنشاء العناصر مطابق للعملاء
            def create_item(icon, text, default="No Data"):
                display_text = text if text and text.strip() else default
                item = QTableWidgetItem(f"{icon} {display_text}")
                item.setTextAlignment(Qt.AlignCenter)
                if display_text == default:
                    item.setForeground(QColor("#ef4444"))
                return item

            # الرقم مع أيقونة ثابتة - استخدام رقم متسلسل بدلاً من ID الفعلي
            sequential_number = row + 1
            id_item = QTableWidgetItem(f"🔢 {sequential_number}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
            # حفظ الـ ID الفعلي كبيانات مخفية للاستخدام في العمليات
            id_item.setData(Qt.UserRole, item.id)
            self.inventory_table.setItem(row, 0, id_item)

            self.inventory_table.setItem(row, 1, create_item("📦", item.name))

            # الفئة مطابق للعنوان
            self.inventory_table.setItem(row, 2, create_item("🏷️", item.category))

            # باقي الأعمدة مطابق للعملاء
            from utils import format_quantity, format_currency

            quantity_text = format_quantity(item.quantity) if item.quantity else None
            min_quantity_text = format_quantity(item.min_quantity) if item.min_quantity else None
            cost_price_text = format_currency(item.cost_price) if item.cost_price else None

            # الكمية والحد الأدنى مطابق للعناوين
            self.inventory_table.setItem(row, 3, create_item("📊", quantity_text))
            self.inventory_table.setItem(row, 4, create_item("⚠️", min_quantity_text))
            self.inventory_table.setItem(row, 5, create_item("💰", cost_price_text))

            # باقي الأعمدة مطابق للعملاء
            selling_price_text = format_currency(item.selling_price) if item.selling_price else None
            supplier_name = item.supplier.name if item.supplier else None

            self.inventory_table.setItem(row, 6, create_item("💵", selling_price_text))

            # عمود الباركود
            barcode_text = item.barcode if item.barcode else "لا يوجد"
            self.inventory_table.setItem(row, 7, create_item("📊", barcode_text))

            self.inventory_table.setItem(row, 8, create_item("🏢", supplier_name))

    def update_summary(self, items):
        """تحديث ملخص المخزون - تم إزالة العرض"""
        # تم إزالة عرض الإجمالي والمخزون المنخفض حسب الطلب
        pass

    def filter_inventory(self):
        """تصفية المخزون بناءً على نص البحث والفئة والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            category = getattr(self, 'current_category_value', None)
            status = getattr(self, 'current_status_value', None)
        except Exception as e:
            print(f"خطأ في الحصول على قيم التصفية: {str(e)}")
            return

        try:
            # بناء الاستعلام
            query = self.session.query(Inventory)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Inventory.name.like(f"%{search_text}%") |
                    Inventory.location.like(f"%{search_text}%") |
                    Inventory.barcode.like(f"%{search_text}%")
                )

            # تطبيق تصفية الفئة
            if category and category != "all":
                query = query.filter(Inventory.category == category)

            # تطبيق تصفية الحالة
            if status:
                if status == "متوفر":
                    # متوفر: الكمية أكبر من الحد الأدنى
                    query = query.filter(Inventory.quantity > Inventory.min_quantity)
                elif status == "منخفض":
                    # منخفض: الكمية تساوي الحد الأدنى أو أقل (لكن ليس صفر)
                    query = query.filter(
                        Inventory.quantity <= Inventory.min_quantity,
                        Inventory.quantity > 0
                    )
                elif status == "نفد":
                    # نفد المخزون: الكمية تساوي صفر بالضبط
                    query = query.filter(Inventory.quantity == 0)

            # تنفيذ الاستعلام
            items = query.order_by(Inventory.id.asc()).all()

            # تحديث الجدول
            self.populate_table(items)
            self.update_summary(items)

        except Exception as e:
            print(f"خطأ في تصفية المخزون: {str(e)}")
            # في حالة الخطأ، عرض جميع العناصر
            try:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()
                self.populate_table(items)
                self.update_summary(items)
            except Exception as e2:
                print(f"خطأ في عرض جميع العناصر: {str(e2)}")
                # إنشاء جدول فارغ
                self.inventory_table.setRowCount(0)

    def add_item(self):
        """إضافة عنصر جديد للمخزون"""
        dialog = InventoryItemDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء عنصر جديد
                item = Inventory(**data)
                self.session.add(item)
                self.session.commit()
                self.show_success_message(f"تم إضافة العنصر '{item.name}' بنجاح")
                self.refresh_data()

    def scan_barcode_search(self):
        """قراءة باركود للبحث عن منتج"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            try:
                from utils.barcode_reader import read_barcode_from_file, find_product_by_barcode
            except ImportError:
                self.show_error_message("مكتبة قراءة الباركود غير متوفرة")
                return

            # خيارات القراءة
            reply = QMessageBox.question(
                self,
                "قراءة الباركود",
                "كيف تريد قراءة الباركود؟\n\n"
                "نعم: من ملف صورة\n"
                "لا: من الكاميرا (قريباً)",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )

            if reply == QMessageBox.Yes:
                # قراءة من ملف
                file_path, _ = QFileDialog.getOpenFileName(
                    self,
                    "اختر صورة الباركود",
                    "",
                    "صور (*.png *.jpg *.jpeg *.bmp *.gif)"
                )

                if file_path:
                    success, message, barcodes = read_barcode_from_file(file_path)

                    if success and barcodes:
                        # استخدام أول باركود مكتشف
                        barcode_data = barcodes[0]['data']

                        # البحث عن المنتج في قاعدة البيانات
                        found, search_message, product = find_product_by_barcode(self.session, barcode_data)

                        if found and product:
                            # تحديث حقل البحث بالباركود
                            self.search_edit.setText(barcode_data)
                            self.filter_inventory()

                            # عرض رسالة نجاح
                            self.show_success_message(f"تم العثور على المنتج: {product.name}")

                            # تحديد المنتج في الجدول
                            self.select_product_in_table(product.id)
                        else:
                            self.show_error_message(f"لم يتم العثور على منتج بهذا الباركود: {barcode_data}")
                    else:
                        self.show_error_message(f"فشل في قراءة الباركود: {message}")

            elif reply == QMessageBox.No:
                # قراءة من الكاميرا (سيتم تطويرها لاحقاً)
                self.show_info_message("قراءة الباركود من الكاميرا ستكون متاحة قريباً")

        except Exception as e:
            self.show_error_message(f"خطأ في قراءة الباركود: {str(e)}")

    def select_product_in_table(self, product_id):
        """تحديد منتج في الجدول بناءً على معرفه"""
        try:
            for row in range(self.inventory_table.rowCount()):
                id_item = self.inventory_table.item(row, 0)
                if id_item and id_item.data(Qt.UserRole) == product_id:
                    self.inventory_table.selectRow(row)
                    self.inventory_table.scrollToItem(id_item)
                    break
        except Exception as e:
            print(f"خطأ في تحديد المنتج في الجدول: {str(e)}")

    def print_selected_barcodes(self):
        """طباعة ملصقات باركود للعناصر المحددة"""
        try:
            from PyQt5.QtWidgets import QInputDialog, QMessageBox, QFileDialog
            try:
                from utils.barcode_printer import create_product_labels
            except ImportError:
                self.show_error_message("مكتبة طباعة الباركود غير متوفرة")
                return

            # الحصول على العناصر المحددة
            selected_rows = set()
            for item in self.inventory_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                self.show_error_message("يرجى تحديد عنصر واحد على الأقل لطباعة الباركود")
                return

            # جمع بيانات المنتجات المحددة
            products_data = []
            for row in selected_rows:
                try:
                    # الحصول على معرف المنتج
                    id_item = self.inventory_table.item(row, 0)
                    if not id_item:
                        continue

                    product_id = id_item.data(Qt.UserRole)

                    # البحث عن المنتج في قاعدة البيانات
                    if not self.session:
                        self.show_error_message("خطأ في الاتصال بقاعدة البيانات")
                        return
                    product = self.session.query(Inventory).get(product_id)
                    if product and product.barcode:
                        product_data = {
                            'name': product.name,
                            'barcode': product.barcode,
                            'selling_price': product.selling_price or 0.0,
                            'category': product.category or ''
                        }
                        products_data.append(product_data)
                    else:
                        print(f"⚠️ المنتج {product.name if product else 'غير محدد'} لا يحتوي على باركود")

                except Exception as e:
                    print(f"خطأ في معالجة المنتج في الصف {row}: {str(e)}")

            if not products_data:
                self.show_error_message("لا توجد منتجات صالحة للطباعة (تحتاج إلى باركود)")
                return

            # خيارات الطباعة
            labels_per_product, ok = QInputDialog.getInt(
                self,
                "عدد الملصقات",
                f"كم ملصق تريد لكل منتج؟\n(تم تحديد {len(products_data)} منتج)",
                1, 1, 100, 1
            )

            if not ok:
                return

            # خيارات حجم الملصق
            size_options = ["صغير", "متوسط", "كبير"]
            size_mapping = {"صغير": "small", "متوسط": "medium", "كبير": "large"}

            size_choice, ok = QInputDialog.getItem(
                self,
                "حجم الملصق",
                "اختر حجم الملصق:",
                size_options,
                1,  # متوسط كافتراضي
                False
            )

            if not ok:
                return

            label_size = size_mapping[size_choice]

            # خيارات صيغة الحفظ
            format_options = ["PDF", "صور منفصلة", "ورقة واحدة"]
            format_mapping = {"PDF": "pdf", "صور منفصلة": "images", "ورقة واحدة": "sheet"}

            format_choice, ok = QInputDialog.getItem(
                self,
                "صيغة الحفظ",
                "اختر صيغة حفظ الملصقات:",
                format_options,
                0,  # PDF كافتراضي
                False
            )

            if not ok:
                return

            output_format = format_mapping[format_choice]

            # اختيار مكان الحفظ
            if output_format == "pdf":
                output_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "حفظ ملصقات الباركود",
                    f"barcode_labels_{format_datetime_for_filename()}.pdf",
                    "ملفات PDF (*.pdf)"
                )
            else:
                output_path = QFileDialog.getExistingDirectory(
                    self,
                    "اختر مجلد حفظ الملصقات"
                )

            if not output_path:
                return

            # إنشاء الملصقات
            success, message, saved_path = create_product_labels(
                products_data=products_data,
                labels_per_product=labels_per_product,
                label_size=label_size,
                output_format=output_format,
                output_path=output_path
            )

            if success:
                self.show_success_message(f"تم إنشاء الملصقات بنجاح!\n{message}")

                # سؤال عن فتح الملف
                reply = QMessageBox.question(
                    self,
                    "فتح الملف",
                    "هل تريد فتح الملف المحفوظ؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes and saved_path:
                    try:
                        import os
                        os.startfile(saved_path)  # Windows
                    except:
                        try:
                            import subprocess
                            subprocess.run(['xdg-open', saved_path])  # Linux
                        except:
                            print(f"لا يمكن فتح الملف تلقائياً: {saved_path}")
            else:
                self.show_error_message(f"فشل في إنشاء الملصقات: {message}")

        except Exception as e:
            self.show_error_message(f"خطأ في طباعة الباركود: {str(e)}")

    def edit_item(self):
        """تعديل عنصر في المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار عنصر من القائمة")
            return

        # استخراج الـ ID الفعلي من البيانات المخفية
        id_item = self.inventory_table.item(selected_row, 0)
        item_id = id_item.data(Qt.UserRole)

        # التحقق من وجود session
        if not self.session:
            self.show_error_message("خطأ في الاتصال بقاعدة البيانات")
            return

        item = self.session.query(Inventory).get(item_id)

        if not item:
            self.show_error_message("لم يتم العثور على العنصر")
            return

        dialog = InventoryItemDialog(self, item, self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات العنصر
                for key, value in data.items():
                    setattr(item, key, value)

                self.session.commit()
                self.show_success_message(f"تم تحديث العنصر '{item.name}' بنجاح")
                self.refresh_data()

    def delete_selected_items(self):
        """حذف عناصر المخزون المحددة"""
        try:
            self.update_selected_items()
            if not self.selected_items:
                return

            count = len(self.selected_items)
            if count == 1:
                self.delete_item()
            else:
                if self.show_confirmation_message("تأكيد الحذف", f"هل تريد حذف {count} عنصر من المخزون؟"):
                    for item_id in self.selected_items:
                        inventory_item = self.session.query(Inventory).get(item_id)
                        if inventory_item:
                            self.session.delete(inventory_item)
                    self.session.commit()
                    self.show_success_message(f"تم حذف {count} عنصر من المخزون بنجاح")
                    self.refresh_data()
        except Exception as e:
            self.show_error_message(f"خطأ في حذف عناصر المخزون: {str(e)}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية للمخزون"""
        try:
            menu = QMenu(self)

            single_actions = [
                ("✏️ تعديل", self.edit_item),
                ("👁️ عرض التفاصيل", self.view_item_details),
                ("🗑️ حذف", self.delete_item)
            ]

            multi_actions = [
                ("🗑️ حذف {count} عنصر", self.delete_selected_items)
            ]

            self.create_context_menu_actions(menu, single_actions, multi_actions)
            menu.exec_(self.inventory_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في القائمة السياقية: {e}")

    def delete_item(self):
        """حذف عنصر من المخزون مع نافذة تأكيد متطورة"""
        try:
            selected_row = self.inventory_table.currentRow()
            if selected_row < 0:
                self.show_warning_message("الرجاء اختيار عنصر من القائمة")
                return

            # استخراج الـ ID الفعلي من البيانات المخفية
            id_item = self.inventory_table.item(selected_row, 0)
            item_id = id_item.data(Qt.UserRole)
            item = self.session.query(Inventory).get(item_id)

            if not item:
                self.show_error_message("لم يتم العثور على العنصر")
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            dialog = DeleteInventoryDialog(self, item)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # حذف العنصر من قاعدة البيانات
                    self.session.delete(item)
                    self.session.commit()

                    # إظهار رسالة نجاح متطورة
                    self.show_success_message(f"تم حذف العنصر '{item.name}' بنجاح")

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف العنصر: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف العنصر: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventoryWarningDialog(self, "تحذير", message, "⚠️")
        dialog.exec_()

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventorySuccessDialog(self, "نجح", message, "✅")
        dialog.exec_()

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventoryErrorDialog(self, "خطأ", message, "❌")
        dialog.exec_()

    def show_confirmation_message(self, title, message):
        """إظهار رسالة تأكيد متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventoryConfirmationDialog(self, title, message, "❓")
        return dialog.exec_() == QDialog.Accepted

    def show_dangerous_action_dialog(self, title, message, action_name="متابعة"):
        """إظهار نافذة تحذير للعمليات الخطيرة"""
        dialog = InventoryDangerousActionDialog(self, title, message, action_name)
        return dialog.exec_() == QDialog.Accepted

    def show_backup_restore_dialog(self):
        """إظهار نافذة تأكيد استعادة النسخة الاحتياطية"""
        dialog = InventoryBackupRestoreDialog(self)
        return dialog.exec_() == QDialog.Accepted

    def show_info_message(self, message, title="معلومات"):
        """إظهار رسالة معلومات متطورة مطابقة لنافذة حذف الأقساط"""
        dialog = InventoryMessageDialog(self, title, message, "ℹ️")
        dialog.exec_()

    def view_item(self):
        """عرض تفاصيل عنصر المخزون - مطابق للنموذج المرجعي"""
        try:
            selected_row = self.inventory_table.currentRow()
            if selected_row < 0:
                self.show_error_message("الرجاء اختيار عنصر من القائمة")
                return

            # استخراج الـ ID الفعلي من البيانات المخفية
            id_item = self.inventory_table.item(selected_row, 0)
            item_id = id_item.data(Qt.UserRole)

            # البحث عن العنصر في قاعدة البيانات
            item = self.session.query(Inventory).get(item_id)
            if not item:
                self.show_error_message("لم يتم العثور على العنصر")
                return

            # إنشاء نافذة المعلومات المتطورة مع تحسين إدارة الذاكرة
            info_dialog = InventoryInfoDialog(self, item)
            try:
                info_dialog.exec_()
            finally:
                # تنظيف الذاكرة بعد إغلاق النافذة
                info_dialog.deleteLater()
                info_dialog = None

        except Exception as e:
            self.show_error_message(f"فشل في عرض تفاصيل العنصر: {str(e)}")
            return

    def adjust_quantity(self):
        """تعديل كمية عنصر في المخزون - نافذة متطورة مثل الإحصائيات"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار عنصر من القائمة")
            return

        # استخراج الـ ID الفعلي من البيانات المخفية
        id_item = self.inventory_table.item(selected_row, 0)
        item_id = id_item.data(Qt.UserRole)
        item = self.session.query(Inventory).get(item_id)

        if not item:
            self.show_error_message("لم يتم العثور على العنصر")
            return

        # فتح نافذة تعديل الكمية المتطورة
        dialog = AdjustQuantityDialog(self, item, self.session)
        dialog.exec_()



    def export_to_excel(self):
        """تصدير بيانات المخزون إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير بيانات المخزون إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_المخزون.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = []
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            self.show_success_message(f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_pdf(self):
        """تصدير بيانات المخزون إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

            if not items:
                self.show_warning_message("لا توجد عناصر للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون", "تقرير_المخزون.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المخزون</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>📦 تقرير المخزون</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم العنصر</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>القيمة الإجمالية</th>
                            <th>المورد</th>
                        </tr>
                """

                total_value = 0
                for item in items:
                    quantity = item.quantity or 0
                    cost_price = item.cost_price or 0
                    item_total = quantity * cost_price
                    total_value += item_total
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"

                    html_content += f"""
                        <tr>
                            <td>{item.id}</td>
                            <td>{item.name}</td>
                            <td>{int(quantity):,}</td>
                            <td>{int(cost_price):,} جنيه</td>
                            <td>{int(item_total):,} جنيه</td>
                            <td>{supplier_name}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي قيمة المخزون: {int(total_value):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                self.show_success_message(f"تم تصدير المخزون إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير بيانات المخزون إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_المخزون.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = {}
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            self.show_success_message(f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def view_stock_history(self):
        """عرض تاريخ المخزون للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار عنصر من القائمة")
            return

        # استخراج الـ ID الفعلي من البيانات المخفية
        id_item = self.inventory_table.item(selected_row, 0)
        item_id = id_item.data(Qt.UserRole)
        item = self.session.query(Inventory).get(item_id)

        if not item:
            self.show_error_message("لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لعرض تاريخ المخزون
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تاريخ المخزون - {item.name}")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout()

        # معلومات العنصر
        info_text = f"""
📦 تاريخ المخزون - {item.name}

📊 المعلومات الحالية:
• الكمية الحالية: {item.quantity} وحدة
• الحد الأدنى: {item.min_quantity} وحدة
• سعر التكلفة: {format_currency(item.cost_price)}
• سعر البيع: {format_currency(item.selling_price)}
• آخر تحديث: {item.last_updated.strftime('%Y-%m-%d %H:%M') if item.last_updated else 'غير متوفر'}

📈 الإحصائيات:
• قيمة المخزون: {format_currency(item.quantity * item.cost_price)}
• الربح المتوقع: {format_currency(item.quantity * (item.selling_price - item.cost_price))}
• حالة المخزون: {'منخفض ⚠️' if item.quantity <= item.min_quantity else 'طبيعي ✅'}

📝 ملاحظات:
• يُنصح بإعادة الطلب عند الوصول للحد الأدنى
• تحقق من تواريخ انتهاء الصلاحية إن وجدت
        """

        info_label = QLabel(info_text)
        info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(info_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_supplier_info(self):
        """عرض معلومات المورد للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار عنصر من القائمة")
            return

        # استخراج الـ ID الفعلي من البيانات المخفية
        id_item = self.inventory_table.item(selected_row, 0)
        item_id = id_item.data(Qt.UserRole)
        item = self.session.query(Inventory).get(item_id)

        if not item:
            self.show_error_message("لم يتم العثور على العنصر")
            return

        if not item.supplier:
            self.show_warning_message(f"لا يوجد مورد محدد للعنصر '{item.name}'")
            return

        supplier = item.supplier

        # إنشاء نافذة لعرض معلومات المورد
        dialog = QDialog(self)
        dialog.setWindowTitle(f"معلومات المورد - {supplier.name}")
        dialog.setMinimumSize(500, 350)

        layout = QVBoxLayout()

        # معلومات المورد
        supplier_text = f"""
🏪 معلومات المورد

📋 البيانات الأساسية:
• الاسم: {supplier.name}
• الهاتف: {supplier.phone or 'غير متوفر'}
• البريد الإلكتروني: {supplier.email or 'غير متوفر'}
• العنوان: {supplier.address or 'غير متوفر'}

💰 المعلومات المالية:
• الرصيد الحالي: {format_currency(supplier.balance)}
• حالة الرصيد: {'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'}

📦 معلومات العنصر:
• اسم العنصر: {item.name}
• سعر التكلفة: {format_currency(item.cost_price)}
• الكمية المتوفرة: {item.quantity} وحدة

📝 ملاحظات:
{supplier.notes or 'لا توجد ملاحظات'}
        """

        supplier_label = QLabel(supplier_text)
        supplier_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(supplier_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            # الحصول على العناصر منخفضة المخزون
            low_stock_items = self.session.query(Inventory).filter(
                Inventory.quantity <= Inventory.min_quantity
            ).order_by(Inventory.id.asc()).all()

            if not low_stock_items:
                self.show_warning_message("لا توجد عناصر منخفضة المخزون حالياً")
                return

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ تقرير المخزون المنخفض", "تقرير_المخزون_المنخفض.pdf", "ملفات PDF (*.pdf)")
            if not file_path:
                return

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_low_stock_report_html(low_stock_items)
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            self.show_success_message(f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def generate_low_stock_report_html(self, low_stock_items):
        """إنشاء محتوى HTML لتقرير المخزون المنخفض"""
        try:
            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المخزون المنخفض</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #e74c3c; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #e74c3c; color: white; }}
                    .warning {{ background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }}
                    .critical {{ background-color: #f8d7da; color: #721c24; }}
                </style>
            </head>
            <body>
                <h1>⚠️ تقرير المخزون المنخفض</h1>
                <p style="text-align: center;">تاريخ التقرير: {format_datetime_for_export()}</p>

                <div class="warning">
                    <h2>🚨 تحذير</h2>
                    <p>يوجد <strong>{len(low_stock_items)}</strong> عنصر منخفض المخزون يحتاج إلى إعادة طلب فوري!</p>
                </div>

                <h2>📋 تفاصيل العناصر المنخفضة</h2>
                <table>
                    <tr>
                        <th>اسم العنصر</th>
                        <th>الفئة</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>المورد</th>
                        <th>الحالة</th>
                    </tr>
            """

            # إضافة صفوف العناصر
            for item in low_stock_items:
                status_class = "critical" if item.quantity == 0 else ""
                status_text = "نفد المخزون" if item.quantity == 0 else "منخفض"

                html += f"""
                    <tr class="{status_class}">
                        <td>{item.name}</td>
                        <td>{item.category or ''}</td>
                        <td>{item.quantity}</td>
                        <td>{item.min_quantity}</td>
                        <td>{item.supplier.name if item.supplier else 'غير محدد'}</td>
                        <td>{status_text}</td>
                    </tr>
                """

            html += """
                </table>

                <div class="warning">
                    <h2>📝 توصيات</h2>
                    <ul>
                        <li>قم بإعادة طلب العناصر المنخفضة فوراً</li>
                        <li>تواصل مع الموردين لتأكيد توفر العناصر</li>
                        <li>راجع الحد الأدنى للمخزون بانتظام</li>
                        <li>فكر في زيادة الحد الأدنى للعناصر سريعة الاستهلاك</li>
                    </ul>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في إنشاء التقرير</h1>
                <p>حدث خطأ أثناء إنشاء تقرير المخزون المنخفض: {str(e)}</p>
            </body>
            </html>
            """


    def show_statistics(self):
        """عرض نافذة إحصائيات المخزون"""
        try:
            dialog = InventoryStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض إحصائيات المخزون: {e}")
            self.show_error_message(f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        self.columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        self.columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("📦 اسم المنتج", 1),
            ("📂 الفئة", 2),
            ("🔢 الكمية المتاحة", 3),
            ("💰 سعر الوحدة", 4),
            ("💵 القيمة الإجمالية", 5),
            ("📍 الموقع", 6),
            ("📋 الوصف", 7),
            ("🗓️ تاريخ الإضافة", 8)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            self.columns_menu.addAction(action)

        # إضافة فاصل
        self.columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        self.columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        self.columns_menu.addAction(hide_all_action)

        # حفظ مرجع للقائمة
        columns_menu = self.columns_menu

        # تطبيق تصميم المحاذاة اليمنى للعناصر المحددة
        for action in columns_menu.actions():
            if action.text() and not action.isSeparator():
                # محاذاة النص لليمين مع مسافة كافية
                current_text = action.text()
                right_aligned_text = f"{current_text:>30}"
                action.setText(right_aligned_text)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            self.columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = self.columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            self.columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'inventory_table') and self.inventory_table:
                if visible:
                    self.inventory_table.showColumn(column_index)
                else:
                    self.inventory_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"خطأ في تبديل إظهار العمود: {e}")

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'inventory_table') and self.inventory_table:
                for i in range(self.inventory_table.columnCount()):
                    self.inventory_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"خطأ في إظهار جميع الأعمدة: {e}")

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'inventory_table') and self.inventory_table:
                for i in range(self.inventory_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.inventory_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"خطأ في إخفاء الأعمدة: {e}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المخزون.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المخزون
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
""")

                self.show_success_message(f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التقرير: {str(e)}")

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            if not low_stock_items:
                self.show_warning_message("لا توجد عناصر منخفضة المخزون")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون المنخفض", "المخزون_المنخفض.txt", "Text Files (*.txt)"
            )

            if file_path:
                report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                            ⚠️ تقرير المخزون المنخفض
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

🔴 عدد العناصر المنخفضة: {len(low_stock_items)}

📋 تفاصيل العناصر المنخفضة:
─────────────────────────────────────────────────────────────────────────────
"""

                for item in low_stock_items:
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"
                    report_content += f"""
🔸 {item.name}
   📊 الكمية الحالية: {int(item.quantity or 0)}
   ⚠️ الحد الأدنى: {int(item.min_quantity or 5)}
   💰 سعر التكلفة: {int(item.cost_price or 0):,} جنيه
   🏪 المورد: {supplier_name}
   📂 الفئة: {item.category or 'غير محدد'}
   ─────────────────────────────────────────────────────────────────────────────
"""

                report_content += """
💡 التوصيات:
─────────────────────────────────────────────────────────────────────────────
• إعادة طلب العناصر المنخفضة فوراً
• مراجعة الحد الأدنى للمخزون
• التواصل مع الموردين لتأكيد التوفر
• مراقبة معدل الاستهلاك

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
"""

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                self.show_success_message(f"تم تصدير تقرير المخزون المنخفض بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_stock_alerts(self):
        """عرض تنبيهات المخزون"""
        try:
            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            # إنشاء نافذة التنبيهات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QListWidgetItem, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("⚠️ تنبيهات المخزون")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout()

            # معلومات التنبيهات
            info_label = QLabel(f"🔴 عدد العناصر المنخفضة: {len(low_stock_items)}")
            info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f8d7da; border-radius: 5px; color: #721c24;")
            layout.addWidget(info_label)

            # قائمة التنبيهات
            alerts_list = QListWidget()
            alerts_list.setStyleSheet("""
                QListWidget {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #eee;
                    margin: 2px;
                    border-radius: 3px;
                }
                QListWidget::item:selected {
                    background-color: #fff3cd;
                }
            """)

            if low_stock_items:
                for item in low_stock_items:
                    alert_text = f"⚠️ {item.name} - الكمية: {int(item.quantity or 0)} (الحد الأدنى: {int(item.min_quantity or 5)})"
                    list_item = QListWidgetItem(alert_text)
                    alerts_list.addItem(list_item)
            else:
                no_alerts_item = QListWidgetItem("✅ لا توجد تنبيهات - جميع العناصر فوق الحد الأدنى")
                alerts_list.addItem(no_alerts_item)

            layout.addWidget(alerts_list)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            if low_stock_items:
                export_btn = QPushButton("📤 تصدير التقرير")
                export_btn.clicked.connect(lambda: (dialog.close(), self.export_low_stock_report()))
                buttons_layout.addWidget(export_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في عرض التنبيهات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#2d2d2d', 'bg_bottom': '#404040',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#1a1a1a', 'pressed_bottom': '#2d2d2d', 'pressed_border': '#1a1a1a',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                },
                'gray': {
                    'bg_start': '#374151', 'bg_mid': '#4b5563', 'bg_end': '#6b7280', 'bg_bottom': '#9ca3af',
                    'hover_start': '#4b5563', 'hover_mid': '#6b7280', 'hover_end': '#9ca3af', 'hover_bottom': '#d1d5db',
                    'hover_border': '#9ca3af', 'pressed_start': '#1f2937', 'pressed_mid': '#374151',
                    'pressed_end': '#4b5563', 'pressed_bottom': '#6b7280', 'pressed_border': '#4b5563',
                    'border': '#6b7280', 'text': '#ffffff', 'shadow': 'rgba(107, 114, 128, 0.5)'
                },
                'warning': {
                    'bg_start': '#451a03', 'bg_mid': '#78350f', 'bg_end': '#a16207', 'bg_bottom': '#eab308',
                    'hover_start': '#78350f', 'hover_mid': '#ca8a04', 'hover_end': '#eab308', 'hover_bottom': '#facc15',
                    'hover_border': '#eab308', 'pressed_start': '#451a03', 'pressed_mid': '#78350f',
                    'pressed_end': '#a16207', 'pressed_bottom': '#ca8a04', 'pressed_border': '#a16207',
                    'border': '#eab308', 'text': '#ffffff', 'shadow': 'rgba(234, 179, 8, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم على الزر: {str(e)}")





    def create_custom_category_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للفئات مطابقة للعملاء"""
        try:
            print("🔧 إنشاء قائمة تصفية الفئات...")

            # إنشاء إطار للقائمة المخصصة
            self.category_filter_frame = QFrame()
            self.category_filter_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 6px 15px;
                    min-width: 500px;
                    max-width: 500px;
                    min-height: 33px;
                    max-height: 37px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
                }
            """)

            # إنشاء تخطيط أفقي للإطار
            filter_layout = QHBoxLayout(self.category_filter_frame)
            filter_layout.setContentsMargins(5, 0, 5, 0)
            filter_layout.setSpacing(8)

            # سهم يسار
            self.left_arrow = QPushButton("▼")
            self.left_arrow.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # زر القائمة (سهم يمين)
            self.category_menu_button = QPushButton("▼")
            self.category_menu_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # النص الحالي للفئة
            self.current_category_label = QLabel("جميع الفئات")
            self.current_category_label.setAlignment(Qt.AlignCenter)
            self.current_category_label.setStyleSheet("""
                QLabel {
                    color: #1f2937;
                    font-size: 16px;
                    font-weight: 900;
                    background: transparent;
                    border: none;
                    padding: 0px 12px;
                    text-align: center;
                    max-width: 435px;
                    min-width: 435px;
                    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                    cursor: pointer;
                }
            """)

            # إضافة العناصر للتخطيط
            filter_layout.addWidget(self.left_arrow, 0)
            filter_layout.addWidget(self.current_category_label, 1)
            filter_layout.addWidget(self.category_menu_button, 0)

            # إنشاء القائمة المنسدلة
            self.setup_category_menu()

            # ربط الأحداث
            self.category_menu_button.clicked.connect(self.show_category_menu)
            self.left_arrow.clicked.connect(self.show_category_menu)
            self.category_filter_frame.mousePressEvent = self.category_frame_mouse_press_event
            self.current_category_label.mousePressEvent = self.category_frame_mouse_press_event

            # تعيين القيم الافتراضية
            self.current_category_value = None
            print("✅ تم إنشاء قائمة تصفية الفئات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة تصفية الفئات: {str(e)}")
            import traceback
            traceback.print_exc()
            # إنشاء قائمة بسيطة كبديل
            self.category_filter_frame = QLabel("جميع الفئات")
            self.current_category_value = None

    def setup_category_menu(self):
        """إعداد قائمة التصفية"""
        self.category_menu = QMenu(self)
        self.category_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                border-radius: 15px;
                padding: 12px 0px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية
        filter_options = [
            ("جميع الفئات", None),
            ("📦 إلكترونيات", "إلكترونيات"),
            ("🏠 أثاث", "أثاث"),
            ("👕 ملابس", "ملابس"),
            ("📚 كتب", "كتب"),
            ("🍔 طعام", "طعام"),
            ("📦 أخرى", "أخرى")
        ]

        # إضافة الفئات من قاعدة البيانات
        if self.session:
            try:
                db_categories = self.session.query(Inventory.category).distinct().filter(
                    Inventory.category.isnot(None),
                    Inventory.category != ""
                ).all()

                existing_values = {value for text, value in filter_options}
                for category_tuple in db_categories:
                    category = category_tuple[0]
                    if category and category.strip() and category not in existing_values:
                        filter_options.append((f"📦 {category}", category))
            except Exception as e:
                print(f"خطأ في جلب الفئات من قاعدة البيانات: {e}")

        for text, value in filter_options:
            centered_text = f"{text:^35}"
            action = QAction(centered_text, self)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_category_filter(v, t))
            self.category_menu.addAction(action)

    def category_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار التصفية"""
        if event.button() == Qt.LeftButton:
            self.show_category_menu()

    def show_category_menu(self):
        """عرض قائمة التصفية"""
        try:
            frame_pos = self.category_filter_frame.mapToGlobal(self.category_filter_frame.rect().bottomLeft())
            self.category_menu.exec_(frame_pos)
        except Exception as e:
            print(f"خطأ في عرض قائمة الفئات: {str(e)}")

    def set_category_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_category_value = value
        self.current_category_label.setText(text)
        self.filter_inventory()

    def update_category_filter(self):
        """تحديث خيارات تصفية الفئات بعد إضافة أو حذف فئة"""
        try:
            print("🔄 تحديث تصفية الفئات...")
            # مسح القائمة الحالية
            self.category_menu.clear()

            # إعادة إنشاء القائمة
            self.setup_category_menu()
            print("✅ تم تحديث تصفية الفئات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحديث تصفية الفئات: {e}")

    def set_category_filter(self, value, text):
        """تعيين تصفية الفئة"""
        self.current_category_value = value
        self.current_category_label.setText(text)
        self.filter_inventory()

    def update_category_filter(self):
        """تحديث خيارات تصفية الفئات بعد إضافة أو حذف فئة"""
        try:
            print("🔄 تحديث تصفية الفئات...")
            # مسح القائمة الحالية
            self.category_menu.clear()

            # إضافة خيار "جميع الفئات"
            all_action = QAction("جميع الفئات".center(35), self)
            all_action.setData(None)
            all_action.triggered.connect(lambda: self.set_category_filter(None, "جميع الفئات"))
            self.category_menu.addAction(all_action)

            # الحصول على الفئات من قاعدة البيانات
            categories_from_db = set()
            if self.session:
                db_categories = self.session.query(Inventory.category).distinct().filter(
                    Inventory.category.isnot(None),
                    Inventory.category != ""
                ).all()

                print(f"📊 تم العثور على {len(db_categories)} فئة في قاعدة البيانات")
                for category_tuple in db_categories:
                    category = category_tuple[0]
                    if category and category.strip():
                        categories_from_db.add(category.strip())
                        print(f"  - {category}")

                print(f"📋 إجمالي الفئات المميزة: {len(categories_from_db)}")

            # الفئات الافتراضية
            default_categories = [
                ("🎨 دهانات", "دهانات"),
                ("🏺 سيراميك", "سيراميك"),
                ("🪵 أخشاب", "أخشاب"),
                ("🚿 أدوات صحية", "أدوات صحية"),
                ("⚡ أدوات كهربائية", "أدوات كهربائية"),
                ("🧱 مواد بناء", "مواد بناء"),
                ("📦 أخرى", "أخرى")
            ]

            # دمج الفئات من قاعدة البيانات مع الافتراضية
            all_categories = set()
            for text, value in default_categories:
                all_categories.add((text, value))

            # إضافة الفئات من قاعدة البيانات التي ليست في الافتراضية
            default_values = {value for text, value in default_categories}
            for category in categories_from_db:
                if category not in default_values:
                    all_categories.add((f"📦 {category}", category))

            # إضافة جميع الفئات للقائمة
            for text, value in sorted(all_categories, key=lambda x: x[1]):
                centered_text = f"{text:^35}"
                action = QAction(centered_text, self)
                action.setData(value)
                action.triggered.connect(lambda checked, v=value, t=text: self.set_category_filter(v, t))
                self.category_menu.addAction(action)

            print("✅ تم تحديث تصفية الفئات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحديث تصفية الفئات: {e}")

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة للحالة"""
        try:
            print("🔧 إنشاء قائمة تصفية الحالة...")

            # إنشاء إطار للقائمة المخصصة
            self.status_filter_frame = QFrame()
            self.status_filter_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 6px 15px;
                    min-width: 500px;
                    max-width: 500px;
                    min-height: 33px;
                    max-height: 37px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
                }
            """)

            # إنشاء تخطيط أفقي للإطار
            status_layout = QHBoxLayout(self.status_filter_frame)
            status_layout.setContentsMargins(5, 0, 5, 0)
            status_layout.setSpacing(8)

            # سهم يسار
            self.status_left_arrow = QPushButton("▼")
            self.status_left_arrow.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # زر القائمة (سهم يمين)
            self.status_menu_button = QPushButton("▼")
            self.status_menu_button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 28px;
                    max-height: 28px;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.9),
                        stop:0.2 rgba(96, 165, 250, 0.8),
                        stop:0.4 rgba(139, 92, 246, 0.7),
                        stop:0.6 rgba(124, 58, 237, 0.8),
                        stop:0.8 rgba(109, 40, 217, 0.9),
                        stop:1 rgba(91, 33, 182, 0.8));
                    border: 3px solid rgba(139, 92, 246, 0.9);
                }
            """)

            # النص الحالي للحالة
            self.current_status_label = QLabel("جميع الحالات")
            self.current_status_label.setAlignment(Qt.AlignCenter)
            self.current_status_label.setStyleSheet("""
                QLabel {
                    color: #1f2937;
                    font-size: 16px;
                    font-weight: 900;
                    background: transparent;
                    border: none;
                    padding: 0px 12px;
                    text-align: center;
                    max-width: 435px;
                    min-width: 435px;
                    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                    cursor: pointer;
                }
            """)

            # إضافة العناصر للتخطيط
            status_layout.addWidget(self.status_left_arrow, 0)
            status_layout.addWidget(self.current_status_label, 1)
            status_layout.addWidget(self.status_menu_button, 0)

            # إنشاء القائمة المنسدلة
            self.setup_status_menu()

            # ربط الأحداث
            self.status_menu_button.clicked.connect(self.show_status_menu)
            self.status_left_arrow.clicked.connect(self.show_status_menu)
            self.status_filter_frame.mousePressEvent = self.status_frame_mouse_press_event
            self.current_status_label.mousePressEvent = self.status_frame_mouse_press_event

            # تعيين القيم الافتراضية
            self.current_status_value = None
            print("✅ تم إنشاء قائمة تصفية الحالة بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة تصفية الحالة: {str(e)}")
            import traceback
            traceback.print_exc()
            # إنشاء قائمة بسيطة كبديل
            self.status_filter_frame = QLabel("جميع الحالات")
            self.current_status_value = None

    def setup_status_menu(self):
        """إعداد قائمة تصفية الحالة"""
        self.status_menu = QMenu(self)
        self.status_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                border-radius: 15px;
                padding: 12px 0px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية
        status_options = [
            ("جميع الحالات", None),
            ("🟢 متوفر", "متوفر"),
            ("🟡 مخزون منخفض", "منخفض"),
            ("🔴 نفد المخزون", "نفد")
        ]

        for text, value in status_options:
            centered_text = f"{text:^35}"
            action = QAction(centered_text, self)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_status_filter(v, t))
            self.status_menu.addAction(action)

    def status_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الحالة"""
        if event.button() == Qt.LeftButton:
            self.show_status_menu()

    def show_status_menu(self):
        """عرض قائمة تصفية الحالة"""
        try:
            frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
            self.status_menu.exec_(frame_pos)
        except Exception as e:
            print(f"خطأ في عرض قائمة الحالة: {str(e)}")

    def set_status_filter(self, value, text):
        """تعيين قيمة تصفية الحالة"""
        self.current_status_value = value
        self.current_status_label.setText(text)
        self.filter_inventory()

    def category_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الفئة"""
        self.show_category_menu()

    def export_detailed_report(self):
        """تصدير تقرير تفصيلي للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            # الحصول على جميع عناصر المخزون
            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

            if not items:
                self.show_warning_message("لا توجد عناصر في المخزون للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير التفصيلي",
                f"تقرير_مخزون_تفصيلي_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow([
                        'الرقم', 'اسم الصنف', 'الكمية', 'سعر الوحدة', 'القيمة الإجمالية',
                        'الحد الأدنى', 'الحد الأقصى', 'الفئة', 'الملاحظات'
                    ])

                    # كتابة البيانات التفصيلية
                    total_value = 0
                    total_quantity = 0
                    for item in items:
                        item_value = (item.quantity or 0) * (item.unit_price or 0)
                        total_value += item_value
                        total_quantity += item.quantity or 0

                        writer.writerow([
                            item.id,
                            item.name,
                            item.quantity or 0,
                            f"{item.unit_price:.2f}" if item.unit_price else "0.00",
                            f"{item_value:.2f}",
                            item.min_quantity or 0,
                            item.max_quantity or 0,
                            item.category or "غير محدد",
                            item.notes or ""
                        ])

                    # إضافة صف الإجمالي
                    writer.writerow([])
                    writer.writerow(['الإجمالي', '', total_quantity, '', f"{total_value:.2f}", '', '', '', '', ''])

                self.show_success_message(f"تم تصدير التقرير التفصيلي بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التقرير التفصيلي: {str(e)}")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            from sqlalchemy import func

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الأرصدة",
                f"تقرير_أرصدة_مخزون_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow(['نوع التقرير', 'القيمة', 'العدد', 'النسبة المئوية'])
                    writer.writerow([])

                    # تقرير حسب حالة المخزون
                    writer.writerow(['=== تقرير حسب حالة المخزون ==='])

                    # العناصر منخفضة المخزون
                    low_stock_items = self.session.query(Inventory).filter(
                        Inventory.quantity <= Inventory.min_quantity
                    ).order_by(Inventory.id.asc()).all()

                    # العناصر عالية المخزون
                    high_stock_items = self.session.query(Inventory).filter(
                        Inventory.quantity >= Inventory.max_quantity
                    ).order_by(Inventory.id.asc()).all()

                    # العناصر في المستوى الطبيعي
                    normal_stock_items = self.session.query(Inventory).filter(
                        Inventory.quantity > Inventory.min_quantity,
                        Inventory.quantity < Inventory.max_quantity
                    ).order_by(Inventory.id.asc()).all()

                    total_items = self.session.query(func.count(Inventory.id)).scalar() or 0
                    total_value = sum((item.quantity or 0) * (item.cost_price or 0) for item in self.session.query(Inventory).order_by(Inventory.id.asc()).all())

                    # حساب القيم
                    low_stock_value = sum((item.quantity or 0) * (item.unit_price or 0) for item in low_stock_items)
                    high_stock_value = sum((item.quantity or 0) * (item.unit_price or 0) for item in high_stock_items)
                    normal_stock_value = sum((item.quantity or 0) * (item.unit_price or 0) for item in normal_stock_items)

                    # كتابة البيانات
                    if total_items > 0:
                        low_percentage = (len(low_stock_items) / total_items) * 100
                        high_percentage = (len(high_stock_items) / total_items) * 100
                        normal_percentage = (len(normal_stock_items) / total_items) * 100
                    else:
                        low_percentage = high_percentage = normal_percentage = 0

                    writer.writerow(['مخزون منخفض', f"{low_stock_value:.2f}", len(low_stock_items), f"{low_percentage:.1f}%"])
                    writer.writerow(['مخزون عالي', f"{high_stock_value:.2f}", len(high_stock_items), f"{high_percentage:.1f}%"])
                    writer.writerow(['مخزون طبيعي', f"{normal_stock_value:.2f}", len(normal_stock_items), f"{normal_percentage:.1f}%"])
                    writer.writerow(['الإجمالي', f"{total_value:.2f}", total_items, "100.0%"])
                    writer.writerow([])

                    # تقرير حسب الفئات
                    writer.writerow(['=== تقرير حسب الفئات ==='])
                    categories = self.session.query(
                        Inventory.category,
                        func.count(Inventory.id),
                        func.sum(Inventory.quantity * Inventory.cost_price)
                    ).group_by(Inventory.category).order_by(Inventory.category.asc()).all()

                    for category, count, value in categories:
                        category_name = category or "غير محدد"
                        value = value or 0
                        percentage = (value / total_value * 100) if total_value > 0 else 0
                        writer.writerow([category_name, f"{value:.2f}", count, f"{percentage:.1f}%"])

                self.show_success_message(f"تم تصدير تقرير الأرصدة بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير تقرير الأرصدة: {str(e)}")

    def export_excel_advanced(self):
        """تصدير Excel متقدم للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            # الحصول على جميع عناصر المخزون
            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

            if not items:
                self.show_warning_message("لا توجد عناصر في المخزون للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel",
                f"مخزون_متقدم_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين المتقدمة
                    writer.writerow([
                        'الرقم', 'اسم الصنف', 'الكمية', 'سعر التكلفة', 'سعر البيع',
                        'القيمة الإجمالية', 'الربح المتوقع', 'الحد الأدنى', 'الحد الأقصى',
                        'الفئة', 'الموقع', 'الملاحظات', 'حالة المخزون'
                    ])

                    # كتابة البيانات المتقدمة
                    for item in items:
                        total_value = (item.quantity or 0) * (item.cost_price or 0)
                        expected_profit = (item.quantity or 0) * ((item.selling_price or 0) - (item.cost_price or 0))

                        # تحديد حالة المخزون
                        if (item.quantity or 0) <= (item.min_quantity or 0):
                            stock_status = "منخفض"
                        elif (item.quantity or 0) >= (item.max_quantity or 999):
                            stock_status = "عالي"
                        else:
                            stock_status = "طبيعي"

                        writer.writerow([
                            item.id,
                            item.name,
                            item.quantity or 0,
                            f"{item.cost_price:.2f}" if item.cost_price else "0.00",
                            f"{item.selling_price:.2f}" if item.selling_price else "0.00",
                            f"{total_value:.2f}",
                            f"{expected_profit:.2f}",
                            item.min_quantity or 0,
                            item.max_quantity or 0,
                            item.category or "غير محدد",
                            item.location or "غير محدد",
                            item.notes or "",
                            stock_status
                        ])

                self.show_success_message(f"تم تصدير Excel المتقدم بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير Excel المتقدم: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            # الحصول على جميع عناصر المخزون
            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

            if not items:
                self.show_warning_message("لا توجد عناصر في المخزون للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV الشامل",
                f"مخزون_شامل_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التقرير
                    writer.writerow(['تقرير المخزون الشامل'])
                    writer.writerow([f'تاريخ التقرير: {format_datetime_for_export()}'])
                    writer.writerow([f'إجمالي العناصر: {len(items)}'])
                    writer.writerow([])

                    # كتابة العناوين
                    writer.writerow([
                        'الرقم', 'اسم الصنف', 'الكمية', 'سعر التكلفة', 'سعر البيع',
                        'القيمة الإجمالية', 'هامش الربح%', 'الفئة', 'الموقع'
                    ])

                    # كتابة البيانات
                    total_value = 0
                    for item in items:
                        item_value = (item.quantity or 0) * (item.cost_price or 0)
                        total_value += item_value

                        # حساب هامش الربح
                        if item.cost_price and item.cost_price > 0:
                            profit_margin = ((item.selling_price or 0) - item.cost_price) / item.cost_price * 100
                        else:
                            profit_margin = 0

                        writer.writerow([
                            item.id,
                            item.name,
                            item.quantity or 0,
                            f"{item.cost_price:.2f}" if item.cost_price else "0.00",
                            f"{item.selling_price:.2f}" if item.selling_price else "0.00",
                            f"{item_value:.2f}",
                            f"{profit_margin:.1f}%",
                            item.category or "غير محدد",
                            item.location or "غير محدد"
                        ])

                    # إضافة الإجماليات
                    writer.writerow([])
                    writer.writerow(['الإجمالي', '', '', '', '', f"{total_value:.2f}", '', '', '', ''])

                self.show_success_message(f"تم تصدير CSV الشامل بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير CSV الشامل: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument, QFont
            from datetime import datetime

            # الحصول على جميع عناصر المخزون
            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

            if not items:
                self.show_warning_message("لا توجد عناصر في المخزون للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف PDF",
                f"مخزون_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء مستند HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        .header {{ text-align: center; margin-bottom: 30px; }}
                        .title {{ font-size: 24px; font-weight: bold; color: #2563eb; }}
                        .subtitle {{ font-size: 14px; color: #6b7280; margin-top: 10px; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #d1d5db; padding: 8px; text-align: center; }}
                        th {{ background-color: #f3f4f6; font-weight: bold; }}
                        .summary {{ margin-top: 30px; padding: 15px; background-color: #f8fafc; border-radius: 8px; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="title">تقرير المخزون التفصيلي</div>
                        <div class="subtitle">تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
                    </div>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم الصنف</th>
                            <th>الكمية</th>
                            <th>سعر التكلفة</th>
                            <th>سعر البيع</th>
                            <th>القيمة الإجمالية</th>
                            <th>الفئة</th>
                            <th>الموقع</th>
                        </tr>
                """

                total_value = 0
                total_quantity = 0

                for item in items:
                    item_value = (item.quantity or 0) * (item.cost_price or 0)
                    total_value += item_value
                    total_quantity += item.quantity or 0

                    html_content += f"""
                        <tr>
                            <td>{item.id}</td>
                            <td>{item.name}</td>
                            <td>{item.quantity or 0}</td>
                            <td>{item.cost_price:.2f} جنيه</td>
                            <td>{item.selling_price:.2f} جنيه</td>
                            <td>{item_value:.2f} جنيه</td>
                            <td>{item.category or 'غير محدد'}</td>
                            <td>{item.location or 'غير محدد'}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>

                    <div class="summary">
                        <h3>ملخص التقرير</h3>
                        <p><strong>إجمالي العناصر:</strong> {len(items)} عنصر</p>
                        <p><strong>إجمالي الكمية:</strong> {total_quantity} وحدة</p>
                        <p><strong>إجمالي القيمة:</strong> {total_value:.2f} جنيه</p>
                    </div>
                </body>
                </html>
                """

                # إنشاء مستند PDF
                document = QTextDocument()
                document.setHtml(html_content)

                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                document.print_(printer)

                self.show_success_message(f"تم تصدير PDF التفصيلي بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير PDF التفصيلي: {str(e)}")

    def export_custom(self):
        """تصدير مخصص للمخزون"""
        self.show_warning_message("ميزة التصدير المخصص قيد التطوير")

    def export_backup(self):
        """إنشاء نسخة احتياطية للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import json
            from datetime import datetime

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية",
                f"نسخة_احتياطية_مخزون_{format_datetime_for_filename()}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_items': len(items),
                        'version': '1.0'
                    },
                    'inventory': []
                }

                for item in items:
                    backup_data['inventory'].append({
                        'id': item.id,
                        'name': item.name,
                        'category': item.category,
                        'quantity': item.quantity,
                        'min_quantity': item.min_quantity,
                        'max_quantity': item.max_quantity,
                        'cost_price': item.cost_price,
                        'selling_price': item.selling_price,
                        'location': item.location,
                        'notes': item.notes
                    })

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import json

            # اختيار ملف النسخة الاحتياطية
            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختيار النسخة الاحتياطية",
                "", "JSON Files (*.json)"
            )

            if file_path:
                if show_confirmation_message("تأكيد الاستعادة",
                    "هل تريد استعادة النسخة الاحتياطية؟\nسيتم إضافة العناصر الجديدة فقط."):

                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    restored_count = 0
                    for item_data in backup_data['inventory']:
                        # التحقق من وجود العنصر
                        existing = self.session.query(Inventory).filter_by(id=item_data['id']).first()
                        if not existing:
                            # إنشاء عنصر جديد
                            new_item = Inventory(
                                name=item_data['name'],
                                category=item_data.get('category'),
                                unit=item_data.get('unit'),
                                quantity=item_data.get('quantity', 0),
                                min_quantity=item_data.get('min_quantity', 0),
                                max_quantity=item_data.get('max_quantity', 0),
                                cost_price=item_data.get('cost_price', 0),
                                selling_price=item_data.get('selling_price', 0),
                                location=item_data.get('location'),
                                notes=item_data.get('notes')
                            )
                            self.session.add(new_item)
                            restored_count += 1

                    self.session.commit()
                    self.load_data()  # تحديث الجدول

                    self.show_success_message(f"تم استعادة {restored_count} عنصر من النسخة الاحتياطية")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")


class InventoryInfoDialog(QDialog):
    """نافذة تفاصيل عنصر المخزون - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, item=None):
        super().__init__(parent)
        self.item = item

        # التحقق من وجود item
        if not self.item:
            raise ValueError("item مطلوب لإنشاء نافذة معلومات عنصر المخزون")
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية - مطابق تماماً للعملاء"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        item_name = self.item.name if self.item else "عنصر غير محدد"
        self.setWindowTitle("📦📋 معلومات عنصر المخزون - نظام إدارة المخزون المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل - مطابق للعملاء

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling())

        # ═══════════════════════════════════════════════════════════════
        # التخطيط الرئيسي المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # ═══════════════════════════════════════════════════════════════
        # عنوان النافذة المحسن - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        title_label = QLabel(f"📦 تفاصيل العنصر: {item_name}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 22px;
                font-weight: bold;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 18px 25px;
                margin: 8px 0px 20px 0px;
            }
        """)
        main_layout.addWidget(title_label)

        # ═══════════════════════════════════════════════════════════════
        # منطقة التمرير المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                background: transparent;
                padding: 5px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                width: 14px;
                border-radius: 7px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(168, 85, 247, 0.6));
                border-radius: 6px;
                min-height: 25px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.9),
                    stop:1 rgba(168, 85, 247, 0.8));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق للعملاء
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)  # زيادة المسافة بين الأقسام لاستغلال المساحة الإضافية

        # إضافة معلومات العنصر
        self.add_item_info(info_layout)

        scroll_area.setWidget(info_widget)
        main_layout.addWidget(scroll_area)

        # ═══════════════════════════════════════════════════════════════
        # أزرار التحكم المحسنة - مطابق للعملاء
        # ═══════════════════════════════════════════════════════════════
        self.create_control_buttons(main_layout)

        # تطبيق تصميم شريط العنوان
        self.apply_advanced_title_bar_styling()

    def create_window_icon(self):
        """إنشاء أيقونة النافذة - مطابق للعملاء"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
            from PyQt5.QtCore import Qt

            # إنشاء أيقونة بسيطة
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setBrush(QBrush(QColor(59, 130, 246)))
            painter.drawEllipse(4, 4, 24, 24)
            painter.end()

            return QIcon(pixmap)
        except Exception as e:
            print(f"خطأ في إنشاء أيقونة النافذة: {str(e)}")
            return QIcon()

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي - مطابق تماماً للعملاء"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """

    def add_item_info(self, layout):
        """إضافة معلومات العنصر إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.item:
            return

        # قسم المعلومات الأساسية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.item.id).zfill(8)}"),
            ("📦 اسم العنصر", self.item.name or "غير محدد"),
            ("🏷️ الفئة", self.item.category or "غير محدد"),
            ("📍 موقع التخزين", self.item.location or "غير محدد")
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم المعلومات المالية والكميات
        financial_info = [
            ("📊 الكمية الحالية", f"{getattr(self.item, 'quantity', 0):,.0f} وحدة"),
            ("⚠️ الحد الأدنى", f"{getattr(self.item, 'min_quantity', 0):,.0f} وحدة"),
            ("💰 سعر التكلفة", f"{getattr(self.item, 'cost_price', 0):,.0f} جنيه"),
            ("💵 سعر البيع", f"{getattr(self.item, 'selling_price', 0):,.0f} جنيه"),
            ("💎 قيمة المخزون", f"{self.get_stock_value():,.0f} جنيه")
        ]
        self.add_info_section(layout, "💰 المعلومات المالية والكميات", financial_info)

        # قسم تفاصيل إضافية
        additional_info = [
            ("🚛 المورد", self.item.supplier.name if self.item.supplier else "غير محدد"),
            ("📈 الربح المتوقع", f"{self.get_expected_profit():,.0f} جنيه"),
            ("📊 حالة المخزون", self.get_stock_status()),
            ("⏰ آخر تحديث", self.item.last_updated.strftime('%Y-%m-%d %H:%M') if hasattr(self.item, 'last_updated') and self.item.last_updated else "غير محدد"),
            ("📋 ملخص العنصر", self.get_item_summary())
        ]
        self.add_info_section(layout, "📊 تفاصيل إضافية", additional_info)

    def add_info_section(self, layout, title, info_list):
        """إضافة قسم معلومات بدون إطار رئيسي"""
        # حاوي القسم بدون إطار
        section_frame = QWidget()
        section_frame.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
                margin: 5px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المبسط
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(139, 92, 246, 0.15),
                    stop:1 rgba(34, 197, 94, 0.1));
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                margin-bottom: 8px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(title_label)

        # معلومات القسم
        for i, (label_text, value_text) in enumerate(info_list):
            info_frame = QWidget()
            info_frame.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.02 + (i % 2) * 0.01}),
                        stop:0.5 rgba(248, 250, 252, {0.03 + (i % 2) * 0.01}),
                        stop:1 rgba(241, 245, 249, {0.02 + (i % 2) * 0.01}));
                    border: none;
                    border-radius: 4px;
                    margin: 1px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.05),
                        stop:0.5 rgba(139, 92, 246, 0.04),
                        stop:1 rgba(34, 197, 94, 0.03));
                    border: none;
                }}
            """)

            info_layout = QHBoxLayout(info_frame)
            info_layout.setContentsMargins(12, 8, 12, 8)
            info_layout.setSpacing(15)

            # التسمية المحسنة
            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: none;
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة
            value = QLabel(str(value_text))
            value.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }
            """)
            value.setWordWrap(True)

            info_layout.addWidget(label)
            info_layout.addWidget(value, 1)

            section_layout.addWidget(info_frame)

        layout.addWidget(section_frame)

    def get_stock_value(self):
        """حساب قيمة المخزون"""
        quantity = getattr(self.item, 'quantity', 0) or 0
        cost_price = getattr(self.item, 'cost_price', 0) or 0
        return quantity * cost_price

    def get_expected_profit(self):
        """حساب الربح المتوقع"""
        quantity = getattr(self.item, 'quantity', 0) or 0
        cost_price = getattr(self.item, 'cost_price', 0) or 0
        selling_price = getattr(self.item, 'selling_price', 0) or 0
        return quantity * (selling_price - cost_price)

    def get_stock_status(self):
        """حالة المخزون"""
        quantity = getattr(self.item, 'quantity', 0) or 0
        min_quantity = getattr(self.item, 'min_quantity', 0) or 0

        if quantity <= 0:
            return "نفد المخزون ❌"
        elif quantity <= min_quantity:
            return "منخفض ⚠️"
        elif quantity <= min_quantity * 2:
            return "متوسط 🟡"
        else:
            return "جيد ✅"

    def get_item_summary(self):
        """ملخص العنصر"""
        item_name = self.item.name or "عنصر غير محدد"
        quantity = getattr(self.item, 'quantity', 0) or 0
        return f"{item_name} - {quantity:,.0f} وحدة متوفر"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;
                margin: 5px 0;
                min-height: 65px;
                max-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الإغلاق - في المقدمة
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)
        close_btn.setMaximumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)
        print_btn.setMaximumHeight(45)
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)
        export_pdf_btn.setMaximumHeight(45)
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر تعديل الكمية
        adjust_btn = QPushButton("📊 تعديل الكمية")
        adjust_btn.setMinimumWidth(200)
        adjust_btn.setMaximumHeight(45)
        self.style_advanced_button(adjust_btn, 'orange')
        adjust_btn.clicked.connect(self.adjust_quantity)

        # ترتيب الأزرار
        buttons_layout.addWidget(close_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(adjust_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                if button_type in colors:
                    primary, secondary = colors[button_type]
                    button.setStyleSheet(f"""
                        QPushButton {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            color: #FFFFFF;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            border-radius: 8px;
                            font-size: 15px;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                            padding: 10px 14px;
                        }}
                        QPushButton:hover {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {secondary}, stop:1 {primary});
                            border: 3px solid rgba(255, 255, 255, 0.5);
                            transform: translateY(-2px);
                        }}
                        QPushButton:pressed {{
                            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                stop:0 {primary}, stop:1 {secondary});
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            transform: translateY(1px);
                        }}
                    """)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {str(e)}")

    def print_info(self):
        """طباعة معلومات العنصر"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                item_name = self.item.name or "عنصر غير محدد"
                painter.drawText(100, y_position, f"تقرير عنصر المخزون: {item_name}")
                y_position += line_height * 2

                # تاريخ الطباعة
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات الأساسية:")
                y_position += line_height

                painter.setFont(normal_font)
                basic_info = [
                    f"المعرف: #{str(self.item.id).zfill(8)}",
                    f"اسم العنصر: {item_name}",
                    f"الفئة: {self.item.category or 'غير محدد'}",
                    f"موقع التخزين: {self.item.location or 'غير محدد'}"
                ]

                for info in basic_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                y_position += line_height

                # المعلومات المالية والكميات
                painter.setFont(header_font)
                painter.drawText(100, y_position, "المعلومات المالية والكميات:")
                y_position += line_height

                painter.setFont(normal_font)
                financial_info = [
                    f"الكمية الحالية: {getattr(self.item, 'quantity', 0):,.0f} وحدة",
                    f"الحد الأدنى: {getattr(self.item, 'min_quantity', 0):,.0f} وحدة",
                    f"سعر التكلفة: {getattr(self.item, 'cost_price', 0):,.0f} جنيه",
                    f"سعر البيع: {getattr(self.item, 'selling_price', 0):,.0f} جنيه",
                    f"قيمة المخزون: {self.get_stock_value():,.0f} جنيه"
                ]

                for info in financial_info:
                    painter.drawText(120, y_position, info)
                    y_position += line_height

                painter.end()
                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message("تم طباعة تفاصيل العنصر بنجاح")
                else:
                    show_info_message("نجح", "تم طباعة تفاصيل العنصر بنجاح")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"خطأ في الطباعة: {str(e)}")
            else:
                show_error_message("خطأ", f"خطأ في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات العنصر إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont
            from datetime import datetime
            import os

            # اختيار مكان الحفظ
            item_name = self.item.name or "عنصر_غير_محدد"
            default_filename = f"عنصر_مخزون_{item_name}_{self.item.id}.pdf"

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير عنصر المخزون",
                default_filename,
                "PDF Files (*.pdf)"
            )

            if file_path:
                printer = QPrinter()
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y_position = 100
                line_height = 50

                # عنوان التقرير
                painter.setFont(title_font)
                painter.drawText(100, y_position, f"تقرير عنصر المخزون: {item_name}")
                y_position += line_height * 2

                # تاريخ التصدير
                painter.setFont(normal_font)
                painter.drawText(100, y_position, f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                y_position += line_height * 2

                # المعلومات التفصيلية
                sections = [
                    ("المعلومات الأساسية", [
                        f"المعرف: #{str(self.item.id).zfill(8)}",
                        f"اسم العنصر: {item_name}",
                        f"الفئة: {self.item.category or 'غير محدد'}",
                        f"موقع التخزين: {self.item.location or 'غير محدد'}"
                    ]),
                    ("المعلومات المالية والكميات", [
                        f"الكمية الحالية: {getattr(self.item, 'quantity', 0):,.0f} وحدة",
                        f"الحد الأدنى: {getattr(self.item, 'min_quantity', 0):,.0f} وحدة",
                        f"سعر التكلفة: {getattr(self.item, 'cost_price', 0):,.0f} جنيه",
                        f"سعر البيع: {getattr(self.item, 'selling_price', 0):,.0f} جنيه",
                        f"قيمة المخزون: {self.get_stock_value():,.0f} جنيه"
                    ]),
                    ("تفاصيل إضافية", [
                        f"المورد: {self.item.supplier.name if self.item.supplier else 'غير محدد'}",
                        f"الربح المتوقع: {self.get_expected_profit():,.0f} جنيه",
                        f"حالة المخزون: {self.get_stock_status()}",
                        f"ملخص العنصر: {self.get_item_summary()}"
                    ])
                ]

                for section_title, section_info in sections:
                    painter.setFont(header_font)
                    painter.drawText(100, y_position, section_title + ":")
                    y_position += line_height

                    painter.setFont(normal_font)
                    for info in section_info:
                        painter.drawText(120, y_position, info)
                        y_position += line_height

                    y_position += line_height // 2

                painter.end()
                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم تصدير تقرير العنصر إلى:\n{file_path}")
                else:
                    show_info_message("نجح", f"تم تصدير تقرير العنصر إلى:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في التصدير: {str(e)}")
            else:
                show_error_message("خطأ", f"فشل في التصدير: {str(e)}")

    def adjust_quantity(self):
        """فتح نافذة تعديل الكمية"""
        try:
            from PyQt5.QtWidgets import QInputDialog

            current_quantity = getattr(self.item, 'quantity', 0) or 0
            new_quantity, ok = QInputDialog.getDouble(
                self,
                "تعديل الكمية",
                f"الكمية الحالية: {current_quantity:,.0f} وحدة\n\nأدخل الكمية الجديدة:",
                current_quantity,
                0,
                999999,
                0
            )

            if ok and new_quantity != current_quantity:
                self.item.quantity = new_quantity
                if self.parent_widget and hasattr(self.parent_widget, 'session'):
                    self.parent_widget.session.commit()
                    if hasattr(self.parent_widget, 'refresh_data'):
                        self.parent_widget.refresh_data()

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم تحديث الكمية إلى {new_quantity:,.0f} وحدة")
                else:
                    show_info_message("تم", f"تم تحديث الكمية إلى {new_quantity:,.0f} وحدة")
                self.setup_ui()  # تحديث النافذة

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في تعديل الكمية: {str(e)}")
            else:
                show_error_message("خطأ", f"فشل في تعديل الكمية: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {str(e)}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {str(e)}")


class InventoryStatisticsDialog(QDialog):
    """نافذة إحصائيات المخزون مطابقة للعملاء والموردين والعمال والمشاريع والعقارات"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent

        # التحقق من وجود session
        if not self.session:
            raise ValueError("session مطلوب لإنشاء نافذة إحصائيات المخزون")

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي
        self.setWindowTitle("📊 إحصائيات المخزون - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(520, 450)  # حجم أكبر قليلاً لاستيعاب الإحصائيات الجديدة

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)  # هوامش مضغوطة جداً
        layout.setSpacing(6)  # مسافات مضغوطة جداً

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 4px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(3)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات المخزون")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                padding: 4px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة المخزون والكميات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 13px;
                font-weight: normal;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                padding: 2px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(3)  # مسافات مضغوطة جداً
        stats_layout.setContentsMargins(8, 4, 8, 4)  # هوامش مضغوطة جداً

        # إنشاء قائمة الإحصائيات المحدثة والمتطورة
        stats_items = [
            ("📦", "إجمالي عناصر المخزون المسجلة", str(self.total_items), "#3B82F6", "📊"),
            ("📊", "إجمالي الكميات المتوفرة", str(self.total_quantity), "#10B981", "💚"),
            ("⚠️", "عناصر منخفضة المخزون", str(self.low_stock_items), "#F59E0B", "🟡"),
            ("🔴", "عناصر نفدت من المخزون", str(self.out_of_stock_items), "#EF4444", "🔴"),
            ("💰", "إجمالي القيمة التقديرية", format_currency(self.total_value), "#10B981", "⬆️"),
            ("🚀", "العناصر الأكثر ربحية", str(self.most_profitable_items), "#059669", "💎"),
            ("📉", "العناصر الأقل ربحية", str(self.least_profitable_items), "#DC2626", "⬇️"),
            ("⏰", "عناصر منتهية أو قريبة الصلاحية", str(self.expired_items), "#EF4444", "⚡"),
            ("📈", "عناصر تجاوزت الحد الأقصى", str(self.overstock_items), "#F59E0B", "🔺"),
            ("🏷️", "عدد فئات المخزون النشطة", str(self.active_categories), "#8B5CF6", "📂")
        ]

        for icon, title, value, color, secondary_icon in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 1px;
                    margin: 0px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 3px;
                }
            """)
            item_widget.setFixedHeight(28)  # ارتفاع مضغوط جداً

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(6)
            item_layout.setContentsMargins(6, 2, 6, 2)

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(20, 20)  # حجم أصغر جداً
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                QLabel {{
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px 3px;
                }}
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px 6px;
                    min-width: 50px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            # الأيقونة الثانوية
            secondary_icon_label = QLabel(secondary_icon)
            secondary_icon_label.setFixedSize(16, 16)
            secondary_icon_label.setAlignment(Qt.AlignCenter)
            secondary_icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px;
                }}
            """)
            item_layout.addWidget(secondary_icon_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(8, 4, 8, 4)

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        self.style_advanced_button(close_button, 'danger')

        # زر تصدير
        export_pdf_button = QPushButton("📄 تصدير")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        self.style_advanced_button(export_pdf_button, 'info')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب إحصائيات المخزون"""
        try:
            # حساب إجمالي العناصر
            self.total_items = self.session.query(Inventory).count()

            # حساب إجمالي الكميات
            total_quantity_result = self.session.query(func.sum(Inventory.quantity)).scalar()
            self.total_quantity = int(total_quantity_result or 0)

            # حساب العناصر منخفضة المخزون (أقل من 10)
            self.low_stock_items = self.session.query(Inventory).filter(Inventory.quantity < 10).count()

            # حساب العناصر النافدة (كمية = 0)
            self.out_of_stock_items = self.session.query(Inventory).filter(Inventory.quantity == 0).count()

            # حساب إجمالي القيمة (الكمية × سعر التكلفة)
            items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()
            self.total_value = sum((item.quantity or 0) * (item.cost_price or 0) for item in items)

            # حساب الإحصائيات الجديدة المتطورة
            self.calculate_advanced_statistics(items)

        except Exception as e:
            print(f"خطأ في حساب إحصائيات المخزون: {e}")
            self.total_items = 0
            self.total_quantity = 0
            self.low_stock_items = 0
            self.out_of_stock_items = 0
            self.total_value = 0
            # تعيين قيم افتراضية للإحصائيات الجديدة
            self.most_profitable_items = 0
            self.least_profitable_items = 0
            self.expired_items = 0
            self.overstock_items = 0
            self.active_categories = 0

    def calculate_advanced_statistics(self, items):
        """حساب الإحصائيات المتطورة للمخزون"""
        try:
            from datetime import datetime, timedelta

            # 1. حساب العناصر الأكثر ربحية (هامش ربح > 30%)
            profitable_items = []
            for item in items:
                if item.cost_price and item.selling_price:
                    profit_margin = ((item.selling_price - item.cost_price) / item.cost_price) * 100
                    if profit_margin > 30:  # هامش ربح أكثر من 30%
                        profitable_items.append(item)
            self.most_profitable_items = len(profitable_items)

            # 2. حساب العناصر الأقل ربحية (هامش ربح < 10%)
            low_profit_items = []
            for item in items:
                if item.cost_price and item.selling_price:
                    profit_margin = ((item.selling_price - item.cost_price) / item.cost_price) * 100
                    if profit_margin < 10:  # هامش ربح أقل من 10%
                        low_profit_items.append(item)
            self.least_profitable_items = len(low_profit_items)

            # 3. حساب العناصر منتهية أو قريبة الصلاحية (خلال 30 يوم)
            current_date = datetime.now().date()
            warning_date = current_date + timedelta(days=30)
            expired_count = 0
            for item in items:
                if hasattr(item, 'expiry_date') and item.expiry_date:
                    if item.expiry_date <= warning_date:
                        expired_count += 1
            self.expired_items = expired_count

            # 4. حساب العناصر التي تجاوزت الحد الأقصى للمخزون
            overstock_count = 0
            for item in items:
                max_stock = getattr(item, 'max_stock', None) or 1000  # افتراضي 1000
                if item.quantity and item.quantity > max_stock:
                    overstock_count += 1
            self.overstock_items = overstock_count

            # 5. حساب عدد الفئات النشطة (التي تحتوي على عناصر)
            categories = set()
            for item in items:
                if hasattr(item, 'category') and item.category and item.quantity and item.quantity > 0:
                    categories.add(item.category)
            self.active_categories = len(categories)

        except Exception as e:
            print(f"خطأ في حساب الإحصائيات المتطورة: {e}")
            # تعيين قيم افتراضية في حالة الخطأ
            self.most_profitable_items = 0
            self.least_profitable_items = 0
            self.expired_items = 0
            self.overstock_items = 0
            self.active_categories = 0

    def export_statistics_to_pdf(self):
        """تصدير إحصائيات المخزون إلى ملف PDF باللغة العربية"""
        try:
            # التأكد من حساب الإحصائيات أولاً
            self.calculate_statistics()

            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from PyQt5.QtWidgets import QFileDialog
            import os

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير إحصائيات المخزون",
                f"تقرير_إحصائيات_المخزون_{self.format_datetime_for_filename()}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return

            # دالة لإصلاح النص العربي
            def fix_arabic_text(text):
                """إصلاح النص العربي للعرض الصحيح في PDF"""
                try:
                    if isinstance(text, str):
                        # استخدام arabic-reshaper لإصلاح شكل الأحرف العربية
                        import arabic_reshaper
                        from bidi.algorithm import get_display

                        # إعادة تشكيل النص العربي
                        reshaped_text = arabic_reshaper.reshape(text)
                        # إصلاح اتجاه النص
                        bidi_text = get_display(reshaped_text)

                        return bidi_text
                    return text
                except ImportError:
                    # إذا لم تكن المكتبات متاحة، استخدم النص كما هو
                    return text
                except Exception:
                    return text

            # إنشاء المستند مع حجم مخصص للمحتوى
            doc = SimpleDocTemplate(
                file_path,
                pagesize=letter,
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=20,
                title="تقرير إحصائيات المخزون"
            )

            # قائمة العناصر
            story = []
            styles = getSampleStyleSheet()

            # تسجيل خط عربي (إذا كان متاحاً)
            try:
                # محاولة استخدام خط عربي
                arabic_font_path = "C:/Windows/Fonts/arial.ttf"  # خط Arial يدعم العربية
                if os.path.exists(arabic_font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'

            # العنوان الرئيسي
            title_style = ParagraphStyle(
                'ArabicTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=20,
                alignment=1,  # وسط
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("📦 تقرير إحصائيات المخزون"), title_style))
            story.append(Spacer(1, 8))

            # معلومات التقرير
            info_style = ParagraphStyle(
                'ArabicInfo',
                parent=styles['Normal'],
                fontSize=10,
                alignment=1,
                textColor=colors.grey,
                fontName=font_name,
                spaceAfter=5
            )

            story.append(Paragraph(fix_arabic_text(f"تاريخ التقرير: {self.format_datetime_for_export()}"), info_style))
            story.append(Spacer(1, 12))

            # عنوان قسم الإحصائيات العامة
            section_style = ParagraphStyle(
                'SectionTitle',
                parent=styles['Heading2'],
                fontSize=13,
                spaceAfter=8,
                alignment=2,  # يمين للعربية
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("📈 الإحصائيات الأساسية"), section_style))
            story.append(Spacer(1, 5))

            # إنشاء جدول الإحصائيات المختصر
            data = [
                # عكس ترتيب الأعمدة للعربية
                [fix_arabic_text('الوصف'), fix_arabic_text('القيمة'), fix_arabic_text('البيان')],
                [fix_arabic_text('العدد الكلي للعناصر'), str(self.total_items), fix_arabic_text('📦 إجمالي العناصر')],
                [fix_arabic_text('إجمالي الكميات'), str(self.total_quantity), fix_arabic_text('📊 إجمالي الكميات')],
                [fix_arabic_text('عناصر منخفضة المخزون'), str(self.low_stock_items), fix_arabic_text('⚠️ منخفضة المخزون')],
                [fix_arabic_text('عناصر نافدة'), str(self.out_of_stock_items), fix_arabic_text('🔴 عناصر نافدة')],
                [fix_arabic_text('إجمالي القيمة'), f"{self.total_value:,.0f} جنيه", fix_arabic_text('💰 إجمالي القيمة')],
                [fix_arabic_text('العناصر الأكثر ربحية'), str(getattr(self, 'most_profitable_items', 0)), fix_arabic_text('🚀 الأكثر ربحية')],
                [fix_arabic_text('العناصر الأقل ربحية'), str(getattr(self, 'least_profitable_items', 0)), fix_arabic_text('📉 الأقل ربحية')],
                [fix_arabic_text('عناصر منتهية الصلاحية'), str(getattr(self, 'expired_items', 0)), fix_arabic_text('⏰ منتهية الصلاحية')],
                [fix_arabic_text('عناصر تجاوزت الحد الأقصى'), str(getattr(self, 'overstock_items', 0)), fix_arabic_text('📈 تجاوزت الحد الأقصى')],
                [fix_arabic_text('عدد الفئات النشطة'), str(getattr(self, 'active_categories', 0)), fix_arabic_text('🏷️ الفئات النشطة')]
            ]

            # إنشاء الجدول مع ترتيب معكوس للعربية
            table = Table(data, colWidths=[2.5*inch, 1.5*inch, 2*inch])
            table.setStyle(TableStyle([
                # تصميم الرأس
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),  # محاذاة يمين للعربية
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                # تلوين الصفوف بالتناوب
                ('BACKGROUND', (0, 1), (-1, 1), colors.lightgrey),
                ('BACKGROUND', (0, 3), (-1, 3), colors.lightgrey),
                ('BACKGROUND', (0, 5), (-1, 5), colors.lightgrey),
                ('BACKGROUND', (0, 7), (-1, 7), colors.lightgrey),
                ('BACKGROUND', (0, 9), (-1, 9), colors.lightgrey),
                ('BACKGROUND', (0, 11), (-1, 11), colors.lightgrey),
            ]))

            story.append(table)
            story.append(Spacer(1, 20))

            # إضافة ملاحظة في النهاية
            note_style = ParagraphStyle(
                'Note',
                parent=styles['Normal'],
                fontSize=8,
                alignment=1,
                textColor=colors.grey,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة المخزون"), note_style))

            # بناء المستند
            doc.build(story)

            # إظهار رسالة نجاح
            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message(f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            print(f"خطأ في تصدير التقرير: {e}")
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في تصدير التقرير: {str(e)}")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير التقرير: {str(e)}")

    def format_datetime_for_filename(self):
        """تنسيق التاريخ والوقت لاسم الملف"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    def format_datetime_for_export(self):
        """تنسيق التاريخ والوقت للتصدير"""
        from datetime import datetime
        return datetime.now().strftime("%Y/%m/%d %H:%M:%S")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")


class AdjustQuantityDialog(QDialog):
    """نافذة تعديل الكمية المبسطة - مطابقة لنافذة الإحصائيات"""

    def __init__(self, parent=None, item=None, session=None):
        super().__init__(parent)
        self.item = item
        self.session = session
        self.parent_widget = parent

        # التحقق من وجود المعاملات المطلوبة
        if not self.session:
            raise ValueError("session مطلوب لإنشاء نافذة تعديل الكمية")
        if not self.item:
            raise ValueError("item مطلوب لإنشاء نافذة تعديل الكمية")

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle(f"📊 تعديل كمية العنصر - {self.item.name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية النافذة مطابقة للإحصائيات
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # تخطيط النافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # العنوان الرئيسي
        main_title = QLabel("📊 تعديل كمية العنصر")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 24px;
                font-weight: bold;
                background: transparent;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
                padding: 8px;
            }
        """)

        # العنوان الفرعي
        subtitle = QLabel(f"العنصر: {self.item.name}")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 4px;
            }
        """)

        layout.addWidget(main_title)
        layout.addWidget(subtitle)

        # معلومات العنصر
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 12px;
                padding: 12px;
                margin: 5px;
            }
        """)

        info_layout = QVBoxLayout(info_frame)

        # الكمية الحالية
        current_quantity_label = QLabel(f"الكمية الحالية: {self.item.quantity:,.0f} وحدة")
        current_quantity_label.setAlignment(Qt.AlignCenter)
        current_quantity_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 6px;
            }
        """)
        info_layout.addWidget(current_quantity_label)

        layout.addWidget(info_frame)

        # حقل الكمية الجديدة
        edit_frame = QFrame()
        edit_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 12px;
                margin: 5px;
            }
        """)

        edit_layout = QHBoxLayout(edit_frame)

        quantity_label = QLabel("الكمية الجديدة:")
        quantity_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        self.quantity_spinbox = QDoubleSpinBox()
        self.quantity_spinbox.setRange(0, 999999)
        self.quantity_spinbox.setDecimals(0)
        self.quantity_spinbox.setValue(self.item.quantity or 0)
        self.quantity_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                font-weight: bold;
                color: #1f2937;
                min-height: 20px;
            }
            QDoubleSpinBox:focus {
                border: 2px solid #3b82f6;
                background: rgba(255, 255, 255, 1.0);
            }
        """)

        unit_label = QLabel('وحدة')
        unit_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                background: transparent;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        edit_layout.addWidget(quantity_label)
        edit_layout.addWidget(self.quantity_spinbox)
        edit_layout.addWidget(unit_label)

        layout.addWidget(edit_frame)

        # أزرار الإجراءات - مطابقة تماماً للإحصائيات
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 8px;
                margin: 2px;
            }
        """)

        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # الصف الأول - أزرار التعديل السريع
        quick_buttons_layout = QHBoxLayout()
        quick_buttons_layout.setSpacing(10)

        # زر +10
        add_10_button = QPushButton("➕ +10")
        add_10_button.clicked.connect(lambda: self.adjust_quantity(10))
        add_10_button.setMinimumHeight(45)
        self.style_statistics_button(add_10_button, 'emerald')

        # زر +100
        add_100_button = QPushButton("⬆️ +100")
        add_100_button.clicked.connect(lambda: self.adjust_quantity(100))
        add_100_button.setMinimumHeight(45)
        self.style_statistics_button(add_100_button, 'info')

        # زر -10
        sub_10_button = QPushButton("➖ -10")
        sub_10_button.clicked.connect(lambda: self.adjust_quantity(-10))
        sub_10_button.setMinimumHeight(45)
        self.style_statistics_button(sub_10_button, 'warning')

        # زر -100
        sub_100_button = QPushButton("⬇️ -100")
        sub_100_button.clicked.connect(lambda: self.adjust_quantity(-100))
        sub_100_button.setMinimumHeight(45)
        self.style_statistics_button(sub_100_button, 'danger')

        quick_buttons_layout.addWidget(add_10_button)
        quick_buttons_layout.addWidget(add_100_button)
        quick_buttons_layout.addWidget(sub_10_button)
        quick_buttons_layout.addWidget(sub_100_button)

        # الصف الثاني - أزرار الحفظ والإلغاء (مطابق لنوافذ العملاء)
        main_buttons_layout = QHBoxLayout()
        main_buttons_layout.setSpacing(10)  # مسافة مناسبة

        # زر الإلغاء (يأتي أولاً مثل نوافذ العملاء)
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.clicked.connect(self.close)
        cancel_button.setMinimumHeight(45)
        self.style_client_button(cancel_button, 'danger')  # أحمر مثل نوافذ العملاء

        # زر الحفظ (يأتي ثانياً مثل نوافذ العملاء)
        save_button = QPushButton("💾 حفظ")
        save_button.clicked.connect(self.save_changes)
        save_button.setMinimumHeight(45)
        self.style_client_button(save_button, 'emerald')  # أخضر مثل نوافذ العملاء

        # الترتيب مطابق لنوافذ العملاء: إلغاء ثم حفظ
        main_buttons_layout.addWidget(cancel_button)
        main_buttons_layout.addWidget(save_button)

        # إضافة الصفوف للإطار
        buttons_layout.addLayout(quick_buttons_layout)
        buttons_layout.addLayout(main_buttons_layout)

        layout.addWidget(buttons_frame)

    def adjust_quantity(self, amount):
        """تعديل سريع للكمية"""
        current_value = self.quantity_spinbox.value()
        new_value = max(0, current_value + amount)  # لا تسمح بالقيم السالبة
        self.quantity_spinbox.setValue(new_value)

    def save_changes(self):
        """حفظ التغييرات"""
        try:
            new_quantity = self.quantity_spinbox.value()
            old_quantity = self.item.quantity or 0

            if new_quantity == old_quantity:
                if hasattr(self.parent_widget, 'show_warning_message'):
                    self.parent_widget.show_warning_message("لم يتم تغيير الكمية")
                else:
                    show_info_message("تنبيه", "لم يتم تغيير الكمية")
                return

            # تحديث الكمية
            self.item.quantity = new_quantity
            self.item.last_updated = datetime.datetime.now()

            # حفظ في قاعدة البيانات
            self.session.commit()

            # رسالة نجاح
            change_text = f"من {old_quantity:,.0f} إلى {new_quantity:,.0f}"
            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message(f"تم تحديث كمية العنصر '{self.item.name}' بنجاح\n\n{change_text} وحدة")
            else:
                show_info_message("تم الحفظ", f"تم تحديث كمية العنصر '{self.item.name}' بنجاح\n\n{change_text} وحدة")

            # تحديث الجدول في النافذة الرئيسية
            if self.parent_widget and hasattr(self.parent_widget, 'refresh_data'):
                self.parent_widget.refresh_data()

            self.close()

        except Exception as e:
            self.session.rollback()
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في حفظ التعديل:\n{str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في حفظ التعديل:\n{str(e)}")

    def style_client_button(self, button, button_type):
        """تطبيق تصميم الأزرار المطابق تماماً لنوافذ العملاء"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة (مثل نوافذ العملاء)
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                # تصميم مطابق لنوافذ العملاء
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6',
                    'warning': '#f59e0b',
                    'secondary': '#6b7280',
                    'cyan': '#06b6d4'
                }

                color = colors.get(button_type, '#6b7280')

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}, stop:0.5 {color}dd, stop:1 {color}bb);
                        color: white;
                        border: 2px solid {color};
                        border-radius: 10px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}ee, stop:0.5 {color}cc, stop:1 {color}aa);
                        border: 3px solid {color};
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color}aa, stop:0.5 {color}88, stop:1 {color}66);
                        transform: translateY(1px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def style_statistics_button(self, button, button_type):
        """تطبيق تصميم الأزرار المطابق تماماً لنافذة الإحصائيات"""
        try:
            # ألوان مطابقة للإحصائيات
            colors = {
                'emerald': '#10b981',
                'danger': '#ef4444',
                'info': '#3b82f6',
                'warning': '#f59e0b',
                'secondary': '#6b7280',
                'cyan': '#06b6d4'
            }

            color = colors.get(button_type, '#6b7280')

            # تصميم مطابق تماماً للإحصائيات
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}, stop:0.5 {color}dd, stop:1 {color}bb);
                    color: white;
                    border: 2px solid {color};
                    border-radius: 10px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}ee, stop:0.5 {color}cc, stop:1 {color}aa);
                    border: 3px solid {color};
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}aa, stop:0.5 {color}88, stop:1 {color}66);
                    transform: translateY(1px);
                }}
            """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار مع ألوان متنوعة"""
        try:
            # ألوان متنوعة للأزرار المختلفة
            colors = {
                'emerald': '#10b981',    # أخضر للإضافة
                'danger': '#ef4444',     # أحمر للحذف
                'info': '#3b82f6',       # أزرق للمعلومات
                'warning': '#f59e0b',    # برتقالي للتحذير
                'secondary': '#6b7280',  # رمادي للإلغاء
                'cyan': '#06b6d4',       # سيان للحفظ
                'purple': '#8b5cf6',     # بنفسجي
                'pink': '#ec4899',       # وردي
                'indigo': '#6366f1'      # نيلي
            }

            color = colors.get(button_type, '#6b7280')

            # تصميم متطور مع تأثيرات بصرية
            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}, stop:0.5 {color}dd, stop:1 {color}bb);
                    color: white;
                    border: 2px solid {color};
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}ee, stop:0.5 {color}cc, stop:1 {color}aa);
                    border: 2px solid {color}ff;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color}aa, stop:0.5 {color}88, stop:1 {color}66);
                    transform: translateY(1px);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }}
            """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

    # دوال التصدير المتقدمة الجديدة
    def export_excel_advanced(self):
        """تصدير Excel متقدم للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", f"مخزون_excel_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تصدير Excel متقدم للمخزون'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي العناصر: {len(items)}'])
                    writer.writerow([])

                    # رؤوس الأعمدة
                    writer.writerow(['الرقم', 'اسم العنصر', 'الفئة', 'الكمية', 'سعر التكلفة', 'سعر البيع', 'الربح', 'المورد', 'الموقع'])

                    # البيانات
                    for item in items:
                        supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                        profit = (item.selling_price or 0) - (item.cost_price or 0)

                        writer.writerow([
                            item.id,
                            item.name or 'غير محدد',
                            item.category or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            f"{item.cost_price:.2f}" if item.cost_price else '0.00',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{profit:.2f}",
                            supplier_name,
                            item.location or 'غير محدد'
                        ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم تصدير Excel بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم تصدير Excel بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في تصدير Excel: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في تصدير Excel: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", f"مخزون_csv_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير مع إحصائيات
                    writer.writerow(['تصدير CSV شامل للمخزون'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])

                    # إحصائيات سريعة
                    total_value = sum((item.selling_price or 0) * (item.quantity or 0) for item in items)
                    total_cost = sum((item.cost_price or 0) * (item.quantity or 0) for item in items)
                    total_profit = total_value - total_cost
                    low_stock_count = len([item for item in items if (item.quantity or 0) <= (item.min_quantity or 0)])

                    writer.writerow([f'إجمالي العناصر: {len(items)}'])
                    writer.writerow([f'إجمالي قيمة المخزون: {total_value:.2f} جنيه'])
                    writer.writerow([f'إجمالي تكلفة المخزون: {total_cost:.2f} جنيه'])
                    writer.writerow([f'إجمالي الربح المتوقع: {total_profit:.2f} جنيه'])
                    writer.writerow([f'عناصر تحت الحد الأدنى: {low_stock_count}'])
                    writer.writerow([])

                    # رؤوس الأعمدة الشاملة
                    writer.writerow(['الرقم', 'اسم العنصر', 'الفئة', 'الكمية', 'الحد الأدنى', 'سعر التكلفة', 'سعر البيع', 'الربح للوحدة', 'إجمالي القيمة', 'المورد', 'الموقع', 'حالة المخزون'])

                    # البيانات الشاملة
                    for item in items:
                        supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                        profit_per_unit = (item.selling_price or 0) - (item.cost_price or 0)
                        total_item_value = (item.selling_price or 0) * (item.quantity or 0)

                        # تحديد حالة المخزون
                        if (item.quantity or 0) <= 0:
                            stock_status = 'نفد المخزون'
                        elif (item.quantity or 0) <= (item.min_quantity or 0):
                            stock_status = 'تحت الحد الأدنى'
                        else:
                            stock_status = 'متوفر'

                        writer.writerow([
                            item.id,
                            item.name or 'غير محدد',
                            item.category or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            f"{item.min_quantity:.0f}" if item.min_quantity else '0',
                            f"{item.cost_price:.2f}" if item.cost_price else '0.00',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{profit_per_unit:.2f}",
                            f"{total_item_value:.2f}",
                            supplier_name,
                            item.location or 'غير محدد',
                            stock_status
                        ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم تصدير CSV بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم تصدير CSV بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في تصدير CSV: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في تصدير CSV: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للمخزون"""
        if hasattr(self.parent_widget, 'show_warning_message'):
            self.parent_widget.show_warning_message("ميزة تصدير PDF المتقدم قيد التطوير")
        else:
            show_info_message("قريباً", "ميزة تصدير PDF المتقدم قيد التطوير")

    def export_stock_report(self):
        """تقرير المخزون المتقدم"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون", f"تقرير_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير المخزون المتقدم'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # تحليل الفئات
                    categories = {}
                    for item in items:
                        category = item.category or 'غير محدد'
                        if category not in categories:
                            categories[category] = {'count': 0, 'total_value': 0, 'total_quantity': 0}
                        categories[category]['count'] += 1
                        categories[category]['total_value'] += (item.selling_price or 0) * (item.quantity or 0)
                        categories[category]['total_quantity'] += item.quantity or 0

                    writer.writerow(['تحليل الفئات:'])
                    writer.writerow(['الفئة', 'عدد العناصر', 'إجمالي الكمية', 'إجمالي القيمة'])

                    for category, data in sorted(categories.items(), key=lambda x: x[1]['total_value'], reverse=True):
                        writer.writerow([
                            category,
                            data['count'],
                            f"{data['total_quantity']:.0f}",
                            f"{data['total_value']:.2f}"
                        ])

                    writer.writerow([])

                    # العناصر الأكثر قيمة
                    writer.writerow(['أعلى 10 عناصر قيمة:'])
                    writer.writerow(['اسم العنصر', 'الكمية', 'سعر البيع', 'إجمالي القيمة'])

                    top_items = sorted(items, key=lambda x: (x.selling_price or 0) * (x.quantity or 0), reverse=True)[:10]
                    for item in top_items:
                        total_value = (item.selling_price or 0) * (item.quantity or 0)
                        writer.writerow([
                            item.name or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{total_value:.2f}"
                        ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء تقرير المخزون بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم إنشاء تقرير المخزون بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إنشاء تقرير المخزون: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير المخزون: {str(e)}")

    def export_low_stock_report(self):
        """تقرير النواقص والعناصر تحت الحد الأدنى"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير النواقص", f"تقرير_نواقص_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # العناصر تحت الحد الأدنى أو نفدت
                low_stock_items = self.session.query(Inventory).filter(
                    (Inventory.quantity <= Inventory.min_quantity) | (Inventory.quantity <= 0)
                ).order_by(Inventory.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير النواقص والعناصر تحت الحد الأدنى'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # إحصائيات النواقص
                    out_of_stock = [item for item in low_stock_items if (item.quantity or 0) <= 0]
                    low_stock = [item for item in low_stock_items if (item.quantity or 0) > 0 and (item.quantity or 0) <= (item.min_quantity or 0)]

                    writer.writerow(['إحصائيات النواقص:'])
                    writer.writerow([f'إجمالي العناصر المتأثرة: {len(low_stock_items)}'])
                    writer.writerow([f'عناصر نفد مخزونها: {len(out_of_stock)}'])
                    writer.writerow([f'عناصر تحت الحد الأدنى: {len(low_stock)}'])
                    writer.writerow([])

                    if out_of_stock:
                        writer.writerow(['العناصر التي نفد مخزونها:'])
                        writer.writerow(['اسم العنصر', 'الفئة', 'الكمية الحالية', 'الحد الأدنى', 'المورد', 'الموقع'])

                        for item in out_of_stock:
                            supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                            writer.writerow([
                                item.name or 'غير محدد',
                                item.category or 'غير محدد',
                                f"{item.quantity:.0f}" if item.quantity else '0',
                                f"{item.min_quantity:.0f}" if item.min_quantity else '0',
                                supplier_name,
                                item.location or 'غير محدد'
                            ])
                        writer.writerow([])

                    if low_stock:
                        writer.writerow(['العناصر تحت الحد الأدنى:'])
                        writer.writerow(['اسم العنصر', 'الفئة', 'الكمية الحالية', 'الحد الأدنى', 'الكمية المطلوبة', 'المورد', 'الموقع'])

                        for item in low_stock:
                            supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                            required_qty = (item.min_quantity or 0) - (item.quantity or 0)
                            writer.writerow([
                                item.name or 'غير محدد',
                                item.category or 'غير محدد',
                                f"{item.quantity:.0f}" if item.quantity else '0',
                                f"{item.min_quantity:.0f}" if item.min_quantity else '0',
                                f"{required_qty:.0f}" if required_qty > 0 else '0',
                                supplier_name,
                                item.location or 'غير محدد'
                            ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء تقرير النواقص بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم إنشاء تقرير النواقص بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إنشاء تقرير النواقص: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير النواقص: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار - مطابق للأقسام الأخرى"""
        try:
            # استخدام دالة التصميم الموحدة
            UnifiedStyles.apply_advanced_button_style(button, button_type, has_menu)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")
            # تطبيق تصميم بديل بسيط
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: #3b82f6;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #2563eb;
                }}
            """)

    def export_valuation_report(self):
        """تقرير التقييم المالي للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير التقييم", f"تقرير_تقييم_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # عنوان التقرير
                    writer.writerow(['تقرير التقييم المالي للمخزون'])
                    writer.writerow([f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([])

                    # الإحصائيات المالية
                    total_cost_value = sum((item.cost_price or 0) * (item.quantity or 0) for item in items)
                    total_selling_value = sum((item.selling_price or 0) * (item.quantity or 0) for item in items)
                    total_profit = total_selling_value - total_cost_value
                    profit_margin = (total_profit / total_selling_value * 100) if total_selling_value > 0 else 0

                    writer.writerow(['الإحصائيات المالية:'])
                    writer.writerow([f'إجمالي تكلفة المخزون: {total_cost_value:.2f} جنيه'])
                    writer.writerow([f'إجمالي قيمة البيع: {total_selling_value:.2f} جنيه'])
                    writer.writerow([f'إجمالي الربح المتوقع: {total_profit:.2f} جنيه'])
                    writer.writerow([f'هامش الربح: {profit_margin:.1f}%'])
                    writer.writerow([])

                    # تفاصيل التقييم
                    writer.writerow(['تفاصيل التقييم:'])
                    writer.writerow(['اسم العنصر', 'الكمية', 'تكلفة الوحدة', 'سعر البيع', 'إجمالي التكلفة', 'إجمالي البيع', 'الربح', 'هامش الربح%'])

                    for item in sorted(items, key=lambda x: ((x.selling_price or 0) - (x.cost_price or 0)) * (x.quantity or 0), reverse=True):
                        cost_total = (item.cost_price or 0) * (item.quantity or 0)
                        selling_total = (item.selling_price or 0) * (item.quantity or 0)
                        profit = selling_total - cost_total
                        margin = (profit / selling_total * 100) if selling_total > 0 else 0

                        writer.writerow([
                            item.name or 'غير محدد',
                            f"{item.quantity:.0f}" if item.quantity else '0',
                            f"{item.cost_price:.2f}" if item.cost_price else '0.00',
                            f"{item.selling_price:.2f}" if item.selling_price else '0.00',
                            f"{cost_total:.2f}",
                            f"{selling_total:.2f}",
                            f"{profit:.2f}",
                            f"{margin:.1f}%"
                        ])

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء تقرير التقييم بنجاح:\n{file_path}")
                else:
                    show_info_message("تم", f"تم إنشاء تقرير التقييم بنجاح:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إنشاء تقرير التقييم: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إنشاء تقرير التقييم: {str(e)}")

    def export_custom(self):
        """تصدير مخصص للمخزون مع خيارات متقدمة - مطابق تماماً للعملاء"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات - مطابق للعملاء
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(400, 380)

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للنافذة الحوارية: {e}")

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي مضغوط
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(15, 10, 15, 10)
            layout.setSpacing(8)

            # العنوان الرئيسي مضغوط
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(3)

            # الأيقونة والعنوان الرئيسي مضغوط
            main_title = QLabel("🔧 تصدير مخصص للمخزون")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 22px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 5px;
                }
            """)

            # العنوان الفرعي التوضيحي مضغوط
            subtitle = QLabel("اختر البيانات المراد تصديرها بدقة")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # مجموعة البيانات الأساسية مطابقة لنافذة الإحصائيات
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)
            basic_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            basic_title = QLabel("📋 البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)

            self.export_id = QCheckBox("🆔 الرقم التعريفي")
            self.export_name = QCheckBox("📦 اسم العنصر")
            self.export_category = QCheckBox("🏷️ الفئة")
            self.export_quantity = QCheckBox("📊 الكمية")

            # تحديد افتراضي
            self.export_name.setChecked(True)
            self.export_quantity.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات
            checkboxes_data = [
                (self.export_id, "#3B82F6"),
                (self.export_name, "#10B981"),
                (self.export_category, "#F59E0B"),
                (self.export_quantity, "#EF4444")
            ]

            for checkbox, color in checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)
                item_layout.setContentsMargins(8, 3, 8, 3)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:  # محدد
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)
                item_layout.addWidget(checkbox)
                basic_layout.addWidget(item_widget)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)

            # مجموعة البيانات المالية
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)
            financial_main_layout.setContentsMargins(15, 5, 15, 5)

            financial_title = QLabel("💰 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)

            self.export_cost_price = QCheckBox("💰 سعر التكلفة")
            self.export_selling_price = QCheckBox("💵 سعر البيع")
            self.export_profit = QCheckBox("📈 الربح")

            self.export_selling_price.setChecked(True)

            financial_checkboxes_data = [
                (self.export_cost_price, "#F59E0B"),
                (self.export_selling_price, "#10B981"),
                (self.export_profit, "#EF4444")
            ]

            for checkbox, color in financial_checkboxes_data:
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)
                item_layout.addWidget(checkbox)
                financial_layout.addWidget(item_widget)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)

            # مجموعة البيانات الإضافية
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)
            additional_main_layout.setContentsMargins(15, 5, 15, 5)

            additional_title = QLabel("📝 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)

            self.export_supplier = QCheckBox("🚛 المورد")
            self.export_location = QCheckBox("📍 الموقع")
            self.export_min_quantity = QCheckBox("⚠️ الحد الأدنى")
            self.export_statistics = QCheckBox("📊 إضافة الإحصائيات")

            additional_checkboxes_data = [
                (self.export_supplier, "#8B5CF6"),
                (self.export_location, "#06B6D4"),
                (self.export_min_quantity, "#F59E0B"),
                (self.export_statistics, "#F97316")
            ]

            for checkbox, color in additional_checkboxes_data:
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                def create_toggle_function(label):
                    def toggle_check(state):
                        if state == 2:
                            label.setText("✓")
                        else:
                            label.setText("")
                    return toggle_check

                checkbox.stateChanged.connect(create_toggle_function(check_label))

                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)
                item_layout.addWidget(checkbox)
                additional_layout.addWidget(item_widget)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

            # أزرار التحكم مطابقة لنافذة الإحصائيات
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            # زر الإلغاء مطابق للبرنامج الرئيسي
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            cancel_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export_inventory(dialog))
            export_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def perform_custom_export_inventory(self, dialog):
        """تنفيذ التصدير المخصص للمخزون - مطابق للعملاء"""
        try:
            import csv
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إضافة الإحصائيات إذا تم اختيارها
                    if self.export_statistics.isChecked():
                        writer.writerow(['تصدير مخصص للمخزون'])
                        writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                        writer.writerow([f'إجمالي العناصر: {len(items)}'])
                        writer.writerow([])

                    # إنشاء رؤوس الأعمدة حسب الاختيار
                    headers = []
                    if self.export_id.isChecked():
                        headers.append('الرقم التعريفي')
                    if self.export_name.isChecked():
                        headers.append('اسم العنصر')
                    if self.export_category.isChecked():
                        headers.append('الفئة')
                    if self.export_quantity.isChecked():
                        headers.append('الكمية')
                    if self.export_cost_price.isChecked():
                        headers.append('سعر التكلفة')
                    if self.export_selling_price.isChecked():
                        headers.append('سعر البيع')
                    if self.export_profit.isChecked():
                        headers.append('الربح')
                    if self.export_supplier.isChecked():
                        headers.append('المورد')
                    if self.export_location.isChecked():
                        headers.append('الموقع')
                    if self.export_min_quantity.isChecked():
                        headers.append('الحد الأدنى')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for item in items:
                        row = []
                        if self.export_id.isChecked():
                            row.append(item.id)
                        if self.export_name.isChecked():
                            row.append(item.name or 'غير محدد')
                        if self.export_category.isChecked():
                            row.append(item.category or 'غير محدد')
                        if self.export_quantity.isChecked():
                            row.append(f"{item.quantity:.0f}" if item.quantity else '0')
                        if self.export_cost_price.isChecked():
                            row.append(f"{item.cost_price:.2f}" if item.cost_price else '0.00')
                        if self.export_selling_price.isChecked():
                            row.append(f"{item.selling_price:.2f}" if item.selling_price else '0.00')
                        if self.export_profit.isChecked():
                            profit = (item.selling_price or 0) - (item.cost_price or 0)
                            row.append(f"{profit:.2f}")
                        if self.export_supplier.isChecked():
                            row.append(item.supplier.name if item.supplier else 'غير محدد')
                        if self.export_location.isChecked():
                            row.append(item.location or 'غير محدد')
                        if self.export_min_quantity.isChecked():
                            row.append(f"{item.min_quantity:.0f}" if item.min_quantity else '0')

                        writer.writerow(row)

                dialog.accept()
                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم التصدير المخصص بنجاح إلى:\n{file_path}")
                else:
                    show_info_message("تم", f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_مخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                items = self.session.query(Inventory).order_by(Inventory.id.asc()).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(items),
                        'backup_type': 'inventory_full_backup'
                    },
                    'inventory': []
                }

                for item in items:
                    item_data = {
                        'id': item.id,
                        'name': item.name,
                        'category': item.category,
                        'quantity': float(item.quantity) if item.quantity else 0.0,
                        'min_quantity': float(item.min_quantity) if item.min_quantity else 0.0,
                        'cost_price': float(item.cost_price) if item.cost_price else 0.0,
                        'selling_price': float(item.selling_price) if item.selling_price else 0.0,
                        'supplier_id': item.supplier_id if item.supplier_id else None,
                        'supplier_name': item.supplier.name if item.supplier else None,
                        'location': item.location,
                        'notes': item.notes,
                        'last_updated': item.last_updated.isoformat() if item.last_updated else None
                    }
                    backup_data['inventory'].append(item_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                if hasattr(self.parent_widget, 'show_success_message'):
                    self.parent_widget.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(items)} عنصر")
                else:
                    show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(items)} عنصر")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للمخزون"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة باستخدام النافذة المتطورة مع تحذير قوي
                if hasattr(self.parent_widget, 'show_backup_restore_dialog'):
                    confirmed = self.parent_widget.show_backup_restore_dialog()
                else:
                    dialog = InventoryBackupRestoreDialog(self)
                    confirmed = dialog.exec_() == QDialog.Accepted

                if confirmed:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    if 'inventory' in backup_data:
                        restored_count = 0
                        for item_data in backup_data['inventory']:
                            # التحقق من وجود العنصر
                            existing = self.session.query(Inventory).filter_by(id=item_data['id']).first()
                            if not existing:
                                # إنشاء عنصر جديد
                                new_item = Inventory(
                                    name=item_data.get('name'),
                                    category=item_data.get('category'),
                                    unit=item_data.get('unit'),
                                    quantity=item_data.get('quantity', 0),
                                    min_quantity=item_data.get('min_quantity', 0),
                                    cost_price=item_data.get('cost_price', 0),
                                    selling_price=item_data.get('selling_price', 0),
                                    supplier_id=item_data.get('supplier_id'),
                                    location=item_data.get('location'),
                                    notes=item_data.get('notes'),
                                    last_updated=datetime.fromisoformat(item_data['last_updated']) if item_data.get('last_updated') else datetime.now()
                                )
                                self.session.add(new_item)
                                restored_count += 1

                        self.session.commit()
                        self.refresh_data()  # إعادة تحميل البيانات

                        if hasattr(self.parent_widget, 'show_success_message'):
                            self.parent_widget.show_success_message(f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم استعادة {restored_count} عنصر")
                        else:
                            show_info_message("تم", f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم استعادة {restored_count} عنصر")
                    else:
                        if hasattr(self.parent_widget, 'show_error_message'):
                            self.parent_widget.show_error_message("ملف النسخة الاحتياطية غير صالح!")
                        else:
                            show_error_message("خطأ", "ملف النسخة الاحتياطية غير صالح!")

        except Exception as e:
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")
            else:
                show_error_message("خطأ", f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")



# استخدام دوال معالجة الأخطاء الموحدة بدلاً من الكلاسات المحذوفة
def show_inventory_advanced_warning(parent, title, message, icon="⚠️"):
    """عرض نافذة تحذير للمخزن باستخدام الدوال الموحدة"""
    show_inventory_error_safely(parent, message, title)

def show_inventory_advanced_error(parent, title, message, icon="❌"):
    """عرض نافذة خطأ للمخزن باستخدام الدوال الموحدة"""
    show_inventory_error_safely(parent, message, title)

def show_inventory_advanced_success(parent, title, message, icon="✅"):
    """عرض نافذة نجاح للمخزن باستخدام الدوال الموحدة"""
    show_inventory_success_safely(parent, message, title)

def show_inventory_advanced_confirmation(parent, title, message, icon="❓"):
    """عرض نافذة تأكيد للمخزن باستخدام الدوال الموحدة"""
    from PyQt5.QtWidgets import QMessageBox
    reply = QMessageBox.question(parent, title, message,
                                QMessageBox.Yes | QMessageBox.No,
                                QMessageBox.No)
    return reply == QMessageBox.Yes


# تصدير الكلاسات الرئيسية
__all__ = [
    'InventoryMainWidget',  # الكلاس الرئيسي مع التبويبات (المشتريات والمبيعات)
    'InventoryWidget',      # كلاس المخزون فقط
    'InventoryItemDialog',  # نافذة إضافة/تعديل عنصر
    'InventoryInfoDialog',  # نافذة عرض تفاصيل عنصر
    'CategoryManagementDialog',  # نافذة إدارة الفئات
    'InventoryStatisticsDialog',  # نافذة الإحصائيات
]

