# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطي
Backup Manager Module
"""

import os
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path
import schedule
import time

class BackupManager:
    """مدير النسخ الاحتياطي"""
    
    def __init__(self):
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        self.database_path = "accounting.db"
        self.max_backups = 10
        
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            if not os.path.exists(self.database_path):
                return False, "ملف قاعدة البيانات غير موجود"
            
            # إنشاء اسم ملف النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}.db"
            backup_path = self.backup_dir / backup_filename
            
            # نسخ قاعدة البيانات
            shutil.copy2(self.database_path, backup_path)
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return True, f"تم إنشاء النسخة الاحتياطية: {backup_filename}"
            
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {e}"
    
    def restore_backup(self, backup_filename):
        """استعادة نسخة احتياطية"""
        try:
            backup_path = self.backup_dir / backup_filename
            
            if not backup_path.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            if os.path.exists(self.database_path):
                current_backup = f"current_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                shutil.copy2(self.database_path, self.backup_dir / current_backup)
            
            # استعادة النسخة الاحتياطية
            shutil.copy2(backup_path, self.database_path)
            
            return True, f"تم استعادة النسخة الاحتياطية: {backup_filename}"
            
        except Exception as e:
            return False, f"خطأ في استعادة النسخة الاحتياطية: {e}"
    
    def list_backups(self):
        """قائمة النسخ الاحتياطية"""
        try:
            backups = []
            for backup_file in self.backup_dir.glob("backup_*.db"):
                stat = backup_file.stat()
                backups.append({
                    'filename': backup_file.name,
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'path': str(backup_file)
                })
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            backups.sort(key=lambda x: x['created'], reverse=True)
            return backups
            
        except Exception as e:
            print(f"خطأ في قراءة النسخ الاحتياطية: {e}")
            return []
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            if len(backups) > self.max_backups:
                # حذف النسخ الزائدة (الأقدم)
                for backup in backups[self.max_backups:]:
                    backup_path = Path(backup['path'])
                    backup_path.unlink()
                    print(f"تم حذف النسخة الاحتياطية القديمة: {backup['filename']}")
                    
        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
    
    def schedule_auto_backup(self, interval_hours=24):
        """جدولة النسخ الاحتياطي التلقائي"""
        try:
            schedule.every(interval_hours).hours.do(self.create_backup)
            print(f"تم جدولة النسخ الاحتياطي التلقائي كل {interval_hours} ساعة")
            return True
        except Exception as e:
            print(f"خطأ في جدولة النسخ الاحتياطي: {e}")
            return False
    
    def run_scheduler(self):
        """تشغيل جدولة النسخ الاحتياطي"""
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
        except KeyboardInterrupt:
            print("تم إيقاف جدولة النسخ الاحتياطي")
        except Exception as e:
            print(f"خطأ في تشغيل جدولة النسخ الاحتياطي: {e}")
    
    def verify_backup(self, backup_filename):
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            backup_path = self.backup_dir / backup_filename
            
            if not backup_path.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # محاولة فتح قاعدة البيانات للتحقق من سلامتها
            conn = sqlite3.connect(str(backup_path))
            cursor = conn.cursor()
            
            # تنفيذ استعلام بسيط للتحقق
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            conn.close()
            
            if tables:
                return True, f"النسخة الاحتياطية سليمة - تحتوي على {len(tables)} جدول"
            else:
                return False, "النسخة الاحتياطية فارغة أو تالفة"
                
        except Exception as e:
            return False, f"خطأ في التحقق من النسخة الاحتياطية: {e}"
    
    def get_backup_info(self, backup_filename):
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            backup_path = self.backup_dir / backup_filename
            
            if not backup_path.exists():
                return None
            
            stat = backup_path.stat()
            
            # الحصول على معلومات قاعدة البيانات
            conn = sqlite3.connect(str(backup_path))
            cursor = conn.cursor()
            
            # عدد الجداول
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table';")
            table_count = cursor.fetchone()[0]
            
            # حجم قاعدة البيانات
            cursor.execute("PRAGMA page_count;")
            page_count = cursor.fetchone()[0]
            cursor.execute("PRAGMA page_size;")
            page_size = cursor.fetchone()[0]
            db_size = page_count * page_size
            
            conn.close()
            
            return {
                'filename': backup_filename,
                'file_size': stat.st_size,
                'db_size': db_size,
                'table_count': table_count,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime)
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات النسخة الاحتياطية: {e}")
            return None

# إنشاء مثيل عام
backup_manager = BackupManager()

# دوال مساعدة للاستخدام المباشر
def create_backup():
    """إنشاء نسخة احتياطية"""
    return backup_manager.create_backup()

def restore_backup(backup_filename):
    """استعادة نسخة احتياطية"""
    return backup_manager.restore_backup(backup_filename)

def list_backups():
    """قائمة النسخ الاحتياطية"""
    return backup_manager.list_backups()

def verify_backup(backup_filename):
    """التحقق من سلامة النسخة الاحتياطية"""
    return backup_manager.verify_backup(backup_filename)

def schedule_auto_backup(interval_hours=24):
    """جدولة النسخ الاحتياطي التلقائي"""
    return backup_manager.schedule_auto_backup(interval_hours)

print("✅ تم تحميل مدير النسخ الاحتياطي بنجاح")
