#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد الباركود المتقدم
===================

وحدة شاملة لتوليد الباركود بصيغ مختلفة مع دعم:
- Code128 (الأكثر شيوعاً)
- EAN13 (للمنتجات التجارية)
- QR Code (للمعلومات المتقدمة)
- Code39 (للاستخدامات الصناعية)

المؤلف: نظام إدارة الأعمال
التاريخ: 2025-07-30
"""

import os
import io
import base64
import random
import string
from datetime import datetime
from typing import Optional, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont

try:
    # محاولة استيراد مكتبات الباركود
    import barcode
    from barcode import Code128, Code39, EAN13
    from barcode.writer import ImageWriter
    BARCODE_AVAILABLE = True
except ImportError:
    BARCODE_AVAILABLE = False
    print("⚠️ مكتبة python-barcode غير مثبتة. سيتم استخدام مولد باركود بسيط")

try:
    import qrcode
    QR_AVAILABLE = True
except ImportError:
    QR_AVAILABLE = False
    print("⚠️ مكتبة qrcode غير مثبتة. لن تكون أكواد QR متاحة")


class BarcodeGenerator:
    """مولد الباركود المتقدم مع دعم صيغ متعددة"""
    
    def __init__(self):
        self.supported_formats = []
        
        if BARCODE_AVAILABLE:
            self.supported_formats.extend(['code128', 'code39', 'ean13'])
        
        if QR_AVAILABLE:
            self.supported_formats.append('qr')
        
        # إضافة مولد باركود بسيط كخيار احتياطي
        self.supported_formats.append('simple')
    
    def generate_barcode_number(self, prefix: str = "ITM", length: int = 10) -> str:
        """توليد رقم باركود فريد"""
        timestamp = datetime.now().strftime("%y%m%d%H%M%S")
        random_part = ''.join(random.choices(string.digits, k=3))
        
        # تكوين الرقم
        if length <= len(prefix) + len(timestamp) + len(random_part):
            # إذا كان الطول المطلوب قصير، استخدم timestamp مختصر
            timestamp = datetime.now().strftime("%m%d%H%M")
            barcode_number = f"{prefix}{timestamp}{random_part}"
        else:
            # إضافة أصفار إضافية إذا لزم الأمر
            total_digits = length - len(prefix)
            number_part = f"{timestamp}{random_part}".zfill(total_digits)
            barcode_number = f"{prefix}{number_part}"
        
        return barcode_number[:length]
    
    def generate_ean13_number(self) -> str:
        """توليد رقم EAN13 صحيح مع check digit"""
        # توليد 12 رقم عشوائي
        digits = [random.randint(0, 9) for _ in range(12)]
        
        # حساب check digit
        odd_sum = sum(digits[i] for i in range(0, 12, 2))
        even_sum = sum(digits[i] for i in range(1, 12, 2))
        total = odd_sum + (even_sum * 3)
        check_digit = (10 - (total % 10)) % 10
        
        # إضافة check digit
        digits.append(check_digit)
        
        return ''.join(map(str, digits))
    
    def create_barcode(self, data: str, format_type: str = 'code128', 
                      save_path: Optional[str] = None) -> Tuple[bool, str, Optional[bytes]]:
        """
        إنشاء باركود بالصيغة المحددة
        
        Args:
            data: البيانات المراد تشفيرها
            format_type: نوع الباركود (code128, code39, ean13, qr, simple)
            save_path: مسار حفظ الصورة (اختياري)
        
        Returns:
            tuple: (نجح, رسالة, بيانات الصورة)
        """
        try:
            format_type = format_type.lower()
            
            if format_type not in self.supported_formats:
                return False, f"صيغة الباركود غير مدعومة: {format_type}", None
            
            if format_type == 'qr':
                return self._create_qr_code(data, save_path)
            elif format_type == 'simple':
                return self._create_simple_barcode(data, save_path)
            elif BARCODE_AVAILABLE:
                return self._create_standard_barcode(data, format_type, save_path)
            else:
                return self._create_simple_barcode(data, save_path)
                
        except Exception as e:
            return False, f"خطأ في إنشاء الباركود: {str(e)}", None
    
    def _create_qr_code(self, data: str, save_path: Optional[str] = None) -> Tuple[bool, str, Optional[bytes]]:
        """إنشاء QR Code"""
        if not QR_AVAILABLE:
            return False, "مكتبة QR Code غير متاحة", None
        
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء الصورة
            img = qr.make_image(fill_color="black", back_color="white")
            
            # حفظ أو إرجاع البيانات
            if save_path:
                img.save(save_path)
                return True, f"تم حفظ QR Code في: {save_path}", None
            else:
                # تحويل إلى bytes
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='PNG')
                img_bytes = img_buffer.getvalue()
                return True, "تم إنشاء QR Code بنجاح", img_bytes
                
        except Exception as e:
            return False, f"خطأ في إنشاء QR Code: {str(e)}", None
    
    def _create_standard_barcode(self, data: str, format_type: str, 
                                save_path: Optional[str] = None) -> Tuple[bool, str, Optional[bytes]]:
        """إنشاء باركود بالصيغ المعيارية"""
        try:
            # اختيار نوع الباركود
            if format_type == 'code128':
                barcode_class = Code128
            elif format_type == 'code39':
                barcode_class = Code39
            elif format_type == 'ean13':
                barcode_class = EAN13
                # التأكد من أن البيانات 13 رقم للـ EAN13
                if len(data) != 13 or not data.isdigit():
                    data = self.generate_ean13_number()
            else:
                return False, f"صيغة باركود غير مدعومة: {format_type}", None
            
            # إنشاء الباركود
            writer = ImageWriter()
            writer.set_options({
                'module_width': 0.2,
                'module_height': 15.0,
                'quiet_zone': 6.5,
                'font_size': 10,
                'text_distance': 5.0,
                'background': 'white',
                'foreground': 'black',
            })
            
            barcode_instance = barcode_class(data, writer=writer)
            
            if save_path:
                # حفظ في ملف
                barcode_instance.save(save_path.replace('.png', ''))
                return True, f"تم حفظ الباركود في: {save_path}", None
            else:
                # إرجاع البيانات
                img_buffer = io.BytesIO()
                barcode_instance.write(img_buffer)
                img_bytes = img_buffer.getvalue()
                return True, f"تم إنشاء باركود {format_type.upper()} بنجاح", img_bytes
                
        except Exception as e:
            return False, f"خطأ في إنشاء الباركود المعياري: {str(e)}", None
    
    def _create_simple_barcode(self, data: str, save_path: Optional[str] = None) -> Tuple[bool, str, Optional[bytes]]:
        """إنشاء باركود بسيط باستخدام PIL فقط"""
        try:
            # إعدادات الباركود البسيط
            width = 300
            height = 100
            bar_width = 2
            
            # إنشاء صورة
            img = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(img)
            
            # تحويل البيانات إلى نمط binary بسيط
            binary_data = ''.join(format(ord(char), '08b') for char in data)
            
            # رسم الخطوط
            x = 10
            for bit in binary_data:
                if x >= width - 10:
                    break
                
                if bit == '1':
                    draw.rectangle([x, 10, x + bar_width, height - 30], fill='black')
                
                x += bar_width + 1
            
            # إضافة النص
            try:
                # محاولة استخدام خط افتراضي
                font = ImageFont.load_default()
                text_width = draw.textlength(data, font=font)
                text_x = (width - text_width) // 2
                draw.text((text_x, height - 25), data, fill='black', font=font)
            except:
                # في حالة فشل الخط، استخدم النص بدون خط
                draw.text((10, height - 25), data, fill='black')
            
            if save_path:
                img.save(save_path)
                return True, f"تم حفظ الباركود البسيط في: {save_path}", None
            else:
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='PNG')
                img_bytes = img_buffer.getvalue()
                return True, "تم إنشاء باركود بسيط بنجاح", img_bytes
                
        except Exception as e:
            return False, f"خطأ في إنشاء الباركود البسيط: {str(e)}", None
    
    def get_supported_formats(self) -> Dict[str, str]:
        """الحصول على الصيغ المدعومة مع أوصافها"""
        formats = {}
        
        if 'code128' in self.supported_formats:
            formats['code128'] = 'Code 128 - الأكثر شيوعاً ومرونة'
        
        if 'code39' in self.supported_formats:
            formats['code39'] = 'Code 39 - للاستخدامات الصناعية'
        
        if 'ean13' in self.supported_formats:
            formats['ean13'] = 'EAN-13 - للمنتجات التجارية'
        
        if 'qr' in self.supported_formats:
            formats['qr'] = 'QR Code - لمعلومات متقدمة'
        
        if 'simple' in self.supported_formats:
            formats['simple'] = 'باركود بسيط - احتياطي'
        
        return formats
    
    def validate_barcode_data(self, data: str, format_type: str) -> Tuple[bool, str]:
        """التحقق من صحة بيانات الباركود للصيغة المحددة"""
        format_type = format_type.lower()
        
        if format_type == 'ean13':
            if len(data) != 13 or not data.isdigit():
                return False, "EAN13 يجب أن يكون 13 رقم بالضبط"
        
        elif format_type == 'code39':
            # Code39 يدعم أرقام وحروف وبعض الرموز
            allowed_chars = set(string.ascii_uppercase + string.digits + '-. $/+%')
            if not all(c in allowed_chars for c in data.upper()):
                return False, "Code39 يدعم فقط الأرقام والحروف الكبيرة وبعض الرموز"
        
        elif format_type == 'code128':
            # Code128 يدعم جميع أحرف ASCII
            try:
                data.encode('ascii')
            except UnicodeEncodeError:
                return False, "Code128 يدعم فقط أحرف ASCII"
        
        return True, "البيانات صحيحة"


# مثيل عام للاستخدام
barcode_generator = BarcodeGenerator()


def generate_barcode(data: str, format_type: str = 'code128') -> Optional[str]:
    """دالة بسيطة لتوليد الباركود"""
    try:
        success, message, img_bytes = barcode_generator.create_barcode(data, format_type)
        if success and img_bytes:
            # تحويل البيانات إلى base64 للإرجاع
            import base64
            return base64.b64encode(img_bytes).decode('utf-8')
        elif success:
            return message  # في حالة الحفظ في ملف
        else:
            return None
    except Exception as e:
        print(f"خطأ في توليد الباركود: {e}")
        return None


def create_barcode_image(data: str, format_type: str = 'code128', save_path: str = None) -> bool:
    """إنشاء صورة باركود وحفظها"""
    try:
        success, message, _ = barcode_generator.create_barcode(data, format_type, save_path)
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
        return success
    except Exception as e:
        print(f"خطأ في إنشاء صورة الباركود: {e}")
        return False


def generate_product_barcode(product_id: int, product_name: str = "",
                           format_type: str = 'code128') -> Tuple[bool, str, str]:
    """
    توليد باركود للمنتج
    
    Args:
        product_id: معرف المنتج
        product_name: اسم المنتج (اختياري)
        format_type: نوع الباركود
    
    Returns:
        tuple: (نجح, رسالة, رقم الباركود)
    """
    try:
        # توليد رقم باركود فريد
        if format_type.lower() == 'ean13':
            barcode_number = barcode_generator.generate_ean13_number()
        else:
            barcode_number = barcode_generator.generate_barcode_number(
                prefix=f"P{product_id:04d}", 
                length=12
            )
        
        # التحقق من صحة البيانات
        is_valid, validation_msg = barcode_generator.validate_barcode_data(
            barcode_number, format_type
        )
        
        if not is_valid:
            return False, f"بيانات باركود غير صحيحة: {validation_msg}", ""
        
        return True, "تم توليد رقم الباركود بنجاح", barcode_number
        
    except Exception as e:
        return False, f"خطأ في توليد باركود المنتج: {str(e)}", ""


def create_product_barcode_image(barcode_number: str, format_type: str = 'code128',
                               save_path: Optional[str] = None) -> Tuple[bool, str, Optional[bytes]]:
    """
    إنشاء صورة باركود للمنتج
    
    Args:
        barcode_number: رقم الباركود
        format_type: نوع الباركود
        save_path: مسار الحفظ (اختياري)
    
    Returns:
        tuple: (نجح, رسالة, بيانات الصورة)
    """
    return barcode_generator.create_barcode(barcode_number, format_type, save_path)
