# -*- coding: utf-8 -*-
"""
إعدادات نظام السجلات للبرنامج
Logging Configuration for the Application
"""

import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path

# ═══════════════════════════════════════════════════════════════════════════════════
# إعدادات السجلات الأساسية
# ═══════════════════════════════════════════════════════════════════════════════════

# مجلد السجلات
LOGS_DIR = Path("logs")
LOGS_DIR.mkdir(exist_ok=True)

# مستويات السجلات
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# المستوى الافتراضي
DEFAULT_LOG_LEVEL = 'INFO'

# ═══════════════════════════════════════════════════════════════════════════════════
# تنسيق السجلات
# ═══════════════════════════════════════════════════════════════════════════════════

# تنسيق السجلات المفصل
DETAILED_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'

# تنسيق السجلات البسيط
SIMPLE_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# تنسيق التاريخ
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# ═══════════════════════════════════════════════════════════════════════════════════
# إعدادات ملفات السجلات
# ═══════════════════════════════════════════════════════════════════════════════════

# أسماء ملفات السجلات
LOG_FILES = {
    'main': LOGS_DIR / 'application.log',
    'database': LOGS_DIR / 'database.log',
    'errors': LOGS_DIR / 'errors.log',
    'performance': LOGS_DIR / 'performance.log',
    'security': LOGS_DIR / 'security.log'
}

# حجم ملف السجل الأقصى (بالبايت)
MAX_LOG_SIZE = 10 * 1024 * 1024  # 10 MB

# عدد ملفات النسخ الاحتياطية
BACKUP_COUNT = 5

# ═══════════════════════════════════════════════════════════════════════════════════
# إعداد نظام السجلات
# ═══════════════════════════════════════════════════════════════════════════════════

def setup_logging(log_level=DEFAULT_LOG_LEVEL, enable_console=True, enable_file=True):
    """
    إعداد نظام السجلات
    
    Args:
        log_level (str): مستوى السجلات
        enable_console (bool): تفعيل السجلات في وحدة التحكم
        enable_file (bool): تفعيل السجلات في الملفات
    """
    
    # تحديد مستوى السجلات
    level = LOG_LEVELS.get(log_level.upper(), logging.INFO)
    
    # إعداد السجل الجذر
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # مسح المعالجات الموجودة
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # إنشاء المنسق
    formatter = logging.Formatter(DETAILED_FORMAT, DATE_FORMAT)
    
    # إعداد معالج وحدة التحكم
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # إعداد معالجات الملفات
    if enable_file:
        # السجل الرئيسي
        main_handler = logging.handlers.RotatingFileHandler(
            LOG_FILES['main'],
            maxBytes=MAX_LOG_SIZE,
            backupCount=BACKUP_COUNT,
            encoding='utf-8'
        )
        main_handler.setLevel(level)
        main_handler.setFormatter(formatter)
        root_logger.addHandler(main_handler)
        
        # سجل الأخطاء
        error_handler = logging.handlers.RotatingFileHandler(
            LOG_FILES['errors'],
            maxBytes=MAX_LOG_SIZE,
            backupCount=BACKUP_COUNT,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
    
    return root_logger

# ═══════════════════════════════════════════════════════════════════════════════════
# سجلات مخصصة
# ═══════════════════════════════════════════════════════════════════════════════════

def get_logger(name):
    """
    الحصول على سجل مخصص
    
    Args:
        name (str): اسم السجل
        
    Returns:
        logging.Logger: كائن السجل
    """
    return logging.getLogger(name)

def get_database_logger():
    """الحصول على سجل قاعدة البيانات"""
    logger = logging.getLogger('database')
    
    if not logger.handlers:
        handler = logging.handlers.RotatingFileHandler(
            LOG_FILES['database'],
            maxBytes=MAX_LOG_SIZE,
            backupCount=BACKUP_COUNT,
            encoding='utf-8'
        )
        formatter = logging.Formatter(DETAILED_FORMAT, DATE_FORMAT)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger

def get_performance_logger():
    """الحصول على سجل الأداء"""
    logger = logging.getLogger('performance')
    
    if not logger.handlers:
        handler = logging.handlers.RotatingFileHandler(
            LOG_FILES['performance'],
            maxBytes=MAX_LOG_SIZE,
            backupCount=BACKUP_COUNT,
            encoding='utf-8'
        )
        formatter = logging.Formatter(SIMPLE_FORMAT, DATE_FORMAT)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    
    return logger

def get_security_logger():
    """الحصول على سجل الأمان"""
    logger = logging.getLogger('security')
    
    if not logger.handlers:
        handler = logging.handlers.RotatingFileHandler(
            LOG_FILES['security'],
            maxBytes=MAX_LOG_SIZE,
            backupCount=BACKUP_COUNT,
            encoding='utf-8'
        )
        formatter = logging.Formatter(DETAILED_FORMAT, DATE_FORMAT)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.WARNING)
    
    return logger

# ═══════════════════════════════════════════════════════════════════════════════════
# دوال مساعدة
# ═══════════════════════════════════════════════════════════════════════════════════

def log_function_call(func):
    """
    مُزخرف لتسجيل استدعاءات الدوال
    
    Args:
        func: الدالة المراد تسجيلها
    """
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"استدعاء الدالة: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"انتهاء الدالة: {func.__name__}")
            return result
        except Exception as e:
            logger.error(f"خطأ في الدالة {func.__name__}: {e}")
            raise
    return wrapper

def log_performance(operation_name):
    """
    مُزخرف لتسجيل أداء العمليات
    
    Args:
        operation_name (str): اسم العملية
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_performance_logger()
            start_time = datetime.now()
            
            try:
                result = func(*args, **kwargs)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.info(f"{operation_name}: {duration:.3f} ثانية")
                return result
            except Exception as e:
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.error(f"{operation_name} فشل بعد {duration:.3f} ثانية: {e}")
                raise
        return wrapper
    return decorator

def cleanup_old_logs(days_to_keep=30):
    """
    تنظيف السجلات القديمة
    
    Args:
        days_to_keep (int): عدد الأيام للاحتفاظ بالسجلات
    """
    import time
    
    cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
    
    for log_file in LOGS_DIR.glob("*.log*"):
        if log_file.stat().st_mtime < cutoff_time:
            try:
                log_file.unlink()
                print(f"تم حذف السجل القديم: {log_file}")
            except Exception as e:
                print(f"خطأ في حذف السجل {log_file}: {e}")

# ═══════════════════════════════════════════════════════════════════════════════════
# تهيئة نظام السجلات
# ═══════════════════════════════════════════════════════════════════════════════════

# إعداد السجلات عند استيراد الوحدة
try:
    setup_logging()
    print("✅ تم إعداد نظام السجلات بنجاح")
except Exception as e:
    print(f"❌ خطأ في إعداد نظام السجلات: {e}")

# تنظيف السجلات القديمة
try:
    cleanup_old_logs()
except Exception as e:
    print(f"⚠️ خطأ في تنظيف السجلات القديمة: {e}")

# ═══════════════════════════════════════════════════════════════════════════════════
# دوال السجلات المطلوبة لملف settings.py
# ═══════════════════════════════════════════════════════════════════════════════════

def smart_logger():
    """الحصول على السجل الذكي"""
    return get_logger('smart_logger')

def log_info(message):
    """تسجيل رسالة معلومات"""
    logger = get_logger('info')
    logger.info(message)

def log_error(message):
    """تسجيل رسالة خطأ"""
    logger = get_logger('error')
    logger.error(message)

def log_warning(message):
    """تسجيل رسالة تحذير"""
    logger = get_logger('warning')
    logger.warning(message)
