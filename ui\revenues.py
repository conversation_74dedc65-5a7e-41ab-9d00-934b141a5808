from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QSizePolicy, QFrame, QTextBrowser, QMenu, QAction, QFileDialog, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime, QTimer, QRect
from PyQt5.QtGui import QIcon, QFont, QTextDocument, QColor, QPainter, QPixmap, QBrush, QPen, QLinearGradient, QRadialGradient
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Revenue, Invoice
from utils import (qdate_to_datetime, datetime_to_qdate, format_currency,
                   format_datetime_for_export, format_datetime_for_filename)
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin
from sqlalchemy import func

# ===============================
# النوافذ المتطورة للإيرادات
# ===============================

def apply_revenue_title_bar_styling(dialog):
    """دالة مشتركة لتطبيق تصميم شريط العنوان للإيرادات"""
    try:
        # إنشاء أيقونة مخصصة للإيرادات
        pixmap = QPixmap(48, 48)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # رسم دائرة بتدرج أخضر للإيرادات
        gradient = QRadialGradient(24, 24, 20)
        gradient.setColorAt(0, QColor(34, 197, 94))  # أخضر فاتح
        gradient.setColorAt(1, QColor(21, 128, 61))  # أخضر داكن

        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.drawEllipse(4, 4, 40, 40)

        # رسم رمز الدولار
        painter.setPen(QPen(QColor(255, 255, 255), 3))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "$")

        painter.end()

        dialog.setWindowIcon(QIcon(pixmap))
        TitleBarStyler.apply_advanced_title_bar_styling(dialog)
    except Exception as e:
        print(f"تحذير: فشل في تطبيق تصميم شريط العنوان للإيرادات: {e}")
        # المتابعة بدون تخصيص شريط العنوان

class RevenueWarningDialog(QDialog):
    """نافذة تحذير متطورة للإيرادات مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="تحذير", message="", icon="⚠️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.setup_ui(title, message, icon)

    def setup_ui(self, title, message, icon):
        """إعداد واجهة النافذة مطابقة لنافذة الحذف"""
        self.setWindowTitle(f"{icon} {title} - نظام إدارة الإيرادات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(245, 158, 11, 0.2),
                    stop:0.5 rgba(217, 119, 6, 0.3),
                    stop:1 rgba(180, 83, 9, 0.2));
                border: 2px solid rgba(245, 158, 11, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المحتوى
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 15px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        close_button = QPushButton("✅ حسناً")
        self.style_advanced_button(close_button, 'warning')
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'warning': '#F59E0B',
                    'danger': '#EF4444',
                    'info': '#3B82F6',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار التحذير: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)


class RevenueErrorDialog(QDialog):
    """نافذة خطأ متطورة للإيرادات مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="خطأ", message="", icon="❌"):
        super().__init__(parent)
        self.parent_widget = parent
        self.setup_ui(title, message, icon)

    def setup_ui(self, title, message, icon):
        """إعداد واجهة النافذة مطابقة لنافذة الحذف"""
        self.setWindowTitle(f"{icon} {title} - نظام إدارة الإيرادات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المحتوى
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 15px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        close_button = QPushButton("✅ حسناً")
        self.style_advanced_button(close_button, 'danger')
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'warning': '#F59E0B',
                    'danger': '#EF4444',
                    'info': '#3B82F6',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الخطأ: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)


class RevenueMessageDialog(QDialog):
    """نافذة رسائل متطورة للإيرادات مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="معلومات", message="", icon="ℹ️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.setup_ui(title, message, icon)

    def setup_ui(self, title, message, icon):
        """إعداد واجهة النافذة مطابقة لنافذة الحذف"""
        self.setWindowTitle(f"{icon} {title} - نظام إدارة الإيرادات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المحتوى
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 15px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        close_button = QPushButton("✅ حسناً")
        self.style_advanced_button(close_button, 'info')
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'warning': '#F59E0B',
                    'danger': '#EF4444',
                    'info': '#3B82F6',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الرسائل: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)


class DeleteRevenueDialog(QDialog):
    """نافذة حذف الإيراد مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, revenue=None):
        super().__init__(parent)
        self.revenue = revenue
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("💰 حذف - نظام إدارة الإيرادات المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("💰 حذف الإيراد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات الإيراد مضغوطة
        if self.revenue:
            info_text = f"💰 {self.revenue.title[:15]}{'...' if len(self.revenue.title) > 15 else ''}"
            if self.revenue.amount:
                info_text += f" | 💰 {self.revenue.amount:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("💰 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الحذف: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)


# ===============================
# دوال مساعدة لعرض النوافذ المتطورة للإيرادات
# ===============================

def ensure_revenue_dialog_stays_visible(dialog):
    """التأكد من أن النافذة تبقى مرئية"""
    if dialog:
        dialog.setModal(True)
        dialog.raise_()
        dialog.activateWindow()
        dialog.show()
        return dialog

def show_revenue_advanced_warning(parent, title, message, icon="⚠️"):
    """عرض نافذة تحذير متطورة للإيرادات"""
    dialog = RevenueWarningDialog(parent, title, message, icon)
    return ensure_revenue_dialog_stays_visible(dialog).exec_()

def show_revenue_advanced_error(parent, title, message, icon="❌"):
    """عرض نافذة خطأ متطورة للإيرادات"""
    dialog = RevenueErrorDialog(parent, title, message, icon)
    return ensure_revenue_dialog_stays_visible(dialog).exec_()

def show_revenue_advanced_info(parent, title, message, icon="ℹ️"):
    """عرض نافذة معلومات متطورة للإيرادات"""
    dialog = RevenueMessageDialog(parent, title, message, icon)
    return ensure_revenue_dialog_stays_visible(dialog).exec_()


class RevenueDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل إيراد - مطابقة للتصميم الموحد"""

    def __init__(self, parent=None, revenue=None, session=None):
        super().__init__(parent)
        self.revenue = revenue
        self.session = session
        self.parent_widget = parent
        self.init_ui()

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)
        try:
            # توسيط النص في شريط العنوان
            self.center_title_text()
        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")
            # المتابعة بدون توسيط النص

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            original_title = "💰 تعديل إيراد - نظام إدارة الإيرادات المتطور والشامل" if self.revenue else "💰 إضافة إيراد جديد - نظام إدارة الإيرادات المتطور والشامل"

            # حساب المسافات المطلوبة للتوسيط
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"

            # تحديث العنوان مع التوسيط
            self.setWindowTitle(centered_title)

        except Exception as e:
            print(f"تحذير: فشل في توسيط النص المتقدم: {e}")
            # المتابعة بدون توسيط النص

    def init_ui(self):
        # إعداد نافذة الحوار مطابق للتصميم الموحد
        if self.revenue:
            self.setWindowTitle("💰 تعديل إيراد - نظام إدارة الإيرادات المتطور والشامل")
        else:
            self.setWindowTitle("💰 إضافة إيراد جديد - نظام إدارة الإيرادات المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان (إذا كانت الدالة متاحة)
        if hasattr(self, 'customize_title_bar'):
            self.customize_title_bar()

        # خلفية النافذة مطابقة للنافذة الرئيسية
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        self.setModal(True)
        self.resize(650, 650)

        # إنشاء التخطيط الرئيسي مطابق للعملاء والموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # إضافة عنوان النافذة الداخلي مطابق للعملاء والموردين
        title_text = "تعديل بيانات الإيراد" if self.revenue else "إضافة إيراد جديد"
        title_label = QLabel(f"💰 {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 18px 25px;
                margin: 8px 5px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                           0 3px 12px rgba(37, 99, 235, 0.3);
                min-height: 50px;
                max-height: 50px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء النموذج مطابق للعملاء والموردين
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # إنشاء دالة لتصميم النصوص مع عرض مقلل - مطابقة للعملاء والموردين
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # حقل العنوان مطابق للعملاء والموردين
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("أدخل عنوان الإيراد...")
        if self.revenue:
            self.title_edit.setText(self.revenue.title)
        self.title_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("العنوان", "💰", True), self.title_edit)

        # حقل المبلغ مطابق للعملاء والموردين
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setRange(0, 1000000)
        self.amount_edit.setDecimals(0)
        self.amount_edit.setSingleStep(100)
        self.amount_edit.setSuffix(" جنيه")
        if self.revenue:
            self.amount_edit.setValue(self.revenue.amount)
        self.amount_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("المبلغ", "💎", True), self.amount_edit)

        # حقل التاريخ مطابق للعملاء والموردين
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDisplayFormat("yyyy-MM-dd")
        if self.revenue:
            self.date_edit.setDate(datetime_to_qdate(self.revenue.date))
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("التاريخ", "📅", True), self.date_edit)

        # حقل المصدر مطابق للمشاريع والعقارات بدون إطارات
        self.category_combo = QComboBox()
        # خيارات متعددة للمصادر مطابقة للمشاريع والعقارات
        categories = [
            "مبيعات", "خدمات", "استثمارات", "فوائد", "إيجارات",
            "عمولات", "أرباح", "منح", "تبرعات", "استشارات", "أخرى"
        ]
        self.category_combo.addItems(categories)
        if self.revenue:
            index = self.category_combo.findText(self.revenue.category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
        self.category_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(16, 185, 129, 0.3);
                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("المصدر", "📂", True), self.category_combo)

        # حقل المشروع - جديد
        self.project_combo = QComboBox()
        self.project_combo.addItem("لا يوجد", None)
        # تحميل المشاريع من قاعدة البيانات
        if self.session:
            from database import Project
            projects = self.session.query(Project).all()
            for project in projects:
                client_name = project.client.name if project.client else 'غير محدد'
                self.project_combo.addItem(f"{project.name} - {client_name}", project.id)

        if self.revenue and hasattr(self.revenue, 'project_id') and self.revenue.project_id:
            # البحث عن المشروع المحدد وتعيينه
            for i in range(self.project_combo.count()):
                if self.project_combo.itemData(i) == self.revenue.project_id:
                    self.project_combo.setCurrentIndex(i)
                    break

        self.project_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(16, 185, 129, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("المشروع", "🏗️"), self.project_combo)

        # حقل العقار - جديد
        self.property_combo = QComboBox()
        self.property_combo.addItem("لا يوجد", None)
        # تحميل العقارات من قاعدة البيانات
        if self.session:
            from database import Property
            properties = self.session.query(Property).all()
            for property_item in properties:
                project_name = property_item.project.name if property_item.project else 'غير مرتبط بمشروع'
                display_text = f"{property_item.title} - {property_item.type} ({project_name})"
                self.property_combo.addItem(display_text, property_item.id)

        if self.revenue and hasattr(self.revenue, 'property_id') and self.revenue.property_id:
            # البحث عن العقار المحدد وتعيينه
            for i in range(self.property_combo.count()):
                if self.property_combo.itemData(i) == self.revenue.property_id:
                    self.property_combo.setCurrentIndex(i)
                    break

        self.property_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(34, 197, 94, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 30px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
                transition: all 0.3s ease;
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(34, 197, 94, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("العقار", "🏠"), self.property_combo)

        # حقل الفاتورة مطابق للعملاء والموردين
        self.invoice_combo = QComboBox()
        self.invoice_combo.addItem("لا يوجد", None)
        # تحميل الفواتير من قاعدة البيانات
        if self.session:
            from database import Invoice
            invoices = self.session.query(Invoice).all()
            for invoice in invoices:
                self.invoice_combo.addItem(f"{invoice.invoice_number} - {invoice.client.name if invoice.client else 'غير محدد'}", invoice.id)

        if self.revenue and self.revenue.invoice_id:
            for i in range(self.invoice_combo.count()):
                if self.invoice_combo.itemData(i) == self.revenue.invoice_id:
                    self.invoice_combo.setCurrentIndex(i)
                    break

        self.invoice_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        form_layout.addRow(create_styled_label("الفاتورة", "📋"), self.invoice_combo)

        # حقل الملاحظات مطابق للعملاء والموردين
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_edit.setMaximumHeight(80)
        if self.revenue:
            self.notes_edit.setText(self.revenue.notes or "")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.7);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                min-height: 60px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.2);
                transition: all 0.3s ease;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.01);
            }
        """)
        form_layout.addRow(create_styled_label("ملاحظات", "📝"), self.notes_edit)

        main_layout.addLayout(form_layout)

        # أزرار التحكم - مطابقة للعملاء والموردين (ترتيب صحيح)
        button_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        if hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.accept)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        if hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # ترتيب صحيح: الإلغاء أولاً ثم الحفظ
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(save_button)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات الإيراد من النموذج"""
        title = self.title_edit.text().strip()
        amount = self.amount_edit.value()
        date = qdate_to_datetime(self.date_edit.date())
        category = self.category_combo.currentText()
        project_id = self.project_combo.currentData()
        property_id = self.property_combo.currentData()
        invoice_id = self.invoice_combo.currentData()
        notes = self.notes_edit.toPlainText().strip()

        # التحقق من صحة البيانات
        if not title:
            show_revenue_advanced_error(self, "خطأ", "يجب إدخال عنوان الإيراد")
            return None

        if amount <= 0:
            show_revenue_advanced_error(self, "خطأ", "يجب أن يكون المبلغ أكبر من صفر")
            return None

        return {
            'title': title,
            'amount': amount,
            'date': date,
            'category': category,
            'project_id': project_id,
            'property_id': property_id,
            'invoice_id': invoice_id,
            'notes': notes
        }

class RevenuesWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة الإيرادات مع التحديد المتعدد"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        self.init_ui()
        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(150, self.refresh_data)

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مع تقليل المساحات لاستغلال الجدول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(3)  # تقليل المسافات من 8 إلى 3

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("💵 إدارة الإيرادات المتطورة - نظام شامل ومتقدم لإدارة الإيرادات مع أدوات احترافية للبحث والتحليل")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالعنوان، الفئة، الفاتورة أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_revenues)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 3px 10px rgba(96, 165, 250, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_revenues)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية مطابقة للفواتير
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الإيرادات المتطور والمحسن
        self.create_advanced_revenues_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.revenues_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة مطابقة للفواتير
        self.add_button = QPushButton("➕ إضافة إيراد")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_revenue)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_revenue)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_revenue)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث بسيط بدون قائمة منسدلة مطابق للفواتير
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة مطابقة للفواتير
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')  # لون مطابق للعملاء والموردين والعمال
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # ربط زر العرض بالوظيفة الأساسية
        self.view_button.clicked.connect(self.view_revenue)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)  # لون مطابق للعملاء والموردين والعمال
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)



        # إنشاء قائمة التصدير مطابقة تماماً لقسم العملاء
        export_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 5px 10px 5px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: left;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                padding-left: 110px !important;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        detailed_action = QAction("📊 تقرير تفصيلي", self)
        detailed_action.triggered.connect(self.export_detailed_report)
        export_menu.addAction(detailed_action)

        balance_action = QAction("💰 تقرير الأرصدة", self)
        balance_action.triggered.connect(self.export_balance_report)
        export_menu.addAction(balance_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(self.export_custom)
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(self.export_backup)
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(self.restore_backup)
        export_menu.addAction(restore_action)

        # تخصيص موضع وعرض القائمة
        def show_export_menu():
            """عرض قائمة التصدير فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.export_button.mapToGlobal(self.export_button.rect().topLeft())

            # تحديد عرض القائمة مع تكبير ربع درجة
            button_width = self.export_button.width()
            export_menu.setFixedWidth(max(button_width, 190))

            # ترحيل القائمة نصف درجة لليسار
            button_pos.setX(button_pos.x() - 10)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = export_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            export_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة بدلاً من setMenu
        self.export_button.clicked.connect(show_export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()

        # إجمالي الإيرادات مطور ليتشابه مع الفواتير مع حفظ المقاسات والخط الداخلي
        self.total_label = QLabel("إجمالي الإيرادات: 0.00")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)

        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.columns_visibility_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        # تهيئة حالة الأزرار (جميع الأزرار مفعلة ومنيرة في البداية)
        QTimer.singleShot(100, self.initialize_button_states)

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث تغيير التحديد لتفعيل/تعطيل الأزرار - مطابق للمشاريع
            self.revenues_table.itemSelectionChanged.connect(self.on_revenues_selection_changed)

        except Exception as e:
            print(f"❌ خطأ في ربط أحداث الإيرادات: {str(e)}")
            print(f"تفاصيل الخطأ: {e}")

    def on_revenues_selection_changed(self):
        """معالج تغيير التحديد مطابق للمشاريع"""
        try:
            print("🚨 تم استدعاء معالج تحديد الإيرادات!")

            # تسجيل أن المستخدم تفاعل مع الجدول
            self.user_interacted_with_table = True
            print("👆 المستخدم تفاعل مع الجدول - سيتم تطبيق خاصية الإغلاق")

            self.update_selected_revenues_list()
            self.update_button_states()

        except Exception as e:
            print(f"تحذير: فشل في معالجة تحديد الإيرادات: {e}")
            # المتابعة بدون معالجة التحديد

    def update_selected_revenues_list(self):
        """تحديث قائمة الإيرادات المحددة مطابق للمشاريع"""
        try:
            if not hasattr(self, 'selected_revenues'):
                self.selected_revenues = []

            self.selected_revenues.clear()
            selected_rows = set()

            # الحصول على الصفوف المحددة
            for item in self.revenues_table.selectedItems():
                selected_rows.add(item.row())

            # إضافة معرفات الإيرادات المحددة
            for row in selected_rows:
                try:
                    id_item = self.revenues_table.item(row, 0)
                    if id_item:
                        # استخدام البيانات المخفية للحصول على الـ ID الفعلي
                        revenue_id = id_item.data(Qt.UserRole)
                        if revenue_id and revenue_id not in self.selected_revenues:
                            self.selected_revenues.append(revenue_id)
                except (ValueError, AttributeError):
                    continue

        except Exception as e:
            print(f"تحذير: فشل في تحديث قائمة الإيرادات المحددة: {e}")
            # المتابعة بدون تحديث القائمة

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مطابق للمشاريع"""
        try:
            # إذا لم يتفاعل المستخدم مع الجدول بعد، لا نغير حالة الأزرار
            if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                print("🔥 المستخدم لم يتفاعل مع الجدول بعد - الأزرار تبقى مفعلة")
                return

            if not hasattr(self, 'selected_revenues'):
                self.selected_revenues = []

            selected_count = len(self.selected_revenues)
            has_selection = selected_count > 0
            has_single_selection = selected_count == 1



            # الأزرار التي تحتاج تحديد واحد فقط
            self.set_button_visibility(self.edit_button, has_single_selection)
            self.set_button_visibility(self.view_button, has_single_selection)

            # الأزرار التي تعمل مع التحديد المتعدد
            self.set_button_visibility(self.delete_button, has_selection)

            # الأزرار المتاحة دائماً
            self.set_button_visibility(self.add_button, True)  # زر الإضافة متاح دائماً

            # تحديث نص زر الحذف
            if has_selection:
                if selected_count > 1:
                    self.delete_button.setText(f"🗑️ حذف ({selected_count})")
                else:
                    self.delete_button.setText("🗑️ حذف")

        except Exception as e:
            print(f"تحذير: فشل في تحديث حالة الأزرار: {e}")
            # المتابعة بدون تحديث حالة الأزرار

    def set_button_visibility(self, button, visible):
        """تعيين رؤية الزر مع الحفاظ على الألوان الأصلية - مطابق للمشاريع"""
        try:
            if button:
                # التحقق من أن المستخدم تفاعل مع الجدول قبل تطبيق التأثيرات
                if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                    # إذا لم يتفاعل المستخدم بعد، نبقي الزر مفعلاً ومنيراً
                    button.setEnabled(True)
                    button.setGraphicsEffect(None)
                    return

                # لا نستخدم setEnabled للحفاظ على الألوان الأصلية

                if visible:
                    # إظهار الزر بشفافية كاملة مع إعادة تفعيل الأحداث
                    button.setGraphicsEffect(None)
                    button.setAttribute(Qt.WA_TransparentForMouseEvents, False)
                    button.setFocusPolicy(Qt.StrongFocus)
                    button.setProperty("custom_disabled", False)
                    button.setEnabled(True)
                else:
                    # تعطيل الزر مع الحفاظ على الألوان الأصلية - شفافية 50%
                    from PyQt5.QtWidgets import QGraphicsOpacityEffect
                    opacity_effect = QGraphicsOpacityEffect()
                    opacity_effect.setOpacity(0.5)  # 50% شفافية
                    button.setGraphicsEffect(opacity_effect)

                    # منع جميع الأحداث دون تغيير المظهر
                    button.setAttribute(Qt.WA_TransparentForMouseEvents, True)
                    button.setFocusPolicy(Qt.NoFocus)
                    button.setProperty("custom_disabled", True)

        except Exception as e:
            print(f"تحذير: فشل في تعيين رؤية الزر: {e}")
            # المتابعة بدون تأثيرات الرؤية
            # في حالة فشل التأثير، استخدم الطريقة البسيطة
            if button:
                button.setEnabled(True)  # نبقيه مفعلاً في حالة الخطأ

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة أزرار الإيرادات...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة إيراد"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.export_button, "📤 تصدير ▼"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    import re
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
        except Exception as e:
            print(f"تحذير: فشل في تهيئة حالة الأزرار: {e}")
            # المتابعة بدون تهيئة الأزرار

    def setup_multi_selection(self):
        """إعداد التحديد المتعدد مطابق للمشاريع"""
        try:
            # تهيئة قائمة العناصر المحددة
            self.selected_items = []
            # متغير لتتبع ما إذا كان المستخدم قد تفاعل مع الجدول
            self.user_interacted_with_table = False
        except Exception as e:
            print(f"❌ خطأ في إعداد التحديد المتعدد للإيرادات: {e}")
            # المتابعة بدون تحديد متعدد

    def create_advanced_revenues_table(self):
        """إنشاء جدول الإيرادات المتطور والمحسن مطابق للموردين"""
        styled_table = StyledTable()
        self.revenues_table = styled_table.table
        self.revenues_table.setColumnCount(9)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🔢 ID",
            "📝 الوصف",
            "💎 المبلغ",
            "📅 التاريخ",
            "📂 المصدر",
            "🏗️ المشروع",
            "🏠 العقار",
            "📋 الفاتورة",
            "📋 ملاحظات"
        ]
        self.revenues_table.setHorizontalHeaderLabels(headers)

        # إعدادات عرض الأعمدة مع التكيف التلقائي مطابقة للعملاء
        header = self.revenues_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # الوصف
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # التاريخ
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # المصدر
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # المشروع
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # العقار
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # الفاتورة
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # الملاحظات

        # تحديد عرض الأعمدة الثابتة مطابق للعملاء
        self.revenues_table.setColumnWidth(0, 120)  # ID
        self.revenues_table.setColumnWidth(1, 300)  # الوصف

        self.revenues_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.revenues_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.revenues_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.revenues_table.setAlternatingRowColors(True)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.revenues_table)
        self.setup_multi_selection()

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.revenues_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.revenues_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للفواتير
        self.revenues_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير
        header = self.revenues_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير
        self.revenues_table.verticalHeader().setDefaultSectionSize(45)
        self.revenues_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_revenues_table()

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.revenues_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.revenues_table, event)

        self.revenues_table.wheelEvent = wheelEvent

        # ربط خاصية النقر المزدوج للتعديل مطابق للعملاء
        self.revenues_table.cellDoubleClicked.connect(self.on_cell_double_clicked)

    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية مطابق للعملاء"""
        try:
            self.edit_revenue()
        except Exception as e:
            pass  # خطأ في النقر المزدوج

    def add_watermark_to_revenues_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.revenues_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.revenues_table.viewport())
                paint_watermark(painter, self.revenues_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.revenues_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.revenues_table.viewport().update()
        self.revenues_table.repaint()

    def refresh_data(self):
        """تحديث بيانات الإيرادات في الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # الحصول على جميع الإيرادات من قاعدة البيانات
            revenues = self.session.query(Revenue).all()
            self.populate_table(revenues)
            self.update_total(revenues)

        except Exception as e:
            print(f"❌ خطأ في تحديث بيانات الإيرادات: {e}")
            if hasattr(self, 'show_error_message'):
                self.show_error_message(f"فشل في تحديث البيانات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def populate_table(self, revenues):
        """ملء جدول الإيرادات بالبيانات مع أيقونات متطورة مطابقة للفواتير"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.revenues_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.revenues_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن وأيقونات متطورة
            for row, revenue in enumerate(revenues):
                try:
                    self.revenues_table.insertRow(row)

                    # 1. الرقم التسلسلي مع أيقونة ثابتة مطابق للعملاء
                    # الإيرادات تستخدم أرقام متسلسلة بدلاً من ID الفعلي
                    sequential_number = row + 1
                    id_item = QTableWidgetItem(f"🔢 {sequential_number}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
                    # حفظ الـ ID الفعلي كبيانات مخفية للاستخدام في العمليات
                    id_item.setData(Qt.UserRole, revenue.id)
                    self.revenues_table.setItem(row, 0, id_item)

                    # 2. عنوان الإيراد - مطابق للعملاء
                    def create_item(icon, text, default="No Data"):
                        display_text = text if text and text.strip() else default
                        item = QTableWidgetItem(f"{icon} {display_text}")
                        item.setTextAlignment(Qt.AlignCenter)
                        if display_text == default:
                            item.setForeground(QColor("#ef4444"))
                        return item

                    self.revenues_table.setItem(row, 1, create_item("📝", revenue.title))

                    # 3. المبلغ - مطابق للعملاء
                    try:
                        if revenue.amount and revenue.amount > 0:
                            amount_formatted = f"{int(revenue.amount):,}".replace(',', '٬')
                            amount_display = f"{amount_formatted} جنيه"
                        else:
                            amount_display = None
                    except Exception:
                        amount_display = None

                    self.revenues_table.setItem(row, 2, create_item("💎", amount_display))

                    # 4. التاريخ - مطابق للعملاء
                    try:
                        if revenue.date:
                            date_display = revenue.date.strftime('%Y-%m-%d')
                        else:
                            date_display = None
                    except Exception:
                        date_display = None

                    self.revenues_table.setItem(row, 3, create_item("📅", date_display))

                    # 5. الفئة مع أيقونة مناسبة
                    category = revenue.category or "غير محدد"
                    category_icons = {
                        "مبيعات": "🛒",
                        "خدمات": "🔧",
                        "استشارات": "💼",
                        "تدريب": "🎓",
                        "إيجار": "🏠",
                        "استثمار": "📈",
                        "فوائد": "💰",
                        "عمولة": "🤝",
                        "هدايا": "🎁",
                        "أخرى": "💼"
                    }
                    category_icon = category_icons.get(category, "📂")
                    # استخدام create_item للتوحيد مع العملاء
                    self.revenues_table.setItem(row, 4, create_item("📂", category if category != "غير محدد" else None))

                    # 6. المشروع المرتبط - جديد
                    project_text = revenue.project.name if revenue.project else None
                    self.revenues_table.setItem(row, 5, create_item("🏗️", project_text))

                    # 7. العقار المرتبط - جديد
                    property_text = revenue.property.title if revenue.property else None
                    self.revenues_table.setItem(row, 6, create_item("🏠", property_text))

                    # 8. الفاتورة المرتبطة مع أيقونة
                    try:
                        if revenue.invoice:
                            client_name = revenue.invoice.client.name if revenue.invoice.client else "عميل غير محدد"
                            invoice_display = f"📋 {revenue.invoice.invoice_number} - 👤 {client_name}"
                            invoice_color = QColor("#0284c7")
                            tooltip = f"📋 الفاتورة: {revenue.invoice.invoice_number}\n👤 العميل: {client_name}"
                        else:
                            invoice_display = "📋 غير مرتبط بفاتورة"
                            invoice_color = QColor("#6b7280")
                            tooltip = "📋 هذا الإيراد غير مرتبط بأي فاتورة"
                    except Exception:
                        invoice_display = "📋 غير محدد"
                        invoice_color = QColor("#6b7280")
                        tooltip = "📋 معلومات الفاتورة غير متاحة"
                    # استخدام create_item للتوحيد مع العملاء
                    invoice_text = revenue.invoice.invoice_number if revenue.invoice else None
                    self.revenues_table.setItem(row, 7, create_item("📋", invoice_text))

                    # 9. الملاحظات - مطابق للعملاء
                    notes = revenue.notes or None
                    self.revenues_table.setItem(row, 8, create_item("📋", notes))

                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية

                    continue

            # إعادة تمكين تحديث الجدول
            self.revenues_table.setUpdatesEnabled(True)

        except Exception as e:
            # إعادة تمكين تحديث الجدول في حالة حدوث خطأ
            self.revenues_table.setUpdatesEnabled(True)
            self.show_error_message(f"حدث خطأ أثناء تحديث جدول الإيرادات: {str(e)}")

    def update_total(self, revenues):
        """تحديث إجمالي الإيرادات"""
        total = sum(revenue.amount for revenue in revenues)
        self.total_label.setText(f"إجمالي الإيرادات: {format_currency(total)}")

    def filter_revenues(self):
        """تصفية الإيرادات بناءً على نص البحث والفئة"""
        search_text = self.search_edit.text().strip().lower()
        category = getattr(self, 'current_filter_value', None)

        # بناء الاستعلام مع الربط بالعقارات والمشاريع
        from database import Property, Project
        query = self.session.query(Revenue).outerjoin(Property).outerjoin(Project)

        # تطبيق تصفية النص - تشمل البحث في العقارات والمشاريع
        if search_text:
            query = query.filter(
                Revenue.title.like(f"%{search_text}%") |
                Revenue.notes.like(f"%{search_text}%") |
                Property.title.like(f"%{search_text}%") |
                Property.location.like(f"%{search_text}%") |
                Project.name.like(f"%{search_text}%")
            )

        # تطبيق تصفية الفئة
        if category is not None:
            query = query.filter(Revenue.category == category)

        # تنفيذ الاستعلام
        revenues = query.all()

        # تحديث الجدول والإجمالي
        self.populate_table(revenues)
        self.update_total(revenues)

    def add_revenue(self):
        """إضافة إيراد جديد"""
        dialog = RevenueDialog(parent=self, revenue=None, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء إيراد جديد في قاعدة البيانات
                revenue = Revenue(**data)
                self.session.add(revenue)
                self.session.commit()

                # إذا كان الإيراد مرتبط بفاتورة، تحديث المبلغ المدفوع للفاتورة
                if revenue.invoice_id:
                    invoice = self.session.query(Invoice).get(revenue.invoice_id)
                    if invoice:
                        invoice.paid_amount += revenue.amount
                        if invoice.paid_amount >= invoice.total_amount:
                            invoice.status = 'paid'
                        elif invoice.paid_amount > 0:
                            invoice.status = 'partially_paid'
                        self.session.commit()

                self.show_success_message("تمت إضافة الإيراد بنجاح")
                self.refresh_data()

    def edit_revenue(self):
        """تعديل بيانات إيراد"""
        selected_row = self.revenues_table.currentRow()
        if selected_row < 0:
            self.show_error_message("الرجاء اختيار إيراد من القائمة")
            return

        # استخراج الـ ID الفعلي من البيانات المخفية
        id_item = self.revenues_table.item(selected_row, 0)
        revenue_id = id_item.data(Qt.UserRole)
        revenue = self.session.query(Revenue).get(revenue_id)

        if not revenue:
            self.show_error_message("لم يتم العثور على الإيراد")
            return

        # حفظ البيانات القديمة للإيراد
        old_amount = revenue.amount
        old_invoice_id = revenue.invoice_id

        dialog = RevenueDialog(parent=self, revenue=revenue, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # تحديث بيانات الإيراد
                for key, value in data.items():
                    setattr(revenue, key, value)

                # إذا تغير المبلغ أو الفاتورة، تحديث المبلغ المدفوع للفواتير المتأثرة
                if old_invoice_id != revenue.invoice_id or old_amount != revenue.amount:
                    # إذا كان هناك فاتورة قديمة، تحديث المبلغ المدفوع لها
                    if old_invoice_id:
                        old_invoice = self.session.query(Invoice).get(old_invoice_id)
                        if old_invoice:
                            old_invoice.paid_amount -= old_amount
                            if old_invoice.paid_amount <= 0:
                                old_invoice.paid_amount = 0
                                old_invoice.status = 'pending'
                            elif old_invoice.paid_amount < old_invoice.total_amount:
                                old_invoice.status = 'partially_paid'

                    # إذا كان هناك فاتورة جديدة، تحديث المبلغ المدفوع لها
                    if revenue.invoice_id:
                        new_invoice = self.session.query(Invoice).get(revenue.invoice_id)
                        if new_invoice:
                            new_invoice.paid_amount += revenue.amount
                            if new_invoice.paid_amount >= new_invoice.total_amount:
                                new_invoice.status = 'paid'
                            elif new_invoice.paid_amount > 0:
                                new_invoice.status = 'partially_paid'

                self.session.commit()
                self.show_success_message("تم تحديث بيانات الإيراد بنجاح")
                self.refresh_data()

    def delete_selected_items(self):
        """حذف الإيرادات المحددة"""
        try:
            self.update_selected_items()
            if not self.selected_items:
                return

            count = len(self.selected_items)
            if count == 1:
                self.delete_revenue()
            else:
                if show_revenue_advanced_warning(self, "تأكيد الحذف", f"هل تريد حذف {count} إيراد؟") == QDialog.Accepted:
                    for item_id in self.selected_items:
                        revenue = self.session.query(Revenue).get(item_id)
                        if revenue:
                            self.session.delete(revenue)
                    self.session.commit()
                    self.load_revenues()
        except Exception as e:
            print(f"❌ خطأ خطير في حذف الإيرادات المحددة: {e}")
            if hasattr(self, 'show_error_message'):
                self.show_error_message(f"فشل في حذف الإيرادات: {str(e)}")
            # لا نتجاهل هذا الخطأ لأنه يؤثر على سلامة البيانات

    def show_context_menu(self, position):
        """عرض القائمة السياقية للإيرادات"""
        try:
            menu = QMenu(self)

            single_actions = [
                ("✏️ تعديل", self.edit_revenue),
                ("👁️ عرض التفاصيل", self.view_revenue_details),
                ("🗑️ حذف", self.delete_revenue)
            ]

            multi_actions = [
                ("🗑️ حذف {count} إيراد", self.delete_selected_items)
            ]

            self.create_context_menu_actions(menu, single_actions, multi_actions)
            menu.exec_(self.revenues_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في عرض القائمة السياقية للإيرادات: {e}")
            # عرض قائمة بسيطة كبديل
            try:
                menu = QMenu(self)
                menu.addAction("✏️ تعديل", self.edit_revenue)
                menu.addAction("🗑️ حذف", self.delete_revenue)
                menu.exec_(self.revenues_table.mapToGlobal(position))
            except:
                pass  # فشل في عرض القائمة البديلة

    def delete_revenue(self):
        """حذف إيراد مع نافذة تأكيد متطورة"""
        try:
            selected_row = self.revenues_table.currentRow()
            if selected_row < 0:
                self.show_warning_message("الرجاء اختيار إيراد من القائمة")
                return

            # استخراج الـ ID الفعلي من البيانات المخفية
            id_item = self.revenues_table.item(selected_row, 0)
            revenue_id = id_item.data(Qt.UserRole)
            revenue = self.session.query(Revenue).get(revenue_id)

            if not revenue:
                self.show_error_message("لم يتم العثور على الإيراد")
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            dialog = DeleteRevenueDialog(self, revenue)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # إذا كان الإيراد مرتبط بفاتورة، تحديث المبلغ المدفوع للفاتورة
                    if revenue.invoice_id:
                        invoice = self.session.query(Invoice).get(revenue.invoice_id)
                        if invoice:
                            invoice.paid_amount -= revenue.amount
                            if invoice.paid_amount <= 0:
                                invoice.paid_amount = 0
                                invoice.status = 'pending'
                            elif invoice.paid_amount < invoice.total_amount:
                                invoice.status = 'partially_paid'

                    # حذف الإيراد من قاعدة البيانات
                    self.session.delete(revenue)
                    self.session.commit()

                    # إظهار رسالة نجاح متطورة
                    self.show_success_message(f"تم حذف الإيراد '{revenue.title}' بنجاح")

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف الإيراد: {str(e)}")
        except Exception as e:
            self.show_error_message(f"خطأ في حذف الإيراد: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مطابقة لنافذة الحذف"""
        show_revenue_advanced_warning(self, "تحذير", message)

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة مطابقة لنافذة الحذف"""
        show_revenue_advanced_info(self, "تم", message, "✅")

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة مطابقة لنافذة الحذف"""
        show_revenue_advanced_error(self, "خطأ", message)

    def show_info_message(self, message):
        """إظهار رسالة معلومات متطورة مطابقة لنافذة الحذف"""
        show_revenue_advanced_info(self, "معلومات", message)

    def view_revenue(self):
        """عرض تفاصيل الإيراد في نافذة متطورة مع معالجة محسنة للأخطاء"""
        try:
            print("🔍 بدء عرض تفاصيل الإيراد...")

            # التحقق من وجود جلسة قاعدة البيانات
            if not hasattr(self, 'session') or not self.session:
                self.show_error_message("لا توجد جلسة قاعدة بيانات نشطة")
                return

            # التحقق من وجود الجدول
            if not hasattr(self, 'revenues_table') or not self.revenues_table:
                self.show_error_message("جدول الإيرادات غير متاح")
                return

            selected_row = self.revenues_table.currentRow()


            if selected_row < 0:
                self.show_error_message("الرجاء اختيار إيراد من القائمة")
                return

            # التحقق من وجود العنصر في الجدول
            item = self.revenues_table.item(selected_row, 0)
            if not item:
                self.show_error_message("لا يمكن قراءة بيانات الإيراد المحدد")
                return

            # استخراج معرف الإيراد مع التعامل مع الرموز التعبيرية
            id_text = item.text()
            import re
            revenue_id = int(re.sub(r'[^\d]', '', id_text))

            # البحث عن الإيراد في قاعدة البيانات
            try:
                revenue = self.session.query(Revenue).get(revenue_id)
                if not revenue:
                    self.show_error_message("لم يتم العثور على الإيراد")
                    return
            except Exception as db_error:
                self.show_error_message(f"خطأ في قاعدة البيانات: {str(db_error)}")
                return

            # إنشاء نافذة المعلومات المتطورة مع معالجة الأخطاء
            try:
                print("🔄 إنشاء نافذة المعلومات...")
                info_dialog = RevenueInfoDialog(parent=self, revenue=revenue)
                info_dialog.exec_()
            except Exception as dialog_error:
                # عرض نافذة بديلة بسيطة
                try:
                    self.show_simple_revenue_dialog(revenue)
                except Exception as fallback_error:
                    self.show_error_message(f"فشل في عرض تفاصيل الإيراد: {str(dialog_error)}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في عرض تفاصيل الإيراد: {str(e)}")

    def show_simple_revenue_dialog(self, revenue):
        """عرض نافذة بسيطة لتفاصيل الإيراد كبديل آمن"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QTextEdit

            dialog = QDialog(self)
            dialog.setWindowTitle(f"تفاصيل الإيراد - {revenue.title}")
            dialog.setMinimumSize(600, 400)
            dialog.setModal(True)

            layout = QVBoxLayout()

            # معلومات أساسية
            info_text = f"""
تفاصيل الإيراد

المعرف: {revenue.id}
العنوان: {revenue.title or 'غير محدد'}
المبلغ: {revenue.amount or 0} جنيه
الفئة: {revenue.category or 'غير محدد'}
التاريخ: {revenue.date.strftime('%Y-%m-%d') if revenue.date else 'غير محدد'}
الفاتورة: {revenue.invoice.invoice_number if revenue.invoice else 'غير مرتبط'}
الملاحظات: {revenue.notes or 'لا توجد ملاحظات'}
            """

            text_widget = QTextEdit()
            text_widget.setPlainText(info_text)
            text_widget.setReadOnly(True)
            layout.addWidget(text_widget)

            # زر إغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"فشل في عرض النافذة البديلة: {str(e)}")

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.status_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الفئات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        # إنشاء القائمة المنسدلة
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية مع أيقونات مطابقة للعملاء
        filter_options = [
            ("جميع الفئات", None),
            ("💰 مبيعات", "مبيعات"),
            ("🛠️ خدمات", "خدمات"),
            ("💼 استشارات", "استشارات"),
            ("💎 أخرى", "أخرى")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

    def show_filter_menu(self):
        """عرض القائمة المنسدلة للتصفية"""
        # تحديد موقع القائمة تحت الإطار
        button_rect = self.status_filter_frame.geometry()
        menu_pos = self.status_filter_frame.mapToGlobal(button_rect.bottomLeft())
        menu_pos.setY(menu_pos.y() + 5)  # إضافة مسافة صغيرة

        self.filter_menu.exec_(menu_pos)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على الإطار"""
        if event.button() == Qt.LeftButton:
            self.show_filter_menu()

    def set_filter(self, filter_value, filter_text):
        """تعيين التصفية وتحديث النص"""
        self.current_filter_label.setText(filter_text)
        self.current_filter_value = filter_value
        self.filter_revenues()



    def show_statistics(self):
        """عرض نافذة إحصائيات الإيرادات"""
        try:
            dialog = RevenuesStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        self.columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        self.columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("📝 الوصف", 1),
            ("💰 المبلغ", 2),
            ("📅 التاريخ", 3),
            ("📂 الفئة", 4),
            ("🧑‍💼 العميل", 5),
            ("📋 ملاحظات", 6)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            self.columns_menu.addAction(action)

        # إضافة فاصل
        self.columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        self.columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        self.columns_menu.addAction(hide_all_action)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            self.columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = self.columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            self.columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'revenues_table') and self.revenues_table:
                if visible:
                    self.revenues_table.showColumn(column_index)
                else:
                    self.revenues_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"تحذير: فشل في تبديل رؤية العمود: {e}")
            # المتابعة بدون تبديل العمود

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'revenues_table') and self.revenues_table:
                for i in range(self.revenues_table.columnCount()):
                    self.revenues_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"تحذير: فشل في إظهار جميع الأعمدة: {e}")
            # المتابعة بدون إظهار الأعمدة

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'revenues_table') and self.revenues_table:
                for i in range(self.revenues_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.revenues_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"تحذير: فشل في إخفاء جميع الأعمدة: {e}")
            # المتابعة بدون إخفاء الأعمدة

    def export_to_excel(self):
        """تصدير الإيرادات إلى Excel"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "الإيرادات.csv", "CSV Files (*.csv)"
            )

            if file_path:
                revenues = self.session.query(Revenue).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['الرقم', 'العنوان', 'المبلغ', 'التاريخ', 'الفئة', 'العميل', 'الفاتورة', 'الملاحظات'])

                    # كتابة البيانات
                    for revenue in revenues:
                        date_str = revenue.date.strftime("%Y-%m-%d") if revenue.date else ""
                        client_name = revenue.client.name if revenue.client else ""
                        invoice_info = f"فاتورة #{revenue.invoice_id}" if revenue.invoice_id else ""
                        writer.writerow([
                            revenue.id,
                            revenue.title,
                            revenue.amount,
                            date_str,
                            revenue.category or "",
                            client_name,
                            invoice_info,
                            revenue.notes or ""
                        ])

                self.show_success_message(f"تم تصدير الإيرادات بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير الإيرادات إلى PDF"""
        try:

            revenues = self.session.query(Revenue).all()

            if not revenues:
                self.show_info_message("لا توجد إيرادات للتصدير")
                return

            # إنشاء محتوى HTML
            html_content = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير الإيرادات</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #27ae60; text-align: center; }}
                    table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                    th {{ background-color: #f2f2f2; }}
                    .total {{ font-weight: bold; background-color: #e8f5e8; }}
                </style>
            </head>
            <body>
                <h1>📋 تقرير الإيرادات</h1>
                <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>
                <p><strong>وقت الإنشاء:</strong> {QTime.currentTime().toString('hh:mm:ss')}</p>

                <table>
                    <tr>
                        <th>الرقم</th>
                        <th>العنوان</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                        <th>الفئة</th>
                        <th>العميل</th>
                        <th>الفاتورة</th>
                        <th>الملاحظات</th>
                    </tr>
            """

            total_amount = 0
            for revenue in revenues:
                date_str = revenue.date.strftime("%Y-%m-%d") if revenue.date else ""
                client_name = revenue.client.name if revenue.client else ""
                invoice_info = f"فاتورة #{revenue.invoice_id}" if revenue.invoice_id else ""
                total_amount += revenue.amount

                html_content += f"""
                    <tr>
                        <td>{revenue.id}</td>
                        <td>{revenue.title}</td>
                        <td>{int(revenue.amount):,} جنيه</td>
                        <td>{date_str}</td>
                        <td>{revenue.category or ""}</td>
                        <td>{client_name}</td>
                        <td>{invoice_info}</td>
                        <td>{revenue.notes or ""}</td>
                    </tr>
                """

            html_content += f"""
                    <tr class="total">
                        <td colspan="2"><strong>الإجمالي</strong></td>
                        <td><strong>{int(total_amount):,} جنيه</strong></td>
                        <td colspan="5"></td>
                    </tr>
                </table>
            </body>
            </html>
            """

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإيرادات", "تقرير_الإيرادات.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                self.show_success_message(f"تم تصدير الإيرادات إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_csv(self):
        """تصدير الإيرادات إلى CSV"""
        self.export_to_excel()  # نفس الوظيفة

    def export_to_json(self):
        """تصدير الإيرادات إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف JSON", "الإيرادات.json", "JSON Files (*.json)"
            )

            if file_path:
                revenues = self.session.query(Revenue).all()

                revenues_data = []
                for revenue in revenues:
                    revenue_data = {
                        'id': revenue.id,
                        'title': revenue.title,
                        'amount': revenue.amount,
                        'date': revenue.date.strftime("%Y-%m-%d") if revenue.date else "",
                        'category': revenue.category or "",
                        'client_name': revenue.client.name if revenue.client else "",
                        'client_id': revenue.client_id,
                        'invoice_id': revenue.invoice_id,
                        'notes': revenue.notes or ""
                    }
                    revenues_data.append(revenue_data)

                export_data = {
                    "export_info": {
                        "export_date": QDate.currentDate().toString('yyyy-MM-dd'),
                        "export_time": QTime.currentTime().toString('hh:mm:ss'),
                        "total_revenues": len(revenues_data),
                        "total_amount": sum(revenue.amount for revenue in revenues),
                        "exported_by": "نظام إدارة الإيرادات"
                    },
                    "revenues": revenues_data
                }

                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم تصدير الإيرادات بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير: {str(e)}")

    def export_custom(self):
        """إظهار نافذة التصدير المخصص مطابقة للعملاء والموردين والعمال"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel, QWidget
            from PyQt5.QtCore import Qt

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص للإيرادات - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(400, 380)  # تقليل الارتفاع

            # إزالة علامة الاستفهام وتحسين شريط العنوان مطابق للبرنامج
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

            # تخصيص شريط العنوان مطابق للبرنامج
            try:
                from ui.title_bar_utils import TitleBarStyler
                TitleBarStyler.apply_advanced_title_bar_styling(dialog)
            except Exception as e:
                print(f"تحذير: فشل في تصميم شريط العنوان للحوار: {e}")
                # المتابعة بدون تصميم شريط العنوان

            # تطبيق نمط النافذة مطابق تماماً لنافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # التخطيط الرئيسي مضغوط
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(15, 10, 15, 10)
            layout.setSpacing(8)

            # العنوان الرئيسي مضغوط
            title_container = QWidget()
            title_container.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                }
            """)

            title_inner_layout = QVBoxLayout(title_container)
            title_inner_layout.setContentsMargins(0, 0, 0, 0)
            title_inner_layout.setSpacing(3)

            # الأيقونة والعنوان الرئيسي مضغوط
            main_title = QLabel("🔧 تصدير مخصص للإيرادات")
            main_title.setAlignment(Qt.AlignCenter)
            main_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 22px;
                    font-weight: bold;
                    background: transparent;
                    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                    padding: 5px;
                }
            """)

            # العنوان الفرعي التوضيحي مضغوط
            subtitle = QLabel("اختر البيانات المراد تصديرها بدقة")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setStyleSheet("""
                QLabel {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 12px;
                    font-weight: normal;
                    background: transparent;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            title_inner_layout.addWidget(main_title)
            title_inner_layout.addWidget(subtitle)
            layout.addWidget(title_container)

            # إنشاء المجموعات مع علامات الصح الخارجية
            self.create_revenue_export_groups(layout)

            # أزرار التحكم مضغوطة
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(10)

            # زر الإلغاء مطابق للبرنامج الرئيسي
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export_revenues(dialog))

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def create_revenue_export_groups(self, layout):
        """إنشاء مجموعات التصدير مع علامات الصح الخارجية"""
        try:
            from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox
            from PyQt5.QtCore import Qt

            # مجموعة البيانات الأساسية مطابقة لنافذة الإحصائيات
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)
            basic_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            basic_title = QLabel("💰 البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)

            self.export_id = QCheckBox("🆔 الرقم التعريفي")
            self.export_title = QCheckBox("💰 عنوان الإيراد")
            self.export_amount = QCheckBox("💵 المبلغ")
            self.export_date = QCheckBox("📅 التاريخ")
            self.export_client = QCheckBox("👤 العميل")

            # تحديد افتراضي
            self.export_title.setChecked(True)
            self.export_amount.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات مع علامة صح خارجية
            checkboxes_data = [
                (self.export_id, "#3B82F6"),
                (self.export_title, "#10B981"),
                (self.export_amount, "#F59E0B"),
                (self.export_date, "#EF4444"),
                (self.export_client, "#8B5CF6")
            ]

            for checkbox, color in checkboxes_data:
                self.create_checkbox_item(checkbox, color, basic_layout)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)

            # مجموعة البيانات المالية
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)
            financial_main_layout.setContentsMargins(15, 5, 15, 5)

            financial_title = QLabel("📊 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)

            self.export_invoice_id = QCheckBox("🧾 رقم الفاتورة")
            self.export_payment_status = QCheckBox("💳 حالة الدفع")
            self.export_category = QCheckBox("📂 التصنيف")

            self.export_payment_status.setChecked(True)

            financial_checkboxes_data = [
                (self.export_invoice_id, "#10B981"),
                (self.export_payment_status, "#F59E0B"),
                (self.export_category, "#EF4444")
            ]

            for checkbox, color in financial_checkboxes_data:
                self.create_checkbox_item(checkbox, color, financial_layout)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)

            # مجموعة البيانات الإضافية
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)
            additional_main_layout.setContentsMargins(15, 5, 15, 5)

            additional_title = QLabel("📝 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)

            self.export_notes = QCheckBox("📋 الملاحظات")
            self.export_created_date = QCheckBox("📅 تاريخ الإنشاء")
            self.export_statistics = QCheckBox("📊 إضافة الإحصائيات")

            additional_checkboxes_data = [
                (self.export_notes, "#8B5CF6"),
                (self.export_created_date, "#F59E0B"),
                (self.export_statistics, "#3B82F6")
            ]

            for checkbox, color in additional_checkboxes_data:
                self.create_checkbox_item(checkbox, color, additional_layout)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

        except Exception as e:
            print(f"تحذير: فشل في إنشاء مجموعات التصدير: {e}")
            # المتابعة بدون مجموعات التصدير

    def create_checkbox_item(self, checkbox, color, parent_layout):
        """إنشاء عنصر CheckBox مع علامة صح خارجية"""
        try:
            from PyQt5.QtWidgets import QWidget, QHBoxLayout, QLabel
            from PyQt5.QtCore import Qt

            # إنشاء عنصر مطابق لعناصر الإحصائيات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 2px;
                    margin: 0px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(8)
            item_layout.setContentsMargins(8, 3, 8, 3)

            # تصميم الـ CheckBox مع علامة صح واضحة
            checkbox.setStyleSheet(f"""
                QCheckBox {{
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: normal;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    spacing: 12px;
                }}
                QCheckBox::indicator {{
                    width: 20px;
                    height: 20px;
                    border: 2px solid {color};
                    border-radius: 4px;
                    background: rgba(255, 255, 255, 0.1);
                }}
                QCheckBox::indicator:checked {{
                    background: {color};
                    border: 2px solid #ffffff;
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                }}
                QCheckBox::indicator:hover {{
                    background: rgba(255, 255, 255, 0.2);
                    border: 2px solid rgba(255, 255, 255, 0.8);
                }}
            """)

            # إضافة علامة صح خارجية محسنة
            check_label = QLabel("")
            check_label.setFixedSize(30, 25)
            check_label.setAlignment(Qt.AlignCenter)
            check_label.setStyleSheet("""
                QLabel {
                    color: #10B981;
                    font-size: 20px;
                    font-weight: bold;
                    background: rgba(16, 185, 129, 0.1);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 8px;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    padding: 2px;
                }
            """)

            # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
            def create_toggle_function(label):
                def toggle_check(state):
                    if state == 2:  # محدد
                        label.setText("✓")
                    else:
                        label.setText("")
                return toggle_check

            checkbox.stateChanged.connect(create_toggle_function(check_label))

            # إظهار علامة الصح للعناصر المحددة مسبقاً
            if checkbox.isChecked():
                check_label.setText("✓")

            item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
            item_layout.addWidget(checkbox)
            parent_layout.addWidget(item_widget)

        except Exception as e:
            print(f"تحذير: فشل في إنشاء CheckBox للتصدير: {e}")
            # المتابعة بدون هذا CheckBox

    def perform_custom_export_revenues(self, dialog):
        """تنفيذ التصدير المخصص للإيرادات"""
        try:
            import csv
            from datetime import datetime
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_إيرادات_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                revenues = self.session.query(Revenue).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إضافة الإحصائيات إذا تم اختيارها
                    if self.export_statistics.isChecked():
                        writer.writerow(['تصدير مخصص للإيرادات'])
                        writer.writerow([f'تاريخ التصدير: {format_datetime_for_export()}'])
                        writer.writerow([f'إجمالي الإيرادات: {len(revenues)}'])
                        writer.writerow([f'إجمالي المبلغ: {sum(r.amount for r in revenues):.2f} جنيه'])
                        writer.writerow([])

                    # إنشاء رؤوس الأعمدة حسب الاختيار
                    headers = []
                    if self.export_id.isChecked():
                        headers.append('الرقم التعريفي')
                    if self.export_title.isChecked():
                        headers.append('عنوان الإيراد')
                    if self.export_amount.isChecked():
                        headers.append('المبلغ')
                    if self.export_date.isChecked():
                        headers.append('التاريخ')
                    if self.export_client.isChecked():
                        headers.append('العميل')
                    if self.export_invoice_id.isChecked():
                        headers.append('رقم الفاتورة')
                    if self.export_payment_status.isChecked():
                        headers.append('حالة الدفع')
                    if self.export_category.isChecked():
                        headers.append('التصنيف')
                    if self.export_notes.isChecked():
                        headers.append('الملاحظات')
                    if self.export_created_date.isChecked():
                        headers.append('تاريخ الإنشاء')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for revenue in revenues:
                        row = []
                        if self.export_id.isChecked():
                            row.append(revenue.id)
                        if self.export_title.isChecked():
                            row.append(revenue.title or 'غير محدد')
                        if self.export_amount.isChecked():
                            row.append(f"{revenue.amount:.2f}")
                        if self.export_date.isChecked():
                            row.append(revenue.date.strftime('%Y-%m-%d') if revenue.date else 'غير محدد')
                        if self.export_client.isChecked():
                            client_name = revenue.client.name if revenue.client else 'غير محدد'
                            row.append(client_name)
                        if self.export_invoice_id.isChecked():
                            row.append(revenue.invoice_id or 'غير محدد')
                        if self.export_payment_status.isChecked():
                            row.append('مدفوع' if revenue.amount > 0 else 'غير مدفوع')
                        if self.export_category.isChecked():
                            row.append(revenue.category or 'عام')
                        if self.export_notes.isChecked():
                            row.append(revenue.notes or 'لا توجد ملاحظات')
                        if self.export_created_date.isChecked():
                            row.append(revenue.created_at.strftime('%Y-%m-%d') if revenue.created_at else 'غير محدد')

                        writer.writerow(row)

                dialog.accept()
                self.show_success_message(f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_إيرادات_{format_datetime_for_filename()}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                revenues = self.session.query(Revenue).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(revenues),
                        'backup_type': 'revenues_full_backup'
                    },
                    'revenues': []
                }

                for revenue in revenues:
                    revenue_data = {
                        'id': revenue.id,
                        'title': revenue.title,
                        'amount': float(revenue.amount) if revenue.amount else 0.0,
                        'date': revenue.date.isoformat() if revenue.date else None,
                        'client_id': revenue.client_id if revenue.client_id else None,
                        'client_name': revenue.client.name if revenue.client else None,
                        'project_id': revenue.project_id if revenue.project_id else None,
                        'property_id': revenue.property_id if revenue.property_id else None,
                        'invoice_id': revenue.invoice_id,
                        'category': revenue.category,
                        'notes': revenue.notes,
                        'created_at': revenue.created_at.isoformat() if revenue.created_at else None
                    }
                    backup_data['revenues'].append(revenue_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(revenues)} إيراد")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة
                reply = QMessageBox.question(
                    self, "تأكيد الاستعادة",
                    "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم استبدال البيانات الحالية!",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    if 'revenues' in backup_data:
                        # حذف البيانات الحالية (اختياري - يمكن تعديله)
                        # self.session.query(Revenue).delete()

                        restored_count = 0
                        for revenue_data in backup_data['revenues']:
                            # التحقق من وجود الإيراد
                            existing = self.session.query(Revenue).filter_by(id=revenue_data['id']).first()
                            if not existing:
                                # إنشاء إيراد جديد
                                new_revenue = Revenue(
                                    title=revenue_data.get('title'),
                                    amount=revenue_data.get('amount', 0),
                                    date=datetime.fromisoformat(revenue_data['date']).date() if revenue_data.get('date') else None,
                                    client_id=revenue_data.get('client_id'),
                                    project_id=revenue_data.get('project_id'),
                                    property_id=revenue_data.get('property_id'),
                                    invoice_id=revenue_data.get('invoice_id'),
                                    category=revenue_data.get('category'),
                                    notes=revenue_data.get('notes')
                                )
                                self.session.add(new_revenue)
                                restored_count += 1

                        self.session.commit()
                        self.load_revenues()  # إعادة تحميل البيانات

                        self.show_success_message(f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم استعادة {restored_count} إيراد")
                    else:
                        self.show_error_message("ملف النسخة الاحتياطية غير صالح!")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")

    def save_report_to_file(self, content, filename):
        """حفظ التقرير إلى ملف نصي"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"{filename}.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.show_success_message(f"تم حفظ التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في حفظ التقرير: {str(e)}")

    def print_report(self, text_browser):
        """طباعة التقرير"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                text_browser.print_(printer)
                self.show_success_message("تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في الطباعة: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة - مطابق تماماً للعملاء"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق تماماً للعملاء
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                },
                'purple': {
                    'bg_start': '#581c87', 'bg_mid': '#7c3aed', 'bg_end': '#8b5cf6', 'bg_bottom': '#a855f7',
                    'hover_start': '#a855f7', 'hover_mid': '#c084fc', 'hover_end': '#d8b4fe', 'hover_bottom': '#e9d5ff',
                    'hover_border': '#c084fc', 'pressed_start': '#3b0764', 'pressed_mid': '#581c87',
                    'pressed_end': '#6b21a8', 'pressed_bottom': '#7c3aed', 'pressed_border': '#6b21a8',
                    'border': '#a855f7', 'text': '#ffffff', 'shadow': 'rgba(168, 85, 247, 0.6)'
                },
                'gray': {
                    'bg_start': '#2d3748', 'bg_mid': '#4a5568', 'bg_end': '#718096', 'bg_bottom': '#a0aec0',
                    'hover_start': '#a0aec0', 'hover_mid': '#cbd5e0', 'hover_end': '#e2e8f0', 'hover_bottom': '#f7fafc',
                    'hover_border': '#e2e8f0', 'pressed_start': '#1a202c', 'pressed_mid': '#2d3748',
                    'pressed_end': '#4a5568', 'pressed_bottom': '#718096', 'pressed_border': '#4a5568',
                    'border': '#a0aec0', 'text': '#ffffff', 'shadow': 'rgba(160, 174, 192, 0.6)'
                },
                'gold': {
                    'bg_start': '#b7791f', 'bg_mid': '#d69e2e', 'bg_end': '#ecc94b', 'bg_bottom': '#ffd700',
                    'hover_start': '#ecc94b', 'hover_mid': '#f6e05e', 'hover_end': '#faf089', 'hover_bottom': '#fefcbf',
                    'hover_border': '#ffd700', 'pressed_start': '#744210', 'pressed_mid': '#b7791f',
                    'pressed_end': '#d69e2e', 'pressed_bottom': '#ecc94b', 'pressed_border': '#d69e2e',
                    'border': '#ffd700', 'text': '#ffffff', 'shadow': 'rgba(255, 215, 0, 0.9)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#2d2d2d', 'bg_bottom': '#404040',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#1a1a1a', 'pressed_bottom': '#2d2d2d', 'pressed_border': '#1a1a1a',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                },
                'warning': {
                    'bg_start': '#451a03', 'bg_mid': '#78350f', 'bg_end': '#a16207', 'bg_bottom': '#eab308',
                    'hover_start': '#78350f', 'hover_mid': '#ca8a04', 'hover_end': '#eab308', 'hover_bottom': '#facc15',
                    'hover_border': '#eab308', 'pressed_start': '#451a03', 'pressed_mid': '#78350f',
                    'pressed_end': '#a16207', 'pressed_bottom': '#ca8a04', 'pressed_border': '#a16207',
                    'border': '#eab308', 'text': '#ffffff', 'shadow': 'rgba(234, 179, 8, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق تماماً للعملاء
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار التصدير: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def export_detailed_report(self):
        """تصدير تقرير تفصيلي للإيرادات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            # الحصول على جميع الإيرادات
            revenues = self.session.query(Revenue).all()

            if not revenues:
                self.show_info_message("لا توجد إيرادات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير التفصيلي",
                f"تقرير_إيرادات_تفصيلي_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow([
                        'الرقم', 'العنوان', 'المبلغ', 'الفئة', 'التاريخ',
                        'الفاتورة', 'الملاحظات'
                    ])

                    # كتابة البيانات التفصيلية
                    total_amount = 0
                    for revenue in revenues:
                        invoice_info = f"{revenue.invoice.invoice_number}" if revenue.invoice else "لا يوجد"

                        writer.writerow([
                            revenue.id,
                            revenue.title,
                            f"{revenue.amount:.2f}",
                            revenue.category or "غير محدد",
                            revenue.date.strftime('%Y-%m-%d') if revenue.date else "",
                            invoice_info,
                            revenue.notes or ""
                        ])
                        total_amount += revenue.amount

                    # إضافة صف الإجمالي
                    writer.writerow([])
                    writer.writerow(['الإجمالي', '', f"{total_amount:.2f}", '', '', '', ''])

                self.show_success_message(f"تم تصدير التقرير التفصيلي بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التقرير التفصيلي: {str(e)}")

    def export_balance_report(self):
        """تصدير تقرير الأرصدة للإيرادات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            from sqlalchemy import func

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الأرصدة",
                f"تقرير_أرصدة_إيرادات_{format_datetime_for_filename()}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow(['نوع التقرير', 'المبلغ', 'العدد', 'النسبة المئوية'])
                    writer.writerow([])

                    # تقرير حسب الفئات
                    writer.writerow(['=== تقرير حسب الفئات ==='])
                    categories = self.session.query(
                        Revenue.category,
                        func.sum(Revenue.amount),
                        func.count(Revenue.id)
                    ).group_by(Revenue.category).all()

                    total_amount = self.session.query(func.sum(Revenue.amount)).scalar() or 0
                    total_count = self.session.query(func.count(Revenue.id)).scalar() or 0

                    for category, amount, count in categories:
                        category_name = category or "غير محدد"
                        percentage = (amount / total_amount * 100) if total_amount > 0 else 0
                        writer.writerow([category_name, f"{amount:.2f}", count, f"{percentage:.1f}%"])

                    writer.writerow(['الإجمالي', f"{total_amount:.2f}", total_count, "100.0%"])
                    writer.writerow([])

                    # تقرير حسب الشهور
                    writer.writerow(['=== تقرير حسب الشهور ==='])
                    from sqlalchemy import extract
                    monthly_data = self.session.query(
                        extract('month', Revenue.date).label('month'),
                        extract('year', Revenue.date).label('year'),
                        func.sum(Revenue.amount),
                        func.count(Revenue.id)
                    ).group_by(
                        extract('year', Revenue.date),
                        extract('month', Revenue.date)
                    ).order_by(
                        extract('year', Revenue.date),
                        extract('month', Revenue.date)
                    ).all()

                    for month, year, amount, count in monthly_data:
                        month_name = f"{int(year)}-{int(month):02d}"
                        percentage = (amount / total_amount * 100) if total_amount > 0 else 0
                        writer.writerow([month_name, f"{amount:.2f}", count, f"{percentage:.1f}%"])

                self.show_success_message(f"تم تصدير تقرير الأرصدة بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير تقرير الأرصدة: {str(e)}")


class RevenueInfoDialog(QDialog):
    """نافذة عرض تفاصيل الإيراد - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, revenue=None):
        super().__init__(parent)
        self.revenue = revenue
        self.parent_widget = parent
        self.setup_ui()

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي الموحد بدون إطار"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """

    def setup_ui(self):
        """إعداد واجهة النافذة - مطابق تماماً للنموذج المرجعي"""
        self.setWindowTitle(f"💵 {self.revenue.title if self.revenue.title else 'إيراد'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(900, 700)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(self.get_reference_styling())

        # تخطيط رئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف
        title_text = QLabel(f"💵 تفاصيل الإيراد: {self.revenue.title}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # إنشاء منطقة التمرير المحسنة للمعلومات - مطابقة تماماً للنموذج المرجعي
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
            QScrollBar:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(71, 85, 105, 0.4),
                    stop:1 rgba(100, 116, 139, 0.3));
                width: 16px;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.6),
                    stop:0.5 rgba(139, 92, 246, 0.5),
                    stop:1 rgba(34, 197, 94, 0.4));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 7px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(34, 197, 94, 0.6));
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(37, 99, 235, 0.9),
                    stop:0.5 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(21, 128, 61, 0.7));
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: transparent;
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
        """)

        # محتوى المعلومات المحسن - مطابق تماماً للنموذج المرجعي
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(15, 23, 42, 0.1),
                    stop:0.2 rgba(30, 41, 59, 0.08),
                    stop:0.5 rgba(51, 65, 85, 0.06),
                    stop:0.8 rgba(71, 85, 105, 0.08),
                    stop:1 rgba(100, 116, 139, 0.1));
                border-radius: 12px;
                padding: 10px;
            }
        """)

        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(15, 15, 15, 15)
        info_layout.setSpacing(25)

        # إضافة معلومات الإيراد
        self.add_revenue_info(info_layout)

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان
        self.customize_title_bar()

    def add_revenue_info(self, layout):
        """إضافة معلومات الإيراد إلى التخطيط - مطابق تماماً للنموذج المرجعي"""
        if not self.revenue:
            return

        # قسم المعلومات الأساسية
        basic_info = [
            ("🔢 المعرف الفريد", f"#{str(self.revenue.id).zfill(8)}"),
            ("📝 عنوان الإيراد", self.revenue.title or "غير محدد"),
            ("🏷️ الفئة", self.revenue.category or "غير محدد"),
            ("📊 حالة الإيراد", self.get_revenue_status()),
            ("📋 مستوى البيانات", self.get_data_completeness())
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم المعلومات المالية المحسن
        amount_color = self.get_amount_color()
        amount_text = f"{self.revenue.amount:.0f} جنيه" if self.revenue.amount else "0 جنيه"
        self.add_info_section(layout, "💰 المعلومات المالية", [
            ("💵 المبلغ", f"{amount_color} {amount_text}"),
            ("📊 تصنيف المبلغ", self.get_amount_category()),
            ("💳 نوع الإيراد", self.get_revenue_type()),
            ("📈 الأهمية المالية", self.get_financial_importance())
        ])

        # قسم معلومات التاريخ والتوقيت
        self.add_info_section(layout, "📅 معلومات التاريخ والتوقيت", [
            ("📅 تاريخ الإيراد", self.get_revenue_date()),
            ("⏰ وقت الإدخال", self.get_entry_time()),
            ("📊 العمر", self.get_revenue_age()),
            ("🗓️ الفترة", self.get_period_info())
        ])

        # قسم معلومات المشروع
        project_info = self.revenue.project.name if self.revenue.project else "غير مرتبط"
        project_client = self.revenue.project.client.name if self.revenue.project and self.revenue.project.client else "غير محدد"
        self.add_info_section(layout, "🏗️ معلومات المشروع", [
            ("🏗️ اسم المشروع", project_info),
            ("👤 عميل المشروع", project_client),
            ("📍 موقع المشروع", self.revenue.project.location if self.revenue.project else "غير محدد"),
            ("📊 حالة المشروع", self.revenue.project.status if self.revenue.project else "غير محدد")
        ])

        # قسم معلومات العقار
        property_info = self.revenue.property.title if self.revenue.property else "غير مرتبط"
        property_type = self.revenue.property.type if self.revenue.property else "غير محدد"
        property_location = self.revenue.property.location if self.revenue.property else "غير محدد"
        property_price = f"{int(self.revenue.property.price):,} جنيه" if self.revenue.property and self.revenue.property.price else "غير محدد"
        self.add_info_section(layout, "🏠 معلومات العقار", [
            ("🏠 اسم العقار", property_info),
            ("🏷️ نوع العقار", property_type),
            ("📍 موقع العقار", property_location),
            ("💰 سعر العقار", property_price)
        ])

        # قسم معلومات الفاتورة
        invoice_info = self.revenue.invoice.invoice_number if self.revenue.invoice else "غير مرتبط"
        self.add_info_section(layout, "📋 معلومات الفاتورة", [
            ("📋 رقم الفاتورة", invoice_info),
            ("📊 حالة الفاتورة", self.get_invoice_status()),
            ("💼 نوع الفاتورة", self.get_invoice_type()),
            ("🔗 الربط", self.get_invoice_link())
        ])

        # قسم الملاحظات والتفاصيل الإضافية
        self.add_info_section(layout, "📝 ملاحظات وتفاصيل إضافية", [
            ("📝 الملاحظات", self.revenue.notes or "لا توجد ملاحظات"),
            ("🔍 معلومات إضافية", self.get_additional_info()),
            ("📋 ملخص الإيراد", self.get_revenue_summary())
        ])

    def add_info_section(self, layout, title, items):
        """إضافة قسم معلومات بدون إطار رئيسي"""
        # حاوي القسم بدون إطار
        section_frame = QWidget()
        section_frame.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
                margin: 5px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setContentsMargins(15, 15, 15, 15)
        section_layout.setSpacing(12)

        # عنوان القسم المبسط
        section_title = QLabel(title)
        section_title.setAlignment(Qt.AlignCenter)
        section_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(139, 92, 246, 0.15),
                    stop:1 rgba(34, 197, 94, 0.1));
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
                margin-bottom: 8px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        section_layout.addWidget(section_title)

        # عناصر القسم المحسنة
        for i, (label, value) in enumerate(items):
            item_widget = QWidget()
            item_layout = QHBoxLayout(item_widget)
            item_layout.setContentsMargins(12, 8, 12, 8)
            item_layout.setSpacing(15)

            # خلفية مبسطة للعناصر
            item_widget.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(255, 255, 255, {0.02 + (i % 2) * 0.01}),
                        stop:0.5 rgba(248, 250, 252, {0.03 + (i % 2) * 0.01}),
                        stop:1 rgba(241, 245, 249, {0.02 + (i % 2) * 0.01}));
                    border: none;
                    border-radius: 4px;
                    margin: 1px 0px;
                }}
                QWidget:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.05),
                        stop:0.5 rgba(139, 92, 246, 0.04),
                        stop:1 rgba(34, 197, 94, 0.03));
                    border: none;
                }}
            """)

            # التسمية المحسنة - نصوص أوضح
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 16px;
                    font-weight: 800;
                    min-width: 180px;
                    max-width: 180px;
                    padding: 12px 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(71, 85, 105, 0.6),
                        stop:1 rgba(100, 116, 139, 0.5));
                    border: none;
                    border-radius: 4px;
                    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.7);
                }
            """)

            # القيمة المحسنة مع ألوان مخصصة
            value_widget = QLabel(str(value))

            # تحديد لون القيمة حسب المحتوى
            value_color = self.get_value_color(label, str(value))

            value_widget.setStyleSheet(f"""
                QLabel {{
                    color: {value_color};
                    font-size: 15px;
                    font-weight: 600;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.08),
                        stop:0.5 rgba(248, 250, 252, 0.12),
                        stop:1 rgba(241, 245, 249, 0.08));
                    border: none;
                    border-radius: 8px;
                    padding: 10px 15px;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
                }}
                QLabel:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.5 rgba(139, 92, 246, 0.12),
                        stop:1 rgba(34, 197, 94, 0.10));
                    border: none;
                }}
            """)
            value_widget.setWordWrap(True)

            item_layout.addWidget(label_widget)
            item_layout.addWidget(value_widget, 1)

            section_layout.addWidget(item_widget)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - ألوان أوضح وأكثر تباينًا"""
        colors = {
            'positive': '#00FF7F',      # أخضر نيون للإيجابي
            'negative': '#FF6B6B',      # أحمر نيون للسلبي
            'neutral': '#E2E8F0',       # رمادي فاتح للمحايد
            'warning': '#FFD700',       # ذهبي للتحذيرات
            'info': '#00BFFF',          # أزرق سماوي للمعلومات
            'success': '#32CD32',       # أخضر ليموني للنجاح
            'error': '#FF4500',         # برتقالي أحمر للأخطاء
            'special': '#DA70D6',       # بنفسجي نيون للمميز
            'default': '#FFFFFF'        # أبيض نقي افتراضي
        }

        # تحديد اللون حسب التسمية والقيمة
        if "مبلغ" in label.lower():
            if "🟢" in value:
                return colors['positive']
            elif "🔴" in value:
                return colors['negative']
            else:
                return colors['neutral']
        elif "حالة" in label.lower():
            if "مكتمل" in value or "✅" in value:
                return colors['success']
            elif "معلق" in value or "⏳" in value:
                return colors['warning']
            else:
                return colors['info']
        elif "تصنيف" in label.lower() or "أهمية" in label.lower():
            if "عالي" in value or "مهم" in value or "⭐" in value:
                return colors['special']
            elif "متوسط" in value:
                return colors['warning']
            else:
                return colors['neutral']
        elif "غير محدد" in value or "لا توجد" in value:
            return colors['neutral']
        else:
            return colors['default']

    def get_amount_color(self):
        """تحديد لون المبلغ"""
        if not self.revenue.amount or self.revenue.amount == 0:
            return "🟡"  # أصفر للصفر
        elif self.revenue.amount > 1000:
            return "🟢"  # أخضر للمبلغ الكبير (إيراد جيد)
        else:
            return "🔵"  # أزرق للمبلغ العادي

    def get_revenue_status(self):
        """حالة الإيراد"""
        if self.revenue.amount and self.revenue.amount > 0:
            return "مكتمل ✅"
        else:
            return "معلق ⏳"

    def get_data_completeness(self):
        """مستوى اكتمال البيانات"""
        fields = [self.revenue.title, self.revenue.category, self.revenue.notes,
                 str(self.revenue.amount) if self.revenue.amount else None]
        filled = sum(1 for field in fields if field and str(field).strip())
        percentage = (filled / len(fields)) * 100
        if percentage == 100:
            return f"مكتمل {percentage:.0f}% ✅"
        elif percentage >= 75:
            return f"جيد {percentage:.0f}% 🟢"
        elif percentage >= 50:
            return f"متوسط {percentage:.0f}% 🟡"
        else:
            return f"ناقص {percentage:.0f}% 🔴"

    def get_amount_category(self):
        """تصنيف المبلغ"""
        amount = self.revenue.amount if self.revenue.amount else 0
        if amount > 10000:
            return "إيراد ممتاز 🌟"
        elif amount > 5000:
            return "إيراد كبير 🟢"
        elif amount > 1000:
            return "إيراد متوسط 🟡"
        elif amount > 0:
            return "إيراد صغير 🔵"
        else:
            return "غير محدد ⚪"

    def get_revenue_type(self):
        """نوع الإيراد"""
        category = self.revenue.category if self.revenue.category else ""
        if "مبيعات" in category.lower():
            return "إيراد مبيعات 💳"
        elif "خدمات" in category.lower():
            return "إيراد خدمات 🔧"
        elif "استشارات" in category.lower():
            return "إيراد استشارات 💼"
        else:
            return "إيراد عام 📊"

    def get_financial_importance(self):
        """الأهمية المالية"""
        amount = self.revenue.amount if self.revenue.amount else 0
        if amount > 20000:
            return "أهمية عالية جداً ⭐⭐⭐"
        elif amount > 10000:
            return "أهمية عالية ⭐⭐"
        elif amount > 5000:
            return "أهمية متوسطة ⭐"
        else:
            return "أهمية منخفضة 📊"

    def get_revenue_date(self):
        """تاريخ الإيراد"""
        if self.revenue.date:
            try:
                # التعامل مع datetime أو date
                if hasattr(self.revenue.date, 'strftime'):
                    return self.revenue.date.strftime('%Y-%m-%d')
                else:
                    return str(self.revenue.date)
            except:
                return "غير محدد"
        else:
            return "غير محدد"

    def get_entry_time(self):
        """وقت الإدخال"""
        if hasattr(self.revenue, 'created_at') and self.revenue.created_at:
            return self.revenue.created_at.strftime('%H:%M:%S')
        else:
            return "غير محدد"

    def get_revenue_age(self):
        """عمر الإيراد"""
        if self.revenue.date:
            try:
                # التأكد من أن التاريخ من نوع date وليس datetime
                revenue_date = self.revenue.date.date() if hasattr(self.revenue.date, 'date') else self.revenue.date
                days = (datetime.datetime.now().date() - revenue_date).days
                if days == 0:
                    return "اليوم"
                elif days == 1:
                    return "أمس"
                elif days < 30:
                    return f"{days} يوم"
                elif days < 365:
                    months = days // 30
                    return f"{months} شهر"
                else:
                    years = days // 365
                    return f"{years} سنة"
            except:
                return "غير محدد"
        return "غير محدد"

    def get_period_info(self):
        """معلومات الفترة"""
        if self.revenue.date:
            try:
                # التعامل مع datetime أو date
                if hasattr(self.revenue.date, 'strftime'):
                    month_name = self.revenue.date.strftime('%B')
                    year = self.revenue.date.year
                    return f"{month_name} {year}"
                else:
                    return str(self.revenue.date)
            except:
                return "غير محدد"
        return "غير محدد"

    def get_invoice_status(self):
        """حالة الفاتورة"""
        if self.revenue.invoice:
            return "مرتبط بفاتورة ✅"
        else:
            return "غير مرتبط ❌"

    def get_invoice_type(self):
        """نوع الفاتورة"""
        if self.revenue.invoice:
            return "فاتورة مبيعات 📋"
        else:
            return "غير محدد"

    def get_invoice_link(self):
        """الربط مع الفاتورة"""
        if self.revenue.invoice:
            return f"مرتبط برقم {self.revenue.invoice.invoice_number}"
        else:
            return "غير مرتبط"

    def get_additional_info(self):
        """معلومات إضافية"""
        info_parts = []
        if self.revenue.date:
            try:
                # التأكد من أن التاريخ من نوع date وليس datetime
                revenue_date = self.revenue.date.date() if hasattr(self.revenue.date, 'date') else self.revenue.date
                days_ago = (datetime.datetime.now().date() - revenue_date).days
                info_parts.append(f"مضى {days_ago} يوم على الإيراد")
            except:
                pass

        if self.revenue.amount and self.revenue.amount > 5000:
            info_parts.append(f"مبلغ كبير: {self.revenue.amount:.0f} جنيه")

        return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

    def get_revenue_summary(self):
        """ملخص الإيراد"""
        amount = self.revenue.amount if self.revenue.amount else 0
        category = self.revenue.category if self.revenue.category else "عام"
        invoice_info = f"من الفاتورة {self.revenue.invoice.invoice_number}" if self.revenue.invoice else "غير مرتبط بفاتورة"
        return f"إيراد {category} بمبلغ {amount:.0f} جنيه {invoice_info}"

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم المحسنة - مطابق تماماً للنموذج المرجعي"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.5 rgba(248, 250, 252, 0.12),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 10px;
                margin: 5px 0;
                min-height: 65px;
                max-height: 70px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(10)

        # زر الإغلاق - في المقدمة
        close_btn = QPushButton("❌ إغلاق النافذة")
        close_btn.setMinimumWidth(200)
        close_btn.setMaximumHeight(45)
        self.style_advanced_button(close_btn, 'danger')
        close_btn.clicked.connect(self.close)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة التفاصيل")
        print_btn.setMinimumWidth(200)
        print_btn.setMaximumHeight(45)
        self.style_advanced_button(print_btn, 'emerald')
        print_btn.clicked.connect(self.print_info)

        # زر تصدير PDF
        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setMinimumWidth(200)
        export_pdf_btn.setMaximumHeight(45)
        self.style_advanced_button(export_pdf_btn, 'info')
        export_pdf_btn.clicked.connect(self.export_to_pdf)

        # زر إضافة ملاحظة
        note_btn = QPushButton("📝 إضافة ملاحظة")
        note_btn.setMinimumWidth(200)
        note_btn.setMaximumHeight(45)
        self.style_advanced_button(note_btn, 'orange')
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار
        buttons_layout.addWidget(close_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_pdf_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار - مطابق للنموذج المرجعي"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'emerald': ('#10b981', '#34d399'),
                    'danger': ('#ef4444', '#f87171'),
                    'info': ('#3b82f6', '#60a5fa'),
                    'orange': ('#f97316', '#fb923c')
                }

                color_pair = colors.get(button_type, ('#6B7280', '#9CA3AF'))

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[1]});
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        min-height: 20px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {color_pair[0]}, stop:1 {color_pair[0]});
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار المعلومات: {e}")
            # المتابعة بدون تصميم متقدم للأزرار

    def print_info(self):
        """طباعة معلومات الإيراد"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                title_font = QFont("Arial", 16, QFont.Bold)
                header_font = QFont("Arial", 14, QFont.Bold)
                normal_font = QFont("Arial", 12)

                y = 100

                # العنوان الرئيسي
                painter.setFont(title_font)
                painter.drawText(100, y, f"تقرير تفصيلي للإيراد: {self.revenue.title}")
                y += 80

                # المعلومات الأساسية
                painter.setFont(header_font)
                painter.drawText(100, y, "المعلومات الأساسية:")
                y += 40

                painter.setFont(normal_font)
                basic_info = [
                    f"المعرف: #{str(self.revenue.id).zfill(6)}",
                    f"العنوان: {self.revenue.title}",
                    f"المبلغ: {self.revenue.amount or 0} جنيه",
                    f"الفئة: {self.revenue.category or 'غير محدد'}",
                    f"التاريخ: {self.revenue.date.strftime('%Y-%m-%d') if self.revenue.date else 'غير محدد'}",
                    f"الفاتورة: {self.revenue.invoice.invoice_number if self.revenue.invoice else 'غير مرتبط'}"
                ]

                for line in basic_info:
                    painter.drawText(120, y, line)
                    y += 30

                painter.end()

        except Exception as e:
            show_revenue_advanced_error(self, "خطأ في الطباعة", f"فشل في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات الإيراد إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont, QColor, QPen
            from PyQt5.QtCore import QRect

            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير معلومات الإيراد إلى PDF",
                f"revenue_{self.revenue.title}_{format_datetime_for_filename()}.pdf",
                "PDF Files (*.pdf)"
            )

            if not filename:
                return

            printer = QPrinter()
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(filename)
            printer.setPageSize(QPrinter.A4)

            painter = QPainter()
            painter.begin(printer)

            title_font = QFont("Arial", 18, QFont.Bold)
            subtitle_font = QFont("Arial", 14, QFont.Bold)
            content_font = QFont("Arial", 12)

            page_rect = printer.pageRect()
            margin = 50
            content_rect = QRect(margin, margin,
                               page_rect.width() - 2*margin,
                               page_rect.height() - 2*margin)

            y_pos = content_rect.top()

            # رسم العنوان الرئيسي
            painter.setFont(title_font)
            painter.setPen(QColor(0, 0, 0))
            title_text = f"💵 معلومات الإيراد: {self.revenue.title}"
            painter.drawText(content_rect.left(), y_pos, title_text)
            y_pos += 70

            # رسم خط فاصل
            painter.setPen(QPen(QColor(100, 100, 100), 2))
            painter.drawLine(content_rect.left(), y_pos, content_rect.right(), y_pos)
            y_pos += 50

            # المعلومات الأساسية
            painter.setFont(subtitle_font)
            painter.setPen(QColor(0, 0, 0))
            painter.drawText(content_rect.left(), y_pos, "المعلومات الأساسية:")
            y_pos += 40

            painter.setFont(content_font)
            painter.setPen(QColor(50, 50, 50))

            basic_info = [
                f"معرف الإيراد: {self.revenue.id}",
                f"العنوان: {self.revenue.title}",
                f"المبلغ: {self.revenue.amount or 0} جنيه",
                f"الفئة: {self.revenue.category or 'غير محدد'}",
                f"التاريخ: {self.revenue.date.strftime('%Y-%m-%d') if self.revenue.date else 'غير محدد'}",
                f"الفاتورة: {self.revenue.invoice.invoice_number if self.revenue.invoice else 'غير مرتبط'}"
            ]

            for line in basic_info:
                painter.drawText(content_rect.left() + 20, y_pos, line)
                y_pos += 30

            painter.end()

            show_revenue_advanced_info(self, "تم التصدير", f"تم تصدير معلومات الإيراد إلى:\n{filename}", "✅")

        except Exception as e:
            show_revenue_advanced_error(self, "خطأ في التصدير", f"فشل في التصدير: {str(e)}")

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة"""
        try:
            dialog = AddRevenueNoteDialog(self, self.revenue, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_revenue_info()
        except Exception as e:
            show_revenue_advanced_error(self, "خطأ في الملاحظات", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_revenue_info(self):
        """تحديث معلومات الإيراد"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                updated_revenue = self.parent_widget.session.query(Revenue).get(self.revenue.id)
                if updated_revenue:
                    self.revenue = updated_revenue
                    self.setup_ui()
        except Exception as e:
            print(f"تحذير: فشل في تحديث معلومات الإيراد: {e}")
            # المتابعة بدون تحديث المعلومات

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)




class AddRevenueNoteDialog(QDialog):
    """نافذة ملاحظات الإيراد - مطابقة تماماً للنموذج المرجعي"""

    def __init__(self, parent=None, revenue=None, parent_widget=None):
        super().__init__(parent)
        self.revenue = revenue
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً - مطابق للنموذج المرجعي"""
        self.setWindowTitle(f"📝 {self.revenue.title if self.revenue.title else 'إيراد'}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(RevenueInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # شريط العنوان الداخلي مطابق للنموذج المرجعي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.25),
                    stop:0.2 rgba(59, 130, 246, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.28),
                    stop:0.8 rgba(168, 85, 247, 0.25),
                    stop:1 rgba(236, 72, 153, 0.2));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                margin: 1px 0px 5px 0px;
                padding: 2px;
                max-height: 50px;
                min-height: 45px;
            }
        """)

        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(8, 5, 8, 11)
        title_layout.setSpacing(10)

        # نص العنوان في المنتصف - مكبر ومرفوع
        title_text = QLabel(f"📝 ملاحظة للإيراد: {self.revenue.title}")
        title_text.setAlignment(Qt.AlignCenter)
        title_text.setStyleSheet("""
            QLabel {
                color: #FFFFFF;
                font-size: 20px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                background: transparent;
                border: none;
                padding: 4px 15px;
                margin: 0px;
            }
        """)

        # وضع النص في المنتصف تماماً
        title_layout.addStretch()
        title_layout.addWidget(title_text)
        title_layout.addStretch()
        layout.addWidget(title_frame)

        # محرر النصوص المتطور مطابق للنموذج المرجعي
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(248, 250, 252, 0.1));
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', Arial, sans-serif;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                selection-background-color: rgba(59, 130, 246, 0.5);
                line-height: 1.4;
            }
            QTextEdit:focus {
                border: 2px solid rgba(255, 215, 0, 0.6);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:1 rgba(248, 250, 252, 0.15));
            }
        """)
        self.text_editor.setPlaceholderText("اكتب ملاحظاتك هنا...\n\nيمكنك كتابة معلومات مفصلة عن الإيراد، تذكيرات، أو أي ملاحظات مهمة.")
        self.text_editor.setMinimumHeight(180)
        layout.addWidget(self.text_editor)

        # أزرار بسيطة
        self.create_buttons(layout)

        # تحميل النص
        self.load_note()

    def create_buttons(self, layout):
        """أزرار متطورة مطابقة للنموذج المرجعي"""
        # إطار الأزرار مطابق للنموذج المرجعي
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(241, 245, 249, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
                margin: 5px 0px;
                max-height: 80px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(10, 10, 10, 10)
        buttons_layout.setSpacing(20)

        # زر الإلغاء أولاً
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumWidth(140)
        self.apply_reference_button_style(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        # زر الحفظ ثانياً
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumWidth(140)
        self.apply_reference_button_style(save_btn, 'success')
        save_btn.clicked.connect(self.save_note)

        # وضع الأزرار في المنتصف
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def apply_reference_button_style(self, button, button_type):
        """تطبيق تصميم الأزرار المرجعي المتطور"""
        if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
            self.parent_widget.style_advanced_button(button, button_type)
        else:
            colors = {
                'success': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'border': '#10b981', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'border': '#dc2626', 'shadow': 'rgba(220, 38, 38, 0.6)'
                }
            }

            color_set = colors.get(button_type, colors['success'])

            button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['bg_start']},
                        stop:0.15 {color_set['bg_mid']},
                        stop:0.85 {color_set['bg_end']},
                        stop:1 {color_set['bg_bottom']});
                    color: #ffffff;
                    border: 4px solid {color_set['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.2);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_set['hover_start']},
                        stop:0.15 {color_set['hover_mid']},
                        stop:0.85 {color_set['hover_end']},
                        stop:1 {color_set['hover_bottom']});
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_set['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3);
                }}
                QPushButton:pressed {{
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_set['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.2),
                               inset 0 -1px 0 rgba(255, 255, 255, 0.2);
                }}
            """)

    def load_note(self):
        """تحميل الملاحظة الحالية"""
        if self.revenue and self.revenue.notes:
            self.text_editor.setPlainText(self.revenue.notes)

    def save_note(self):
        """حفظ الملاحظة - مطابق للنموذج المرجعي"""
        try:
            note = self.text_editor.toPlainText().strip()
            self.revenue.notes = note if note else None

            if self.parent_widget and self.parent_widget.session:
                self.parent_widget.session.commit()
                if hasattr(self.parent_widget, 'refresh_data'):
                    self.parent_widget.refresh_data()

            self.accept()

            show_revenue_advanced_info(self, "تم الحفظ", "حُفظت الملاحظة", "✅")

        except Exception as e:
            show_revenue_advanced_error(self, "خطأ في الحفظ", f"فشل الحفظ: {str(e)}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)


class RevenuesStatisticsDialog(QDialog):
    """نافذة إحصائيات الإيرادات مطابقة للعملاء والموردين والعمال والمشاريع والعقارات والمخزون والمبيعات والمشتريات والمصروفات"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة - مطابقة للعملاء"""
        # إعداد النافذة الأساسي
        self.setWindowTitle("📊 إحصائيات الإيرادات - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(520, 480)  # حجم أكبر قليلاً لاستيعاب الإحصائيات الجديدة

        # تخصيص شريط العنوان
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)  # هوامش مضغوطة جداً
        layout.setSpacing(6)  # مسافات مضغوطة جداً

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 4px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(3)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات الإيرادات")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 22px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                padding: 4px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة الإيرادات والدخل")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 13px;
                font-weight: normal;
                font-family: 'Segoe UI', 'Tahoma', sans-serif;
                background: transparent;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                padding: 2px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(3)  # مسافات مضغوطة جداً
        stats_layout.setContentsMargins(8, 4, 8, 4)  # هوامش مضغوطة جداً

        # إنشاء قائمة الإحصائيات المحدثة والمتطورة
        stats_items = [
            ("💰", "إجمالي الإيرادات المسجلة", str(self.total_revenues), "#3B82F6", "📊"),
            ("✅", "الإيرادات المحصلة والمكتملة", str(self.collected_revenues), "#10B981", "💚"),
            ("⏳", "الإيرادات المعلقة والمؤجلة", str(self.pending_revenues), "#F59E0B", "🟡"),
            ("❌", "الإيرادات الملغية أو المرفوضة", str(self.cancelled_revenues), "#EF4444", "🔴"),
            ("💵", "إجمالي قيمة الإيرادات", format_currency(self.total_amount), "#10B981", "⬆️"),
            ("🏆", "أفضل مصادر الإيرادات", str(getattr(self, 'top_revenue_sources', 0)), "#059669", "💎"),
            ("💎", "أعلى الإيرادات قيمة", f"{getattr(self, 'highest_revenue', 0):,.0f} جنيه", "#DC2626", "⚡"),
            ("💵", "أقل الإيرادات قيمة", f"{getattr(self, 'lowest_revenue', 0):,.0f} جنيه", "#6366F1", "🔻"),
            ("📈", "إيرادات هذا الشهر", f"{getattr(self, 'current_month_revenues', 0):,.0f} جنيه", "#8B5CF6", "📂"),
            ("🔄", "الإيرادات المتكررة الثابتة", str(getattr(self, 'recurring_revenues', 0)), "#F59E0B", "🔁")
        ]

        for icon, title, value, color, secondary_icon in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 1px;
                    margin: 0px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 3px;
                }
            """)
            item_widget.setFixedHeight(28)  # ارتفاع مضغوط جداً

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(6)
            item_layout.setContentsMargins(6, 2, 6, 2)

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(20, 20)  # حجم أصغر جداً
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet(f"""
                QLabel {{
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px 3px;
                }}
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 13px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px 6px;
                    min-width: 50px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            # الأيقونة الثانوية
            secondary_icon_label = QLabel(secondary_icon)
            secondary_icon_label.setFixedSize(16, 16)
            secondary_icon_label.setAlignment(Qt.AlignCenter)
            secondary_icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 12px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Tahoma', sans-serif;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    background: transparent;
                    border: none;
                    padding: 1px;
                }}
            """)
            item_layout.addWidget(secondary_icon_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(8, 4, 8, 4)

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        self.style_advanced_button(close_button, 'danger')

        # زر تصدير
        export_pdf_button = QPushButton("📄 تصدير")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        self.style_advanced_button(export_pdf_button, 'info')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب إحصائيات الإيرادات"""
        try:
            # حساب إجمالي الإيرادات
            self.total_revenues = self.session.query(Revenue).count()

            # حساب الإيرادات حسب الفئة (بدلاً من الحالة)
            self.collected_revenues = self.session.query(Revenue).filter(Revenue.category == 'محصل').count()
            self.pending_revenues = self.session.query(Revenue).filter(Revenue.category == 'معلق').count()
            self.cancelled_revenues = self.session.query(Revenue).filter(Revenue.category.in_(['ملغي', 'مرفوض'])).count()

            # حساب إجمالي المبلغ
            total_amount_result = self.session.query(func.sum(Revenue.amount)).scalar()
            self.total_amount = total_amount_result or 0

            # حساب الإحصائيات المتطورة الجديدة
            self.calculate_advanced_revenue_statistics()

        except Exception as e:
            self.total_revenues = 0
            self.collected_revenues = 0
            self.pending_revenues = 0
            self.cancelled_revenues = 0
            self.total_amount = 0
            # قيم افتراضية للإحصائيات الجديدة
            self.top_revenue_sources = 0
            self.highest_revenue = 0
            self.lowest_revenue = 0
            self.current_month_revenues = 0
            self.recurring_revenues = 0

    def calculate_advanced_revenue_statistics(self):
        """حساب الإحصائيات المتطورة للإيرادات"""
        try:
            from datetime import datetime, timedelta
            from collections import Counter

            # جلب جميع الإيرادات
            revenues = self.session.query(Revenue).all()

            # 1. حساب أفضل مصادر الإيرادات
            source_amounts = {}
            for revenue in revenues:
                source = getattr(revenue, 'source', None) or getattr(revenue, 'category', None) or 'غير محدد'
                if source not in source_amounts:
                    source_amounts[source] = 0
                source_amounts[source] += revenue.amount or 0

            # أفضل المصادر (التي تتجاوز 50000 جنيه)
            self.top_revenue_sources = len([source for source, amount in source_amounts.items() if amount > 50000])

            # 2. حساب أعلى وأقل الإيرادات قيمة
            amounts = [r.amount for r in revenues if r.amount and r.amount > 0]
            if amounts:
                self.highest_revenue = max(amounts)
                self.lowest_revenue = min(amounts)
            else:
                self.highest_revenue = 0
                self.lowest_revenue = 0

            # 3. حساب إيرادات هذا الشهر
            current_date = datetime.now()
            start_of_month = current_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            current_month_revenues = [
                r for r in revenues
                if hasattr(r, 'revenue_date') and r.revenue_date and r.revenue_date >= start_of_month.date()
            ]

            self.current_month_revenues = sum(r.amount for r in current_month_revenues if r.amount)

            # 4. حساب الإيرادات المتكررة (الثابتة)
            # نفترض أن الإيراد متكرر إذا كان له نفس المصدر أو الوصف في أشهر متعددة
            recurring_count = 0
            revenue_sources = Counter()
            for revenue in revenues:
                source = getattr(revenue, 'source', None) or getattr(revenue, 'description', None)
                if source:
                    revenue_sources[source] += 1

            # الإيرادات التي تكررت 3 مرات أو أكثر تعتبر متكررة
            self.recurring_revenues = len([source for source, count in revenue_sources.items() if count >= 3])

        except Exception as e:
            print(f"خطأ في حساب الإحصائيات المتطورة للإيرادات: {e}")
            # قيم افتراضية في حالة الخطأ
            self.top_revenue_sources = 0
            self.highest_revenue = 0
            self.lowest_revenue = 0
            self.current_month_revenues = 0
            self.recurring_revenues = 0

    def export_statistics_to_pdf(self):
        """تصدير إحصائيات الإيرادات إلى ملف PDF باللغة العربية"""
        try:
            # التأكد من حساب الإحصائيات أولاً
            self.calculate_statistics()

            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            from PyQt5.QtWidgets import QFileDialog
            import os

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير إحصائيات الإيرادات",
                f"تقرير_إحصائيات_الإيرادات_{self.format_datetime_for_filename()}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return

            # دالة لإصلاح النص العربي
            def fix_arabic_text(text):
                try:
                    if isinstance(text, str):
                        import arabic_reshaper
                        from bidi.algorithm import get_display
                        reshaped_text = arabic_reshaper.reshape(text)
                        bidi_text = get_display(reshaped_text)
                        return bidi_text
                    return text
                except ImportError:
                    return text
                except Exception:
                    return text

            # إنشاء المستند
            doc = SimpleDocTemplate(
                file_path,
                pagesize=letter,
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=20,
                title="تقرير إحصائيات الإيرادات"
            )

            story = []
            styles = getSampleStyleSheet()

            # تسجيل خط عربي
            try:
                arabic_font_path = "C:/Windows/Fonts/arial.ttf"
                if os.path.exists(arabic_font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'

            # العنوان الرئيسي
            title_style = ParagraphStyle(
                'ArabicTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=20,
                alignment=1,
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("💰 تقرير إحصائيات الإيرادات"), title_style))
            story.append(Spacer(1, 8))

            # معلومات التقرير
            info_style = ParagraphStyle(
                'ArabicInfo',
                parent=styles['Normal'],
                fontSize=10,
                alignment=1,
                textColor=colors.grey,
                fontName=font_name,
                spaceAfter=5
            )

            story.append(Paragraph(fix_arabic_text(f"تاريخ التقرير: {self.format_datetime_for_export()}"), info_style))
            story.append(Spacer(1, 12))

            # عنوان قسم الإحصائيات
            section_style = ParagraphStyle(
                'SectionTitle',
                parent=styles['Heading2'],
                fontSize=13,
                spaceAfter=8,
                alignment=2,
                textColor=colors.darkblue,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("📈 الإحصائيات الأساسية"), section_style))
            story.append(Spacer(1, 5))

            # إنشاء جدول الإحصائيات
            data = [
                [fix_arabic_text('البيان'), fix_arabic_text('القيمة'), fix_arabic_text('الوصف')],
                [fix_arabic_text('💰 إجمالي الإيرادات'), str(self.total_revenues), fix_arabic_text('العدد الكلي للإيرادات')],
                [fix_arabic_text('✅ الإيرادات المحصلة'), str(self.collected_revenues), fix_arabic_text('الإيرادات المحصلة والمكتملة')],
                [fix_arabic_text('⏳ الإيرادات المعلقة'), str(self.pending_revenues), fix_arabic_text('الإيرادات المعلقة والمؤجلة')],
                [fix_arabic_text('❌ الإيرادات الملغية'), str(self.cancelled_revenues), fix_arabic_text('الإيرادات الملغية أو المرفوضة')],
                [fix_arabic_text('💵 إجمالي القيمة'), f"{self.total_amount:,.0f} جنيه", fix_arabic_text('إجمالي قيمة الإيرادات')],
                [fix_arabic_text('🏆 أفضل المصادر'), str(getattr(self, 'top_revenue_sources', 0)), fix_arabic_text('أفضل مصادر الإيرادات')],
                [fix_arabic_text('💎 أعلى الإيرادات'), f"{getattr(self, 'highest_revenue', 0):,.0f} جنيه", fix_arabic_text('أعلى الإيرادات قيمة')],
                [fix_arabic_text('💵 أقل الإيرادات'), f"{getattr(self, 'lowest_revenue', 0):,.0f} جنيه", fix_arabic_text('أقل الإيرادات قيمة')],
                [fix_arabic_text('📈 إيرادات هذا الشهر'), f"{getattr(self, 'current_month_revenues', 0):,.0f} جنيه", fix_arabic_text('إيرادات الشهر الحالي')],
                [fix_arabic_text('🔄 الإيرادات المتكررة'), str(getattr(self, 'recurring_revenues', 0)), fix_arabic_text('الإيرادات المتكررة الثابتة')]
            ]

            table = Table(data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, 0), (-1, -1), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BACKGROUND', (0, 1), (-1, 1), colors.lightgrey),
                ('BACKGROUND', (0, 3), (-1, 3), colors.lightgrey),
                ('BACKGROUND', (0, 5), (-1, 5), colors.lightgrey),
                ('BACKGROUND', (0, 7), (-1, 7), colors.lightgrey),
                ('BACKGROUND', (0, 9), (-1, 9), colors.lightgrey),
                ('BACKGROUND', (0, 11), (-1, 11), colors.lightgrey),
            ]))

            story.append(table)
            story.append(Spacer(1, 20))

            # ملاحظة
            note_style = ParagraphStyle(
                'Note',
                parent=styles['Normal'],
                fontSize=8,
                alignment=1,
                textColor=colors.grey,
                fontName=font_name
            )

            story.append(Paragraph(fix_arabic_text("تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة الإيرادات"), note_style))

            # بناء المستند
            doc.build(story)

            # إظهار رسالة نجاح
            if hasattr(self.parent_widget, 'show_success_message'):
                self.parent_widget.show_success_message(f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            print(f"خطأ في تصدير التقرير: {e}")
            if hasattr(self.parent_widget, 'show_error_message'):
                self.parent_widget.show_error_message(f"فشل في تصدير التقرير: {str(e)}")
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير التقرير: {str(e)}")

    def format_datetime_for_filename(self):
        """تنسيق التاريخ والوقت لاسم الملف"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    def format_datetime_for_export(self):
        """تنسيق التاريخ والوقت للتصدير"""
        from datetime import datetime
        return datetime.now().strftime("%Y/%m/%d %H:%M:%S")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم أزرار الإحصائيات: {e}")
            # المتابعة بدون تصميم متقدم للأزرار



    def show_info_message(self, message):
        """عرض رسالة معلومات"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "معلومات", message)
        except Exception as e:
            print(f"خطأ في عرض رسالة المعلومات: {e}")

    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", message)
        except Exception as e:
            print(f"خطأ في عرض رسالة الخطأ: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان - استخدام الدالة المشتركة"""
        apply_revenue_title_bar_styling(self)


